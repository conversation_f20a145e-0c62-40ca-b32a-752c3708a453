<template>
  <view class="settings-container">
    <!-- 设置头部 -->
    <view class="settings-header">
      <text class="header-title">设置</text>
      <view class="header-actions">
        <text class="action-btn" @tap="resetAllSettings">重置</text>
        <text class="action-btn save" @tap="saveAllSettings">保存</text>
      </view>
    </view>

    <!-- 设置分类 -->
    <scroll-view class="settings-content" scroll-y>
      <!-- 辐射监测设置 -->
      <view class="settings-section">
        <view class="section-header">
          <text class="section-icon">☢️</text>
          <text class="section-title">辐射监测设置</text>
        </view>
        
        <view class="setting-items">
          <view class="setting-item">
            <view class="item-info">
              <text class="item-title">剂量率报警阈值</text>
              <text class="item-desc">超过此值将触发高剂量率报警</text>
            </view>
            <view class="item-control">
              <input 
                class="threshold-input" 
                type="digit" 
                v-model="settings.radiation.maxDoseRate"
                placeholder="1.0" />
              <text class="threshold-unit">μSv/h</text>
            </view>
          </view>
          
          <view class="setting-item">
            <view class="item-info">
              <text class="item-title">最低剂量率阈值</text>
              <text class="item-desc">低于此值将触发低剂量率警告</text>
            </view>
            <view class="item-control">
              <input 
                class="threshold-input" 
                type="digit" 
                v-model="settings.radiation.minDoseRate"
                placeholder="0.01" />
              <text class="threshold-unit">μSv/h</text>
            </view>
          </view>
          
          <view class="setting-item">
            <view class="item-info">
              <text class="item-title">累积剂量报警阈值</text>
              <text class="item-desc">累积剂量超过此值将触发报警</text>
            </view>
            <view class="item-control">
              <input 
                class="threshold-input" 
                type="digit" 
                v-model="settings.radiation.maxDoseSum"
                placeholder="100.0" />
              <text class="threshold-unit">μSv</text>
            </view>
          </view>
          
          <view class="setting-item toggle-item">
            <view class="item-info">
              <text class="item-title">自动数据上传</text>
              <text class="item-desc">自动将监测数据上传到服务器</text>
            </view>
            <view class="item-control">
              <switch 
                :checked="settings.radiation.autoUpload" 
                @change="toggleAutoUpload" 
                color="#3cc51f" />
            </view>
          </view>
          
          <view class="setting-item" v-if="settings.radiation.autoUpload">
            <view class="item-info">
              <text class="item-title">上传间隔</text>
              <text class="item-desc">自动上传数据的时间间隔</text>
            </view>
            <view class="item-control">
              <picker 
                mode="selector"
                :range="uploadIntervals"
                :value="uploadIntervalIndex"
                @change="onUploadIntervalChange">
                <view class="picker-display">{{ uploadIntervals[uploadIntervalIndex] }}</view>
              </picker>
            </view>
          </view>
        </view>
      </view>

      <!-- MQTT服务器设置 -->
      <view class="settings-section">
        <view class="section-header">
          <text class="section-icon">📡</text>
          <text class="section-title">MQTT服务器设置</text>
        </view>
        
        <view class="setting-items">
          <view class="setting-item">
            <view class="item-info">
              <text class="item-title">服务器地址</text>
              <text class="item-desc">MQTT代理服务器的地址</text>
            </view>
            <view class="item-control">
              <input 
                class="text-input" 
                type="text" 
                v-model="settings.mqtt.host"
                placeholder="broker.emqx.io" />
            </view>
          </view>
          
          <view class="setting-item">
            <view class="item-info">
              <text class="item-title">端口号</text>
              <text class="item-desc">MQTT服务器端口</text>
            </view>
            <view class="item-control">
              <input 
                class="text-input" 
                type="number" 
                v-model="settings.mqtt.port"
                placeholder="8083" />
            </view>
          </view>
          
          <view class="setting-item">
            <view class="item-info">
              <text class="item-title">用户名</text>
              <text class="item-desc">MQTT连接用户名（可选）</text>
            </view>
            <view class="item-control">
              <input 
                class="text-input" 
                type="text" 
                v-model="settings.mqtt.username"
                placeholder="用户名" />
            </view>
          </view>
          
          <view class="setting-item">
            <view class="item-info">
              <text class="item-title">密码</text>
              <text class="item-desc">MQTT连接密码（可选）</text>
            </view>
            <view class="item-control">
              <input 
                class="text-input" 
                type="password" 
                v-model="settings.mqtt.password"
                placeholder="密码" />
            </view>
          </view>
          
          <view class="setting-item action-item">
            <view class="item-info">
              <text class="item-title">连接测试</text>
              <text class="item-desc">测试MQTT服务器连接</text>
            </view>
            <view class="item-control">
              <text class="test-btn" @tap="testMqttConnection">
                {{ mqttTestStatus }}
              </text>
            </view>
          </view>
        </view>
      </view>

      <!-- 通知和提醒设置 -->
      <view class="settings-section">
        <view class="section-header">
          <text class="section-icon">🔔</text>
          <text class="section-title">通知和提醒</text>
        </view>
        
        <view class="setting-items">
          <view class="setting-item toggle-item">
            <view class="item-info">
              <text class="item-title">声音报警</text>
              <text class="item-desc">触发报警时播放声音</text>
            </view>
            <view class="item-control">
              <switch 
                :checked="settings.notifications.soundAlert" 
                @change="toggleSoundAlert" 
                color="#3cc51f" />
            </view>
          </view>
          
          <view class="setting-item toggle-item">
            <view class="item-info">
              <text class="item-title">震动报警</text>
              <text class="item-desc">触发报警时设备震动</text>
            </view>
            <view class="item-control">
              <switch 
                :checked="settings.notifications.vibrationAlert" 
                @change="toggleVibrationAlert" 
                color="#3cc51f" />
            </view>
          </view>
          
          <view class="setting-item toggle-item">
            <view class="item-info">
              <text class="item-title">健康提醒</text>
              <text class="item-desc">定时提醒健康监测</text>
            </view>
            <view class="item-control">
              <switch 
                :checked="settings.notifications.healthReminder" 
                @change="toggleHealthReminder" 
                color="#3cc51f" />
            </view>
          </view>
          
          <view class="setting-item" v-if="settings.notifications.healthReminder">
            <view class="item-info">
              <text class="item-title">提醒间隔</text>
              <text class="item-desc">健康提醒的时间间隔</text>
            </view>
            <view class="item-control">
              <picker 
                mode="selector"
                :range="reminderIntervals"
                :value="reminderIntervalIndex"
                @change="onReminderIntervalChange">
                <view class="picker-display">{{ reminderIntervals[reminderIntervalIndex] }}</view>
              </picker>
            </view>
          </view>
          
          <view class="setting-item toggle-item">
            <view class="item-info">
              <text class="item-title">数据异常通知</text>
              <text class="item-desc">数据异常时发送通知</text>
            </view>
            <view class="item-control">
              <switch 
                :checked="settings.notifications.dataAnomalyAlert" 
                @change="toggleDataAnomalyAlert" 
                color="#3cc51f" />
            </view>
          </view>
        </view>
      </view>

      <!-- 显示和主题设置 -->
      <view class="settings-section">
        <view class="section-header">
          <text class="section-icon">🎨</text>
          <text class="section-title">显示和主题</text>
        </view>
        
        <view class="setting-items">
          <view class="setting-item">
            <view class="item-info">
              <text class="item-title">主题模式</text>
              <text class="item-desc">选择应用的主题颜色</text>
            </view>
            <view class="item-control">
              <picker 
                mode="selector"
                :range="themes"
                :value="themeIndex"
                @change="onThemeChange">
                <view class="picker-display">{{ themes[themeIndex] }}</view>
              </picker>
            </view>
          </view>
          
          <view class="setting-item">
            <view class="item-info">
              <text class="item-title">字体大小</text>
              <text class="item-desc">调整应用字体大小</text>
            </view>
            <view class="item-control">
              <picker 
                mode="selector"
                :range="fontSizes"
                :value="fontSizeIndex"
                @change="onFontSizeChange">
                <view class="picker-display">{{ fontSizes[fontSizeIndex] }}</view>
              </picker>
            </view>
          </view>
          
          <view class="setting-item toggle-item">
            <view class="item-info">
              <text class="item-title">保持屏幕常亮</text>
              <text class="item-desc">监测时保持屏幕不熄灭</text>
            </view>
            <view class="item-control">
              <switch 
                :checked="settings.display.keepScreenOn" 
                @change="toggleKeepScreenOn" 
                color="#3cc51f" />
            </view>
          </view>
          
          <view class="setting-item toggle-item">
            <view class="item-info">
              <text class="item-title">显示实时数据</text>
              <text class="item-desc">在首页显示实时监测数据</text>
            </view>
            <view class="item-control">
              <switch 
                :checked="settings.display.showRealtimeData" 
                @change="toggleShowRealtimeData" 
                color="#3cc51f" />
            </view>
          </view>
        </view>
      </view>

      <!-- 数据管理 -->
      <view class="settings-section">
        <view class="section-header">
          <text class="section-icon">🗂️</text>
          <text class="section-title">数据管理</text>
        </view>
        
        <view class="setting-items">
          <view class="setting-item action-item">
            <view class="item-info">
              <text class="item-title">导出数据</text>
              <text class="item-desc">导出所有监测数据</text>
            </view>
            <view class="item-control">
              <text class="action-btn primary" @tap="exportAllData">导出</text>
            </view>
          </view>
          
          <view class="setting-item action-item">
            <view class="item-info">
              <text class="item-title">清除历史数据</text>
              <text class="item-desc">清除所有历史监测记录</text>
            </view>
            <view class="item-control">
              <text class="action-btn danger" @tap="clearHistoryData">清除</text>
            </view>
          </view>
          
          <view class="setting-item">
            <view class="item-info">
              <text class="item-title">数据存储大小</text>
              <text class="item-desc">当前数据占用的存储空间</text>
            </view>
            <view class="item-control">
              <text class="storage-size">{{ formatStorageSize(storageUsed) }}</text>
            </view>
          </view>
          
          <view class="setting-item">
            <view class="item-info">
              <text class="item-title">最大存储记录数</text>
              <text class="item-desc">保留的最大历史记录数量</text>
            </view>
            <view class="item-control">
              <input 
                class="text-input" 
                type="number" 
                v-model="settings.data.maxRecords"
                placeholder="1000" />
            </view>
          </view>
        </view>
      </view>

      <!-- 设备信息 -->
      <view class="settings-section">
        <view class="section-header">
          <text class="section-icon">📱</text>
          <text class="section-title">设备信息</text>
        </view>
        
        <view class="setting-items">
          <view class="info-item">
            <text class="info-label">设备型号</text>
            <text class="info-value">LYLS-00021</text>
          </view>
          <view class="info-item">
            <text class="info-label">固件版本</text>
            <text class="info-value">v1.0.0</text>
          </view>
          <view class="info-item">
            <text class="info-label">设备序列号</text>
            <text class="info-value">{{ deviceState.deviceInfo.serialNumber || 'LYLS202400001' }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">IMEI</text>
            <text class="info-value">{{ deviceState.deviceInfo.imei || '未获取' }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">SIM卡ICCID</text>
            <text class="info-value">{{ deviceState.deviceInfo.iccid || '未获取' }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">电池电压</text>
            <text class="info-value">{{ deviceState.battery.voltage.toFixed(2) }}V</text>
          </view>
          <view class="info-item">
            <text class="info-label">电池电量</text>
            <text class="info-value">{{ deviceState.battery.level }}%</text>
          </view>
          <view class="info-item">
            <text class="info-label">充电状态</text>
            <text class="info-value">{{ deviceState.battery.charging ? '充电中' : '未充电' }}</text>
          </view>
        </view>
      </view>

      <!-- 关于和帮助 */
      <view class="settings-section">
        <view class="section-header">
          <text class="section-icon">ℹ️</text>
          <text class="section-title">关于和帮助</text>
        </view>
        
        <view class="setting-items">
          <view class="setting-item action-item" @tap="showAppInfo">
            <view class="item-info">
              <text class="item-title">应用信息</text>
              <text class="item-desc">查看应用版本和更新日志</text>
            </view>
            <view class="item-control">
              <text class="nav-arrow">></text>
            </view>
          </view>
          
          <view class="setting-item action-item" @tap="showUserManual">
            <view class="item-info">
              <text class="item-title">使用手册</text>
              <text class="item-desc">查看详细的使用说明</text>
            </view>
            <view class="item-control">
              <text class="nav-arrow">></text>
            </view>
          </view>
          
          <view class="setting-item action-item" @tap="showPrivacyPolicy">
            <view class="item-info">
              <text class="item-title">隐私政策</text>
              <text class="item-desc">查看隐私保护政策</text>
            </view>
            <view class="item-control">
              <text class="nav-arrow">></text>
            </view>
          </view>
          
          <view class="setting-item action-item" @tap="contactSupport">
            <view class="item-info">
              <text class="item-title">技术支持</text>
              <text class="item-desc">联系技术支持团队</text>
            </view>
            <view class="item-control">
              <text class="nav-arrow">></text>
            </view>
          </view>
          
          <view class="setting-item action-item" @tap="checkUpdate">
            <view class="item-info">
              <text class="item-title">检查更新</text>
              <text class="item-desc">检查应用和固件更新</text>
            </view>
            <view class="item-control">
              <text class="action-btn primary">检查</text>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>

    <!-- 版权信息 -->
    <view class="footer-info">
      <text class="copyright">© 2024 智能辐射检测系统</text>
      <text class="version">版本 1.0.0</text>
    </view>
  </view>
</template>

<script>
import { ref, reactive, computed, onMounted } from 'vue'
import { radiationState, deviceState } from '../../utils/dataStore.js'
import dataStore from '../../utils/dataStore.js'
import mqttService from '../../utils/mqttService.js'

export default {
  name: 'Settings',
  setup() {
    const mqttTestStatus = ref('测试连接')
    const storageUsed = ref(0)
    
    // 设置数据
    const settings = reactive({
      radiation: {
        maxDoseRate: radiationState.settings.maxDoseRate,
        minDoseRate: radiationState.settings.minDoseRate,
        maxDoseSum: radiationState.settings.maxDoseSum,
        autoUpload: radiationState.settings.autoUpload,
        uploadInterval: radiationState.settings.uploadInterval
      },
      mqtt: {
        host: 'broker.emqx.io',
        port: 8083,
        username: '',
        password: ''
      },
      notifications: {
        soundAlert: radiationState.settings.soundAlert,
        vibrationAlert: radiationState.settings.vibrationAlert,
        healthReminder: true,
        reminderInterval: 60,
        dataAnomalyAlert: true
      },
      display: {
        theme: 'dark',
        fontSize: 'medium',
        keepScreenOn: false,
        showRealtimeData: true
      },
      data: {
        maxRecords: 1000,
        autoBackup: false
      }
    })

    // 选择器选项
    const uploadIntervals = ['30秒', '1分钟', '5分钟', '10分钟', '30分钟', '1小时']
    const reminderIntervals = ['30分钟', '1小时', '2小时', '6小时', '12小时', '24小时']
    const themes = ['深色模式', '浅色模式', '自动切换']
    const fontSizes = ['小', '中', '大', '特大']

    // 选择器索引
    const uploadIntervalIndex = ref(2) // 默认5分钟
    const reminderIntervalIndex = ref(1) // 默认1小时
    const themeIndex = ref(0) // 默认深色模式
    const fontSizeIndex = ref(1) // 默认中等字体

    // 计算存储使用量
    const calculateStorageUsed = () => {
      try {
        const data = dataStore.exportData()
        const jsonStr = JSON.stringify(data)
        storageUsed.value = new Blob([jsonStr]).size
      } catch (error) {
        storageUsed.value = 0
      }
    }

    // 方法
    const toggleAutoUpload = (e) => {
      settings.radiation.autoUpload = e.detail.value
    }

    const toggleSoundAlert = (e) => {
      settings.notifications.soundAlert = e.detail.value
    }

    const toggleVibrationAlert = (e) => {
      settings.notifications.vibrationAlert = e.detail.value
    }

    const toggleHealthReminder = (e) => {
      settings.notifications.healthReminder = e.detail.value
    }

    const toggleKeepScreenOn = (e) => {
      settings.display.keepScreenOn = e.detail.value
      
      if (e.detail.value) {
        uni.setKeepScreenOn({
          keepScreenOn: true
        })
      } else {
        uni.setKeepScreenOn({
          keepScreenOn: false
        })
      }
    }

    const toggleShowRealtimeData = (e) => {
      settings.display.showRealtimeData = e.detail.value
    }

    const toggleDataAnomalyAlert = (e) => {
      settings.notifications.dataAnomalyAlert = e.detail.value
    }

    const onUploadIntervalChange = (e) => {
      uploadIntervalIndex.value = e.detail.value
      const intervals = [30, 60, 300, 600, 1800, 3600] // 对应秒数
      settings.radiation.uploadInterval = intervals[e.detail.value]
    }

    const onReminderIntervalChange = (e) => {
      reminderIntervalIndex.value = e.detail.value
      const intervals = [30, 60, 120, 360, 720, 1440] // 对应分钟数
      settings.notifications.reminderInterval = intervals[e.detail.value]
    }

    const onThemeChange = (e) => {
      themeIndex.value = e.detail.value
      const themeValues = ['dark', 'light', 'auto']
      settings.display.theme = themeValues[e.detail.value]
      
      uni.showToast({
        title: '主题已切换',
        icon: 'success'
      })
    }

    const onFontSizeChange = (e) => {
      fontSizeIndex.value = e.detail.value
      const fontSizeValues = ['small', 'medium', 'large', 'extra-large']
      settings.display.fontSize = fontSizeValues[e.detail.value]
      
      uni.showToast({
        title: '字体大小已调整',
        icon: 'success'
      })
    }

    const testMqttConnection = async () => {
      mqttTestStatus.value = '连接中...'
      
      try {
        // 使用设置中的MQTT配置进行连接测试
        const testConnection = new Promise((resolve, reject) => {
          const testService = {
            connect: (options) => {
              // 模拟连接测试
              setTimeout(() => {
                if (options.host && options.port) {
                  resolve('连接成功')
                } else {
                  reject('连接失败')
                }
              }, 2000)
            }
          }
          
          testService.connect({
            host: settings.mqtt.host,
            port: settings.mqtt.port,
            username: settings.mqtt.username,
            password: settings.mqtt.password
          })
        })
        
        const result = await testConnection
        mqttTestStatus.value = '连接成功 ✓'
        
        uni.showToast({
          title: 'MQTT连接成功',
          icon: 'success'
        })
        
        setTimeout(() => {
          mqttTestStatus.value = '测试连接'
        }, 3000)
        
      } catch (error) {
        mqttTestStatus.value = '连接失败 ✗'
        
        uni.showToast({
          title: 'MQTT连接失败',
          icon: 'error'
        })
        
        setTimeout(() => {
          mqttTestStatus.value = '测试连接'
        }, 3000)
      }
    }

    const exportAllData = () => {
      uni.showModal({
        title: '导出数据',
        content: '是否导出所有监测数据？这可能需要一些时间。',
        success: (res) => {
          if (res.confirm) {
            uni.showLoading({
              title: '导出中...'
            })
            
            setTimeout(() => {
              try {
                const data = dataStore.exportData()
                const jsonStr = JSON.stringify(data, null, 2)
                
                uni.hideLoading()
                uni.showModal({
                  title: '导出成功',
                  content: `数据已准备完成，大小：${formatStorageSize(new Blob([jsonStr]).size)}`,
                  confirmText: '确定',
                  success: () => {
                    uni.showToast({
                      title: '数据已导出',
                      icon: 'success'
                    })
                  }
                })
              } catch (error) {
                uni.hideLoading()
                uni.showToast({
                  title: '导出失败',
                  icon: 'error'
                })
              }
            }, 2000)
          }
        }
      })
    }

    const clearHistoryData = () => {
      uni.showModal({
        title: '清除数据',
        content: '确定要清除所有历史数据吗？此操作不可恢复！',
        confirmText: '确定清除',
        confirmColor: '#dc3545',
        success: (res) => {
          if (res.confirm) {
            dataStore.clearHistory()
            calculateStorageUsed()
            
            uni.showToast({
              title: '数据已清除',
              icon: 'success'
            })
          }
        }
      })
    }

    const formatStorageSize = (bytes) => {
      if (bytes === 0) return '0 B'
      
      const k = 1024
      const sizes = ['B', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    }

    const saveAllSettings = () => {
      // 保存辐射设置
      Object.assign(radiationState.settings, settings.radiation)
      
      // 保存MQTT设置
      if (settings.mqtt.host && settings.mqtt.port) {
        mqttService.disconnect()
        mqttService.connect({
          host: settings.mqtt.host,
          port: settings.mqtt.port,
          username: settings.mqtt.username,
          password: settings.mqtt.password
        })
      }
      
      // 保存到本地存储
      try {
        uni.setStorageSync('appSettings', JSON.stringify(settings))
        
        uni.showToast({
          title: '设置已保存',
          icon: 'success'
        })
      } catch (error) {
        uni.showToast({
          title: '保存失败',
          icon: 'error'
        })
      }
    }

    const resetAllSettings = () => {
      uni.showModal({
        title: '重置设置',
        content: '确定要重置所有设置为默认值吗？',
        success: (res) => {
          if (res.confirm) {
            // 重置为默认值
            settings.radiation.maxDoseRate = 1.0
            settings.radiation.minDoseRate = 0.01
            settings.radiation.maxDoseSum = 100.0
            settings.radiation.autoUpload = true
            settings.radiation.uploadInterval = 300
            
            settings.mqtt.host = 'broker.emqx.io'
            settings.mqtt.port = 8083
            settings.mqtt.username = ''
            settings.mqtt.password = ''
            
            settings.notifications.soundAlert = true
            settings.notifications.vibrationAlert = true
            settings.notifications.healthReminder = true
            settings.notifications.reminderInterval = 60
            settings.notifications.dataAnomalyAlert = true
            
            settings.display.theme = 'dark'
            settings.display.fontSize = 'medium'
            settings.display.keepScreenOn = false
            settings.display.showRealtimeData = true
            
            settings.data.maxRecords = 1000
            
            // 重置选择器索引
            uploadIntervalIndex.value = 2
            reminderIntervalIndex.value = 1
            themeIndex.value = 0
            fontSizeIndex.value = 1
            
            uni.showToast({
              title: '设置已重置',
              icon: 'success'
            })
          }
        }
      })
    }

    const showAppInfo = () => {
      uni.showModal({
        title: '应用信息',
        content: '智能辐射检测系统 v1.0.0\n\n这是一款专业的辐射监测应用，提供实时监测、数据分析、健康评估等功能。\n\n开发团队：LYLS科技\n发布日期：2024年1月',
        confirmText: '确定',
        showCancel: false
      })
    }

    const showUserManual = () => {
      uni.showModal({
        title: '使用手册',
        content: '请访问官网查看详细的使用手册和技术文档。\n\n官网：www.lyls-tech.com\n技术支持：<EMAIL>',
        confirmText: '确定',
        showCancel: false
      })
    }

    const showPrivacyPolicy = () => {
      uni.showModal({
        title: '隐私政策',
        content: '我们重视您的隐私保护。所有监测数据均在本地存储，仅在您授权的情况下上传到服务器。\n\n详细的隐私政策请访问官网查看。',
        confirmText: '确定',
        showCancel: false
      })
    }

    const contactSupport = () => {
      uni.showActionSheet({
        itemList: ['发送邮件', '拨打电话', '在线客服'],
        success: (res) => {
          switch (res.tapIndex) {
            case 0:
              uni.showToast({ title: '邮件客户端已打开', icon: 'none' })
              break
            case 1:
              uni.makePhoneCall({
                phoneNumber: '************'
              })
              break
            case 2:
              uni.showToast({ title: '在线客服功能开发中', icon: 'none' })
              break
          }
        }
      })
    }

    const checkUpdate = () => {
      uni.showLoading({
        title: '检查中...'
      })
      
      setTimeout(() => {
        uni.hideLoading()
        uni.showModal({
          title: '检查更新',
          content: '当前已是最新版本。\n\n应用版本：v1.0.0\n固件版本：v1.0.0',
          confirmText: '确定',
          showCancel: false
        })
      }, 2000)
    }

    // 生命周期
    onMounted(() => {
      // 加载保存的设置
      try {
        const savedSettings = uni.getStorageSync('appSettings')
        if (savedSettings) {
          const parsed = JSON.parse(savedSettings)
          Object.assign(settings, parsed)
        }
      } catch (error) {
        console.error('加载设置失败:', error)
      }
      
      // 计算存储使用量
      calculateStorageUsed()
    })

    return {
      settings,
      deviceState,
      mqttTestStatus,
      storageUsed,
      uploadIntervals,
      reminderIntervals,
      themes,
      fontSizes,
      uploadIntervalIndex,
      reminderIntervalIndex,
      themeIndex,
      fontSizeIndex,
      toggleAutoUpload,
      toggleSoundAlert,
      toggleVibrationAlert,
      toggleHealthReminder,
      toggleKeepScreenOn,
      toggleShowRealtimeData,
      toggleDataAnomalyAlert,
      onUploadIntervalChange,
      onReminderIntervalChange,
      onThemeChange,
      onFontSizeChange,
      testMqttConnection,
      exportAllData,
      clearHistoryData,
      formatStorageSize,
      saveAllSettings,
      resetAllSettings,
      showAppInfo,
      showUserManual,
      showPrivacyPolicy,
      contactSupport,
      checkUpdate
    }
  }
}
</script>

<style scoped>
.settings-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #0f0f1c 0%, #1a1a2e 100%);
}

/* 设置头部 */
.settings-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 20rpx 20rpx;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.header-title {
  font-size: 36rpx;
  color: #ffffff;
  font-weight: 700;
}

.header-actions {
  display: flex;
  gap: 15rpx;
}

.action-btn {
  padding: 15rpx 25rpx;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20rpx;
  color: #ffffff;
  font-size: 24rpx;
  font-weight: 500;
  transition: all 0.3s ease;
}

.action-btn.save {
  background: rgba(60, 197, 31, 0.2);
  border-color: rgba(60, 197, 31, 0.3);
  color: #3cc51f;
}

.action-btn:active {
  transform: scale(0.95);
}

/* 设置内容 */
.settings-content {
  height: calc(100vh - 120rpx);
  padding: 20rpx;
}

/* 设置分组 */
.settings-section {
  margin-bottom: 40rpx;
}

.section-header {
  display: flex;
  align-items: center;
  gap: 15rpx;
  padding: 25rpx 20rpx;
  margin-bottom: 15rpx;
}

.section-icon {
  font-size: 28rpx;
}

.section-title {
  font-size: 28rpx;
  color: #ffffff;
  font-weight: 600;
}

/* 设置项容器 */
.setting-items {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20px);
  border-radius: 20rpx;
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* 设置项 */
.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 25rpx;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  transition: all 0.3s ease;
}

.setting-item:last-child {
  border-bottom: none;
}

.setting-item.action-item:active {
  background: rgba(255, 255, 255, 0.03);
}

.item-info {
  flex: 1;
  margin-right: 20rpx;
}

.item-title {
  display: block;
  font-size: 26rpx;
  color: #ffffff;
  font-weight: 500;
  margin-bottom: 8rpx;
}

.item-desc {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.6);
  line-height: 1.3;
}

/* 控件样式 */
.item-control {
  display: flex;
  align-items: center;
  gap: 10rpx;
}

.threshold-input,
.text-input {
  width: 120rpx;
  height: 60rpx;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12rpx;
  padding: 0 15rpx;
  color: #ffffff;
  font-size: 24rpx;
  text-align: center;
}

.threshold-unit {
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.7);
  min-width: 60rpx;
}

.picker-display {
  padding: 15rpx 20rpx;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12rpx;
  color: #ffffff;
  font-size: 24rpx;
  min-width: 120rpx;
  text-align: center;
}

.test-btn {
  padding: 15rpx 25rpx;
  background: rgba(33, 150, 243, 0.2);
  border: 1px solid rgba(33, 150, 243, 0.3);
  border-radius: 15rpx;
  color: #2196f3;
  font-size: 22rpx;
  font-weight: 500;
  transition: all 0.3s ease;
}

.test-btn:active {
  transform: scale(0.95);
  background: rgba(33, 150, 243, 0.3);
}

.action-btn.primary {
  background: rgba(60, 197, 31, 0.2);
  border: 1px solid rgba(60, 197, 31, 0.3);
  color: #3cc51f;
}

.action-btn.danger {
  background: rgba(220, 53, 69, 0.2);
  border: 1px solid rgba(220, 53, 69, 0.3);
  color: #dc3545;
}

.storage-size {
  font-size: 24rpx;
  color: #ffffff;
  font-weight: 600;
  padding: 15rpx 20rpx;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12rpx;
}

.nav-arrow {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.5);
  font-weight: bold;
}

/* 信息项 */
.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 25rpx;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.info-item:last-child {
  border-bottom: none;
}

.info-label {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.7);
}

.info-value {
  font-size: 24rpx;
  color: #ffffff;
  font-weight: 500;
  max-width: 300rpx;
  text-align: right;
  word-break: break-all;
}

/* 版权信息 */
.footer-info {
  text-align: center;
  padding: 30rpx 20rpx;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(255, 255, 255, 0.02);
}

.copyright {
  display: block;
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.6);
  margin-bottom: 8rpx;
}

.version {
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.4);
}

/* 开关样式覆盖 */
.setting-item.toggle-item .item-control {
  justify-content: flex-end;
}

/* 输入框聚焦样式 */
.threshold-input:focus,
.text-input:focus {
  border-color: rgba(60, 197, 31, 0.5);
  background: rgba(60, 197, 31, 0.05);
}

/* 选择器激活样式 */
.picker-display:active {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(60, 197, 31, 0.3);
}

/* 响应式布局 */
@media (max-width: 750rpx) {
  .setting-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 15rpx;
  }
  
  .item-control {
    width: 100%;
    justify-content: flex-end;
  }
  
  .threshold-input,
  .text-input,
  .picker-display {
    width: 200rpx;
  }
}
</style> 