Component: ARM Compiler 5.06 update 7 (build 960) Tool: armlink [4d3601]

==============================================================================

Section Cross References

    main.o(.text) refers to _printf_pad.o(.text) for _printf_pre_padding
    main.o(.text) refers to _printf_truncate.o(.text) for _printf_truncate_unsigned
    main.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    main.o(.text) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    main.o(.text) refers to _printf_i.o(.ARM.Collect$$_printf_percent$$00000008) for _printf_i
    main.o(.text) refers to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    main.o(.text) refers to _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B) for _printf_o
    main.o(.text) refers to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    main.o(.text) refers to _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) for _printf_c
    main.o(.text) refers to _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) for _printf_lc
    main.o(.text) refers to _printf_dec.o(.text) for _printf_int_dec
    main.o(.text) refers to _printf_oct_int.o(.text) for _printf_longlong_oct
    main.o(.text) refers to _printf_hex_int.o(.text) for _printf_longlong_hex
    main.o(.text) refers to _printf_str.o(.text) for _printf_str
    main.o(.text) refers to _printf_wctomb.o(.text) for _printf_wctomb
    main.o(.text) refers to delay.o(.text) for delay_init
    main.o(.text) refers to sys.o(.text) for NVIC_Configuration
    main.o(.text) refers to led.o(.text) for LED_Init
    main.o(.text) refers to usart.o(.text) for uart_init
    main.o(.text) refers to noretval__2printf.o(.text) for __2printf
    main.o(.text) refers to bsp_i2c.o(.text) for I2C_Bus_Init
    main.o(.text) refers to ec800.o(.text) for Uart1_SendStr
    main.o(.text) refers to aht20.o(.text) for AHT20_Init
    main.o(.text) refers to wdg.o(.text) for IWDG_Init
    main.o(.text) refers to noretval__2sprintf.o(.text) for __2sprintf
    system_stm32f10x.o(.text) refers to stm32f10x_rcc.o(.text) for RCC_DeInit
    system_stm32f10x.o(.text) refers to stm32f10x_flash.o(.text) for FLASH_PrefetchBufferCmd
    system_stm32f10x.o(.text) refers to system_stm32f10x.o(.data) for SystemCoreClock
    led.o(.text) refers to stm32f10x_rcc.o(.text) for RCC_APB2PeriphClockCmd
    led.o(.text) refers to stm32f10x_gpio.o(.text) for GPIO_Init
    wdg.o(.text) refers to stm32f10x_iwdg.o(.text) for IWDG_WriteAccessCmd
    ec800.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    ec800.o(.text) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    ec800.o(.text) refers to _printf_str.o(.text) for _printf_str
    ec800.o(.text) refers to wdg.o(.text) for IWDG_Feed
    ec800.o(.text) refers to noretval__2printf.o(.text) for __2printf
    ec800.o(.text) refers to delay.o(.text) for delay_ms
    ec800.o(.text) refers to strstr.o(.text) for strstr
    ec800.o(.text) refers to stm32f10x_usart.o(.text) for USART_SendData
    ec800.o(.text) refers to usart.o(.bss) for RxBuffer
    ec800.o(.text) refers to usart.o(.data) for RxCounter
    ec800.o(.text) refers to ec800.o(.data) for strx
    aht20.o(.text) refers to bsp_i2c.o(.text) for Sensors_I2C_ReadRegister
    aht20.o(.text) refers to delay.o(.text) for delay_ms
    aht20.o(.text) refers to aht20.o(.data) for count
    bsp_i2c.o(.text) refers to stm32f10x_rcc.o(.text) for RCC_APB2PeriphClockCmd
    bsp_i2c.o(.text) refers to stm32f10x_gpio.o(.text) for GPIO_Init
    bsp_i2c.o(.text) refers to delay.o(.text) for delay_ms
    bsp_i2c.o(.text) refers to bsp_i2c.o(.data) for RETRY_IN_MLSEC
    stm32f10x_gpio.o(.text) refers to stm32f10x_rcc.o(.text) for RCC_APB2PeriphResetCmd
    stm32f10x_usart.o(.text) refers to stm32f10x_rcc.o(.text) for RCC_APB2PeriphResetCmd
    stm32f10x_rcc.o(.text) refers to stm32f10x_rcc.o(.data) for APBAHBPrescTable
    startup_stm32f10x_hd.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_hd.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_hd.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_hd.o(RESET) refers to startup_stm32f10x_hd.o(STACK) for __initial_sp
    startup_stm32f10x_hd.o(RESET) refers to startup_stm32f10x_hd.o(.text) for Reset_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(.text) for NMI_Handler
    startup_stm32f10x_hd.o(RESET) refers to usart.o(.text) for USART1_IRQHandler
    startup_stm32f10x_hd.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_hd.o(.text) refers to system_stm32f10x.o(.text) for SystemInit
    startup_stm32f10x_hd.o(.text) refers to __main.o(!!!main) for __main
    startup_stm32f10x_hd.o(.text) refers to startup_stm32f10x_hd.o(HEAP) for Heap_Mem
    startup_stm32f10x_hd.o(.text) refers to startup_stm32f10x_hd.o(STACK) for Stack_Mem
    usart.o(.text) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(.text) refers to stm32f10x_rcc.o(.text) for RCC_APB2PeriphClockCmd
    usart.o(.text) refers to stm32f10x_usart.o(.text) for USART_DeInit
    usart.o(.text) refers to stm32f10x_gpio.o(.text) for GPIO_Init
    usart.o(.text) refers to misc.o(.text) for NVIC_Init
    usart.o(.text) refers to usart.o(.data) for RxCounter
    usart.o(.text) refers to usart.o(.bss) for RxBuffer
    usart.o(.bss) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(.data) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    delay.o(.text) refers to misc.o(.text) for SysTick_CLKSourceConfig
    delay.o(.text) refers to system_stm32f10x.o(.data) for SystemCoreClock
    delay.o(.text) refers to delay.o(.data) for fac_us
    sys.o(.text) refers to misc.o(.text) for NVIC_PriorityGroupConfig
    use_no_semi_2.o(.text) refers (Special) to use_no_semi.o(.text) for __use_no_semihosting_swi
    __2printf.o(.text) refers to _printf_char_file.o(.text) for _printf_char_file
    __2printf.o(.text) refers to usart.o(.data) for __stdout
    __2sprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    __2sprintf.o(.text) refers to _sputc.o(.text) for _sputc
    noretval__2printf.o(.text) refers to _printf_char_file.o(.text) for _printf_char_file
    noretval__2printf.o(.text) refers to usart.o(.data) for __stdout
    noretval__2sprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    noretval__2sprintf.o(.text) refers to _sputc.o(.text) for _sputc
    __printf.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    _printf_str.o(.text) refers (Special) to _printf_char.o(.text) for _printf_cs_common
    _printf_str.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_str.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_dec.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_signed
    _printf_dec.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_wctomb.o(.text) refers (Special) to _printf_wchar.o(.text) for _printf_lcs_common
    _printf_wctomb.o(.text) refers to _wcrtomb.o(.text) for _wcrtomb
    _printf_wctomb.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_wctomb.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_wctomb.o(.text) refers to _printf_wctomb.o(.constdata) for .constdata
    _printf_wctomb.o(.constdata) refers (Special) to _printf_wchar.o(.text) for _printf_lcs_common
    _printf_oct_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_oct_int.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int_ll.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll.o(.text) refers to _printf_hex_ll.o(.constdata) for .constdata
    _printf_hex_int.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int.o(.text) refers to _printf_hex_int.o(.constdata) for .constdata
    _printf_hex_int_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ll.o(.text) refers to _printf_hex_int_ll.o(.constdata) for .constdata
    _printf_hex_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ptr.o(.text) refers to _printf_hex_ptr.o(.constdata) for .constdata
    _printf_hex_int_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ptr.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ptr.o(.text) refers to _printf_hex_int_ptr.o(.constdata) for .constdata
    _printf_hex_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll_ptr.o(.text) refers to _printf_hex_ll_ptr.o(.constdata) for .constdata
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll_ptr.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_hex_int_ll_ptr.o(.constdata) for .constdata
    __printf_flags.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags.o(.text) refers to __printf_flags.o(.constdata) for .constdata
    __printf_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to __printf_flags_ss.o(.constdata) for .constdata
    __printf_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_flags_wp.o(.constdata) for .constdata
    __printf_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_flags_ss_wp.o(.constdata) for .constdata
    _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) refers (Weak) to _printf_char.o(.text) for _printf_char
    _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) refers (Weak) to _printf_char.o(.text) for _printf_string
    _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) refers (Weak) to _printf_hex_int.o(.text) for _printf_int_hex
    _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B) refers (Weak) to _printf_oct_int.o(.text) for _printf_int_oct
    _printf_i.o(.ARM.Collect$$_printf_percent$$00000008) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) refers (Special) to _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017) for _printf_percent_end
    _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) refers (Weak) to _printf_wchar.o(.text) for _printf_wchar
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_char_common.o(.text) refers to __printf_flags_ss_wp.o(.text) for __printf
    _printf_char.o(.text) refers (Weak) to _printf_str.o(.text) for _printf_str
    _printf_char_file.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    _printf_char_file.o(.text) refers to ferror.o(.text) for ferror
    _printf_char_file.o(.text) refers to usart.o(.text) for fputc
    _printf_wchar.o(.text) refers (Weak) to _printf_wctomb.o(.text) for _printf_wctomb
    _wcrtomb.o(.text) refers to rt_ctype_table.o(.text) for __rt_ctype_table
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to main.o(.text) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    rt_ctype_table.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    rt_ctype_table.o(.text) refers to lc_ctype_c.o(locale$$code) for _get_lc_ctype
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_stm32f10x_hd.o(.text) for __user_initial_stackheap
    rt_locale.o(.text) refers to rt_locale.o(.bss) for __rt_locale_data
    rt_locale_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    lc_ctype_c.o(locale$$data) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000012) for __rt_lib_init_lc_ctype_2
    lc_ctype_c.o(locale$$code) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000012) for __rt_lib_init_lc_ctype_2
    lc_ctype_c.o(locale$$code) refers to strcmpv7m.o(.text) for strcmp
    lc_ctype_c.o(locale$$code) refers to lc_ctype_c.o(locale$$data) for __lcctype_c_name
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002C) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000002) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000A) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000011) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000033) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_user_alloc_1
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    libinit2.o(.ARM.Collect$$libinit$$0000000F) refers (Weak) to rt_locale_intlibspace.o(.text) for __rt_locale
    libinit2.o(.ARM.Collect$$libinit$$00000010) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers (Weak) to lc_ctype_c.o(locale$$code) for _get_lc_ctype
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000026) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000027) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to usart.o(.text) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    argv_veneer.o(.emb_text) refers to no_argv.o(.text) for __ARM_get_argv
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000004) for __rt_lib_shutdown_cpp_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000002) for __rt_lib_shutdown_fini_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000009) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000011) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000012) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000006) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E) for __rt_lib_shutdown_user_alloc_1
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to usart.o(.text) for _sys_exit
    defsig_exit.o(.text) refers to usart.o(.text) for _sys_exit
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    sys_wrch.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display


==============================================================================

Removing Unused input sections from the image.

    Removing ec800.o(.bss), (100 bytes).
    Removing core_cm3.o(.emb_text), (32 bytes).

2 unused section(s) (total 132 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit1.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit2.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit3.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardshut.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_zi.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_copy.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry4.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_raise.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_ctype_table.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale_intlibspace.o ABSOLUTE
    ../clib/angel/scatter.s                  0x00000000   Number         0  __scatter.o ABSOLUTE
    ../clib/angel/startup.s                  0x00000000   Number         0  __main.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi_2.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  libspace.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  indicate_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  sys_stackheap_outer.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  no_argv.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv_nomalloc.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hrguard.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxi.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown2.o ABSOLUTE
    ../clib/locale.c                         0x00000000   Number         0  _wcrtomb.o ABSOLUTE
    ../clib/locale.s                         0x00000000   Number         0  lc_ctype_c.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  strcmpv7m.o ABSOLUTE
    ../clib/misc.s                           0x00000000   Number         0  printf_stubs.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_wchar.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_nopercent.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_file.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _sputc.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ll_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ll_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_intcommon.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_common.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __2printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __2sprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  noretval__2printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  noretval__2sprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_pad.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_truncate.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_str.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_wctomb.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_oct_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_oct_int.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_oct_int_ll.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent_end.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_c.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_s.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_x.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_o.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_u.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_lc.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_i.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_d.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_l.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_segv_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_general.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  __raise.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_stak_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_pvfn_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_cppl_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_other.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_exit.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_inner.o ABSOLUTE
    ../clib/signal.s                         0x00000000   Number         0  defsig.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  ferror_locked.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  ferror.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  exit.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  strstr.o ABSOLUTE
    ../fplib/fpinit.s                        0x00000000   Number         0  fpinit.o ABSOLUTE
    ..\CORE\core_cm3.c                       0x00000000   Number         0  core_cm3.o ABSOLUTE
    ..\CORE\startup_stm32f10x_hd.s           0x00000000   Number         0  startup_stm32f10x_hd.o ABSOLUTE
    ..\HARDWARE\AHT20\AHT20.c                0x00000000   Number         0  aht20.o ABSOLUTE
    ..\HARDWARE\AHT20\bsp_i2c.c              0x00000000   Number         0  bsp_i2c.o ABSOLUTE
    ..\HARDWARE\EC800\EC800.c                0x00000000   Number         0  ec800.o ABSOLUTE
    ..\HARDWARE\LED\led.c                    0x00000000   Number         0  led.o ABSOLUTE
    ..\HARDWARE\WDG\wdg.c                    0x00000000   Number         0  wdg.o ABSOLUTE
    ..\STM32F10x_FWLib\src\misc.c            0x00000000   Number         0  misc.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_flash.c 0x00000000   Number         0  stm32f10x_flash.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_gpio.c  0x00000000   Number         0  stm32f10x_gpio.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_iwdg.c  0x00000000   Number         0  stm32f10x_iwdg.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_rcc.c   0x00000000   Number         0  stm32f10x_rcc.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_usart.c 0x00000000   Number         0  stm32f10x_usart.o ABSOLUTE
    ..\SYSTEM\delay\delay.c                  0x00000000   Number         0  delay.o ABSOLUTE
    ..\SYSTEM\sys\sys.c                      0x00000000   Number         0  sys.o ABSOLUTE
    ..\SYSTEM\usart\usart.c                  0x00000000   Number         0  usart.o ABSOLUTE
    ..\\CORE\\core_cm3.c                     0x00000000   Number         0  core_cm3.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    main.c                                   0x00000000   Number         0  main.o ABSOLUTE
    stm32f10x_it.c                           0x00000000   Number         0  stm32f10x_it.o ABSOLUTE
    system_stm32f10x.c                       0x00000000   Number         0  system_stm32f10x.o ABSOLUTE
    RESET                                    0x08000000   Section      304  startup_stm32f10x_hd.o(RESET)
    !!!main                                  0x08000130   Section        8  __main.o(!!!main)
    !!!scatter                               0x08000138   Section       52  __scatter.o(!!!scatter)
    !!handler_copy                           0x0800016c   Section       26  __scatter_copy.o(!!handler_copy)
    !!handler_zi                             0x08000188   Section       28  __scatter_zi.o(!!handler_zi)
    .ARM.Collect$$_printf_percent$$00000000  0x080001a4   Section        0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    .ARM.Collect$$_printf_percent$$00000008  0x080001a4   Section        6  _printf_i.o(.ARM.Collect$$_printf_percent$$00000008)
    .ARM.Collect$$_printf_percent$$00000009  0x080001aa   Section        6  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    .ARM.Collect$$_printf_percent$$0000000A  0x080001b0   Section        6  _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A)
    .ARM.Collect$$_printf_percent$$0000000B  0x080001b6   Section        6  _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B)
    .ARM.Collect$$_printf_percent$$0000000C  0x080001bc   Section        6  _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C)
    .ARM.Collect$$_printf_percent$$00000012  0x080001c2   Section       10  _printf_l.o(.ARM.Collect$$_printf_percent$$00000012)
    .ARM.Collect$$_printf_percent$$00000013  0x080001cc   Section        6  _printf_c.o(.ARM.Collect$$_printf_percent$$00000013)
    .ARM.Collect$$_printf_percent$$00000014  0x080001d2   Section        6  _printf_s.o(.ARM.Collect$$_printf_percent$$00000014)
    .ARM.Collect$$_printf_percent$$00000015  0x080001d8   Section        6  _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015)
    .ARM.Collect$$_printf_percent$$00000017  0x080001de   Section        4  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    .ARM.Collect$$libinit$$00000000          0x080001e2   Section        2  libinit.o(.ARM.Collect$$libinit$$00000000)
    .ARM.Collect$$libinit$$00000002          0x080001e4   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000002)
    .ARM.Collect$$libinit$$00000004          0x080001e4   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    .ARM.Collect$$libinit$$0000000A          0x080001e4   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    .ARM.Collect$$libinit$$0000000C          0x080001e4   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    .ARM.Collect$$libinit$$0000000E          0x080001e4   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    .ARM.Collect$$libinit$$0000000F          0x080001e4   Section        6  libinit2.o(.ARM.Collect$$libinit$$0000000F)
    .ARM.Collect$$libinit$$00000011          0x080001ea   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    .ARM.Collect$$libinit$$00000012          0x080001ea   Section       12  libinit2.o(.ARM.Collect$$libinit$$00000012)
    .ARM.Collect$$libinit$$00000013          0x080001f6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    .ARM.Collect$$libinit$$00000015          0x080001f6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    .ARM.Collect$$libinit$$00000017          0x080001f6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    .ARM.Collect$$libinit$$00000019          0x080001f6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    .ARM.Collect$$libinit$$0000001B          0x080001f6   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    .ARM.Collect$$libinit$$0000001D          0x080001f6   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    .ARM.Collect$$libinit$$0000001F          0x080001f6   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    .ARM.Collect$$libinit$$00000021          0x080001f6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    .ARM.Collect$$libinit$$00000023          0x080001f6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    .ARM.Collect$$libinit$$00000025          0x080001f6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    .ARM.Collect$$libinit$$0000002C          0x080001f6   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    .ARM.Collect$$libinit$$0000002E          0x080001f6   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    .ARM.Collect$$libinit$$00000030          0x080001f6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    .ARM.Collect$$libinit$$00000032          0x080001f6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    .ARM.Collect$$libinit$$00000033          0x080001f6   Section        2  libinit2.o(.ARM.Collect$$libinit$$00000033)
    .ARM.Collect$$libshutdown$$00000000      0x080001f8   Section        2  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    .ARM.Collect$$libshutdown$$00000002      0x080001fa   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    .ARM.Collect$$libshutdown$$00000004      0x080001fa   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    .ARM.Collect$$libshutdown$$00000006      0x080001fa   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000006)
    .ARM.Collect$$libshutdown$$00000009      0x080001fa   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000009)
    .ARM.Collect$$libshutdown$$0000000C      0x080001fa   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    .ARM.Collect$$libshutdown$$0000000E      0x080001fa   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E)
    .ARM.Collect$$libshutdown$$00000011      0x080001fa   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000011)
    .ARM.Collect$$libshutdown$$00000012      0x080001fa   Section        2  libshutdown2.o(.ARM.Collect$$libshutdown$$00000012)
    .ARM.Collect$$rtentry$$00000000          0x080001fc   Section        0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    .ARM.Collect$$rtentry$$00000002          0x080001fc   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    .ARM.Collect$$rtentry$$00000004          0x080001fc   Section        6  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    .ARM.Collect$$rtentry$$00000009          0x08000202   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    .ARM.Collect$$rtentry$$0000000A          0x08000202   Section        4  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    .ARM.Collect$$rtentry$$0000000C          0x08000206   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    .ARM.Collect$$rtentry$$0000000D          0x08000206   Section        8  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    .ARM.Collect$$rtexit$$00000000           0x0800020e   Section        2  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    .ARM.Collect$$rtexit$$00000002           0x08000210   Section        0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    .ARM.Collect$$rtexit$$00000003           0x08000210   Section        4  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    .ARM.Collect$$rtexit$$00000004           0x08000214   Section        6  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    .text                                    0x0800021c   Section        0  main.o(.text)
    .text                                    0x08000334   Section        0  stm32f10x_it.o(.text)
    .text                                    0x08000350   Section        0  system_stm32f10x.o(.text)
    SetSysClockTo72                          0x080003bf   Thumb Code   214  system_stm32f10x.o(.text)
    SetSysClock                              0x08000495   Thumb Code     8  system_stm32f10x.o(.text)
    .text                                    0x0800059c   Section        0  led.o(.text)
    .text                                    0x08000600   Section        0  wdg.o(.text)
    .text                                    0x0800062c   Section        0  ec800.o(.text)
    .text                                    0x080009b8   Section        0  aht20.o(.text)
    .text                                    0x08000af8   Section        0  bsp_i2c.o(.text)
    Soft_I2C_Delay                           0x08000af9   Thumb Code    10  bsp_i2c.o(.text)
    Soft_I2C_Configuration                   0x08000b0f   Thumb Code    60  bsp_i2c.o(.text)
    Soft_I2C_START                           0x08000b59   Thumb Code    90  bsp_i2c.o(.text)
    Soft_I2C_STOP                            0x08000bb3   Thumb Code    46  bsp_i2c.o(.text)
    Soft_I2C_SendACK                         0x08000be1   Thumb Code    46  bsp_i2c.o(.text)
    Soft_I2C_SendNACK                        0x08000c0f   Thumb Code    46  bsp_i2c.o(.text)
    Soft_I2C_SendByte                        0x08000c87   Thumb Code    92  bsp_i2c.o(.text)
    Soft_I2C_ReceiveByte                     0x08000ce3   Thumb Code    86  bsp_i2c.o(.text)
    Soft_I2C_ReceiveByte_WithACK             0x08000d39   Thumb Code    86  bsp_i2c.o(.text)
    Soft_DMP_I2C_Write                       0x08000d8f   Thumb Code    90  bsp_i2c.o(.text)
    Soft_DMP_I2C_Read                        0x08000de9   Thumb Code   114  bsp_i2c.o(.text)
    .text                                    0x08000f00   Section        0  stm32f10x_gpio.o(.text)
    .text                                    0x0800125c   Section        0  stm32f10x_iwdg.o(.text)
    .text                                    0x0800129c   Section        0  stm32f10x_usart.o(.text)
    .text                                    0x080016a4   Section        0  stm32f10x_rcc.o(.text)
    .text                                    0x08001a48   Section        0  stm32f10x_flash.o(.text)
    .text                                    0x08002004   Section        0  misc.o(.text)
    .text                                    0x080020e0   Section       64  startup_stm32f10x_hd.o(.text)
    .text                                    0x08002120   Section        0  usart.o(.text)
    .text                                    0x080022d0   Section        0  delay.o(.text)
    .text                                    0x080023a4   Section        0  sys.o(.text)
    .text                                    0x080023b0   Section        2  use_no_semi_2.o(.text)
    .text                                    0x080023b4   Section        0  noretval__2printf.o(.text)
    .text                                    0x080023cc   Section        0  noretval__2sprintf.o(.text)
    .text                                    0x080023f4   Section        0  _printf_pad.o(.text)
    .text                                    0x08002442   Section        0  _printf_truncate.o(.text)
    .text                                    0x08002466   Section        0  _printf_str.o(.text)
    .text                                    0x080024b8   Section        0  _printf_dec.o(.text)
    .text                                    0x08002530   Section        0  _printf_wctomb.o(.text)
    .text                                    0x080025ec   Section        0  _printf_oct_int.o(.text)
    .text                                    0x0800263c   Section        0  _printf_hex_int.o(.text)
    .text                                    0x08002694   Section        0  __printf_flags_ss_wp.o(.text)
    .text                                    0x0800281c   Section        0  strstr.o(.text)
    .text                                    0x08002840   Section        0  heapauxi.o(.text)
    .text                                    0x08002846   Section        2  use_no_semi.o(.text)
    .text                                    0x08002848   Section        0  _printf_intcommon.o(.text)
    .text                                    0x080028fc   Section        0  _printf_char_common.o(.text)
    _printf_input_char                       0x080028fd   Thumb Code    10  _printf_char_common.o(.text)
    .text                                    0x0800292c   Section        0  _sputc.o(.text)
    .text                                    0x08002936   Section        0  _printf_char.o(.text)
    .text                                    0x08002964   Section        0  _printf_char_file.o(.text)
    .text                                    0x08002988   Section        0  _printf_wchar.o(.text)
    .text                                    0x080029b4   Section        0  _wcrtomb.o(.text)
    .text                                    0x080029f4   Section       16  rt_ctype_table.o(.text)
    .text                                    0x08002a04   Section        0  ferror.o(.text)
    .text                                    0x08002a0c   Section       74  sys_stackheap_outer.o(.text)
    .text                                    0x08002a58   Section        8  rt_locale_intlibspace.o(.text)
    .text                                    0x08002a60   Section        0  exit.o(.text)
    .text                                    0x08002a74   Section        8  libspace.o(.text)
    .text                                    0x08002a7c   Section      128  strcmpv7m.o(.text)
    i._is_digit                              0x08002afc   Section        0  __printf_wp.o(i._is_digit)
    locale$$code                             0x08002b0c   Section       44  lc_ctype_c.o(locale$$code)
    .constdata                               0x08002b38   Section        8  _printf_wctomb.o(.constdata)
    initial_mbstate                          0x08002b38   Data           8  _printf_wctomb.o(.constdata)
    .constdata                               0x08002b40   Section       40  _printf_hex_int.o(.constdata)
    uc_hextab                                0x08002b40   Data          20  _printf_hex_int.o(.constdata)
    lc_hextab                                0x08002b54   Data          20  _printf_hex_int.o(.constdata)
    .constdata                               0x08002b68   Section       17  __printf_flags_ss_wp.o(.constdata)
    maptable                                 0x08002b68   Data          17  __printf_flags_ss_wp.o(.constdata)
    locale$$data                             0x08002b9c   Section      272  lc_ctype_c.o(locale$$data)
    __lcctype_c_name                         0x08002ba0   Data           2  lc_ctype_c.o(locale$$data)
    __lcctype_c_start                        0x08002ba8   Data           0  lc_ctype_c.o(locale$$data)
    __lcctype_c_end                          0x08002cac   Data           0  lc_ctype_c.o(locale$$data)
    .data                                    0x20000000   Section       20  system_stm32f10x.o(.data)
    .data                                    0x20000014   Section       16  ec800.o(.data)
    .data                                    0x20000024   Section        1  aht20.o(.data)
    .data                                    0x20000026   Section        2  bsp_i2c.o(.data)
    RETRY_IN_MLSEC                           0x20000026   Data           2  bsp_i2c.o(.data)
    .data                                    0x20000028   Section       20  stm32f10x_rcc.o(.data)
    APBAHBPrescTable                         0x20000028   Data          16  stm32f10x_rcc.o(.data)
    ADCPrescTable                            0x20000038   Data           4  stm32f10x_rcc.o(.data)
    .data                                    0x2000003c   Section        8  usart.o(.data)
    .data                                    0x20000044   Section        4  delay.o(.data)
    fac_us                                   0x20000044   Data           1  delay.o(.data)
    fac_ms                                   0x20000046   Data           2  delay.o(.data)
    .bss                                     0x20000048   Section      100  usart.o(.bss)
    .bss                                     0x200000ac   Section       96  libspace.o(.bss)
    HEAP                                     0x20000110   Section      512  startup_stm32f10x_hd.o(HEAP)
    Heap_Mem                                 0x20000110   Data         512  startup_stm32f10x_hd.o(HEAP)
    STACK                                    0x20000310   Section     1024  startup_stm32f10x_hd.o(STACK)
    Stack_Mem                                0x20000310   Data        1024  startup_stm32f10x_hd.o(STACK)
    __initial_sp                             0x20000710   Data           0  startup_stm32f10x_hd.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$P$D$K$B$S$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OSPACE$ROPI$EBA8$UX$STANDARDLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    _printf_flags                            0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  printf_stubs.o ABSOLUTE
    __ARM_exceptions_init                     - Undefined Weak Reference
    __alloca_initialize                       - Undefined Weak Reference
    __arm_fini_                               - Undefined Weak Reference
    __arm_preinit_                            - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __sigvec_lookup                           - Undefined Weak Reference
    _atexit_init                              - Undefined Weak Reference
    _call_atexit_fns                          - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _fp_trap_init                             - Undefined Weak Reference
    _fp_trap_shutdown                         - Undefined Weak Reference
    _get_lc_collate                           - Undefined Weak Reference
    _get_lc_monetary                          - Undefined Weak Reference
    _get_lc_numeric                           - Undefined Weak Reference
    _get_lc_time                              - Undefined Weak Reference
    _getenv_init                              - Undefined Weak Reference
    _handle_redirection                       - Undefined Weak Reference
    _init_alloc                               - Undefined Weak Reference
    _init_user_alloc                          - Undefined Weak Reference
    _initio                                   - Undefined Weak Reference
    _mutex_acquire                            - Undefined Weak Reference
    _mutex_release                            - Undefined Weak Reference
    _printf_mbtowc                            - Undefined Weak Reference
    _printf_wc                                - Undefined Weak Reference
    _rand_init                                - Undefined Weak Reference
    _signal_finish                            - Undefined Weak Reference
    _signal_init                              - Undefined Weak Reference
    _terminate_alloc                          - Undefined Weak Reference
    _terminate_user_alloc                     - Undefined Weak Reference
    _terminateio                              - Undefined Weak Reference
    __Vectors_Size                           0x00000130   Number         0  startup_stm32f10x_hd.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f10x_hd.o(RESET)
    __Vectors_End                            0x08000130   Data           0  startup_stm32f10x_hd.o(RESET)
    __main                                   0x08000131   Thumb Code     8  __main.o(!!!main)
    __scatterload                            0x08000139   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_rt2                        0x08000139   Thumb Code    44  __scatter.o(!!!scatter)
    __scatterload_rt2_thumb_only             0x08000139   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_null                       0x08000147   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_copy                       0x0800016d   Thumb Code    26  __scatter_copy.o(!!handler_copy)
    __scatterload_zeroinit                   0x08000189   Thumb Code    28  __scatter_zi.o(!!handler_zi)
    _printf_i                                0x080001a5   Thumb Code     0  _printf_i.o(.ARM.Collect$$_printf_percent$$00000008)
    _printf_percent                          0x080001a5   Thumb Code     0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    _printf_d                                0x080001ab   Thumb Code     0  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    _printf_u                                0x080001b1   Thumb Code     0  _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A)
    _printf_o                                0x080001b7   Thumb Code     0  _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B)
    _printf_x                                0x080001bd   Thumb Code     0  _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C)
    _printf_l                                0x080001c3   Thumb Code     0  _printf_l.o(.ARM.Collect$$_printf_percent$$00000012)
    _printf_c                                0x080001cd   Thumb Code     0  _printf_c.o(.ARM.Collect$$_printf_percent$$00000013)
    _printf_s                                0x080001d3   Thumb Code     0  _printf_s.o(.ARM.Collect$$_printf_percent$$00000014)
    _printf_lc                               0x080001d9   Thumb Code     0  _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015)
    _printf_percent_end                      0x080001df   Thumb Code     0  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    __rt_lib_init                            0x080001e3   Thumb Code     0  libinit.o(.ARM.Collect$$libinit$$00000000)
    __rt_lib_init_fp_1                       0x080001e5   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000002)
    __rt_lib_init_heap_1                     0x080001e5   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    __rt_lib_init_lc_common                  0x080001e5   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000F)
    __rt_lib_init_preinit_1                  0x080001e5   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    __rt_lib_init_rand_1                     0x080001e5   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    __rt_lib_init_user_alloc_1               0x080001e5   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    __rt_lib_init_lc_collate_1               0x080001eb   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    __rt_lib_init_lc_ctype_2                 0x080001eb   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000012)
    __rt_lib_init_alloca_1                   0x080001f7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    __rt_lib_init_argv_1                     0x080001f7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    __rt_lib_init_atexit_1                   0x080001f7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    __rt_lib_init_clock_1                    0x080001f7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    __rt_lib_init_cpp_1                      0x080001f7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    __rt_lib_init_exceptions_1               0x080001f7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    __rt_lib_init_fp_trap_1                  0x080001f7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    __rt_lib_init_getenv_1                   0x080001f7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    __rt_lib_init_lc_ctype_1                 0x080001f7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    __rt_lib_init_lc_monetary_1              0x080001f7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    __rt_lib_init_lc_numeric_1               0x080001f7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    __rt_lib_init_lc_time_1                  0x080001f7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    __rt_lib_init_return                     0x080001f7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000033)
    __rt_lib_init_signal_1                   0x080001f7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    __rt_lib_init_stdio_1                    0x080001f7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    __rt_lib_shutdown                        0x080001f9   Thumb Code     0  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    __rt_lib_shutdown_cpp_1                  0x080001fb   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    __rt_lib_shutdown_fini_1                 0x080001fb   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    __rt_lib_shutdown_fp_trap_1              0x080001fb   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000009)
    __rt_lib_shutdown_heap_1                 0x080001fb   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000011)
    __rt_lib_shutdown_return                 0x080001fb   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000012)
    __rt_lib_shutdown_signal_1               0x080001fb   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    __rt_lib_shutdown_stdio_1                0x080001fb   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000006)
    __rt_lib_shutdown_user_alloc_1           0x080001fb   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E)
    __rt_entry                               0x080001fd   Thumb Code     0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    __rt_entry_presh_1                       0x080001fd   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    __rt_entry_sh                            0x080001fd   Thumb Code     0  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    __rt_entry_li                            0x08000203   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    __rt_entry_postsh_1                      0x08000203   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    __rt_entry_main                          0x08000207   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    __rt_entry_postli_1                      0x08000207   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    __rt_exit                                0x0800020f   Thumb Code     0  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    __rt_exit_ls                             0x08000211   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    __rt_exit_prels_1                        0x08000211   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    __rt_exit_exit                           0x08000215   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    main                                     0x0800021d   Thumb Code   236  main.o(.text)
    NMI_Handler                              0x08000335   Thumb Code     2  stm32f10x_it.o(.text)
    HardFault_Handler                        0x08000337   Thumb Code     4  stm32f10x_it.o(.text)
    MemManage_Handler                        0x0800033b   Thumb Code     4  stm32f10x_it.o(.text)
    BusFault_Handler                         0x0800033f   Thumb Code     4  stm32f10x_it.o(.text)
    UsageFault_Handler                       0x08000343   Thumb Code     4  stm32f10x_it.o(.text)
    SVC_Handler                              0x08000347   Thumb Code     2  stm32f10x_it.o(.text)
    DebugMon_Handler                         0x08000349   Thumb Code     2  stm32f10x_it.o(.text)
    PendSV_Handler                           0x0800034b   Thumb Code     2  stm32f10x_it.o(.text)
    SysTick_Handler                          0x0800034d   Thumb Code     2  stm32f10x_it.o(.text)
    RCC_Configuration                        0x08000351   Thumb Code   110  system_stm32f10x.o(.text)
    SystemInit                               0x0800049d   Thumb Code    78  system_stm32f10x.o(.text)
    SystemCoreClockUpdate                    0x080004eb   Thumb Code   142  system_stm32f10x.o(.text)
    LED_Init                                 0x0800059d   Thumb Code    86  led.o(.text)
    IWDG_Init                                0x08000601   Thumb Code    36  wdg.o(.text)
    IWDG_Feed                                0x08000625   Thumb Code     8  wdg.o(.text)
    Uart1_SendStr                            0x0800062d   Thumb Code    34  ec800.o(.text)
    Clear_Buffer                             0x0800064f   Thumb Code    42  ec800.o(.text)
    EC800_Init                               0x08000679   Thumb Code   446  ec800.o(.text)
    EC800Send_StrData                        0x08000837   Thumb Code   110  ec800.o(.text)
    AHT20_Read_Status                        0x080009b9   Thumb Code    20  aht20.o(.text)
    AHT20_Read_Cal_Enable                    0x080009cd   Thumb Code    26  aht20.o(.text)
    AHT20_Read_CTdata                        0x080009e7   Thumb Code   150  aht20.o(.text)
    AHT20_Init                               0x08000a7d   Thumb Code   120  aht20.o(.text)
    Set_I2C_Retry                            0x08000b03   Thumb Code     6  bsp_i2c.o(.text)
    Get_I2C_Retry                            0x08000b09   Thumb Code     6  bsp_i2c.o(.text)
    I2C_Bus_Init                             0x08000b4b   Thumb Code    14  bsp_i2c.o(.text)
    Soft_I2C_Wait_Ack                        0x08000c3d   Thumb Code    74  bsp_i2c.o(.text)
    Sensors_I2C_WriteRegister                0x08000e5b   Thumb Code    76  bsp_i2c.o(.text)
    Sensors_I2C_ReadRegister                 0x08000ea7   Thumb Code    90  bsp_i2c.o(.text)
    GPIO_DeInit                              0x08000f01   Thumb Code   172  stm32f10x_gpio.o(.text)
    GPIO_AFIODeInit                          0x08000fad   Thumb Code    20  stm32f10x_gpio.o(.text)
    GPIO_Init                                0x08000fc1   Thumb Code   278  stm32f10x_gpio.o(.text)
    GPIO_StructInit                          0x080010d7   Thumb Code    16  stm32f10x_gpio.o(.text)
    GPIO_ReadInputDataBit                    0x080010e7   Thumb Code    18  stm32f10x_gpio.o(.text)
    GPIO_ReadInputData                       0x080010f9   Thumb Code     8  stm32f10x_gpio.o(.text)
    GPIO_ReadOutputDataBit                   0x08001101   Thumb Code    18  stm32f10x_gpio.o(.text)
    GPIO_ReadOutputData                      0x08001113   Thumb Code     8  stm32f10x_gpio.o(.text)
    GPIO_SetBits                             0x0800111b   Thumb Code     4  stm32f10x_gpio.o(.text)
    GPIO_ResetBits                           0x0800111f   Thumb Code     4  stm32f10x_gpio.o(.text)
    GPIO_WriteBit                            0x08001123   Thumb Code    10  stm32f10x_gpio.o(.text)
    GPIO_Write                               0x0800112d   Thumb Code     4  stm32f10x_gpio.o(.text)
    GPIO_PinLockConfig                       0x08001131   Thumb Code    18  stm32f10x_gpio.o(.text)
    GPIO_EventOutputConfig                   0x08001143   Thumb Code    26  stm32f10x_gpio.o(.text)
    GPIO_EventOutputCmd                      0x0800115d   Thumb Code     6  stm32f10x_gpio.o(.text)
    GPIO_PinRemapConfig                      0x08001163   Thumb Code   138  stm32f10x_gpio.o(.text)
    GPIO_EXTILineConfig                      0x080011ed   Thumb Code    66  stm32f10x_gpio.o(.text)
    GPIO_ETH_MediaInterfaceConfig            0x0800122f   Thumb Code     8  stm32f10x_gpio.o(.text)
    IWDG_WriteAccessCmd                      0x0800125d   Thumb Code     6  stm32f10x_iwdg.o(.text)
    IWDG_SetPrescaler                        0x08001263   Thumb Code     6  stm32f10x_iwdg.o(.text)
    IWDG_SetReload                           0x08001269   Thumb Code     6  stm32f10x_iwdg.o(.text)
    IWDG_ReloadCounter                       0x0800126f   Thumb Code    10  stm32f10x_iwdg.o(.text)
    IWDG_Enable                              0x08001279   Thumb Code    10  stm32f10x_iwdg.o(.text)
    IWDG_GetFlagStatus                       0x08001283   Thumb Code    20  stm32f10x_iwdg.o(.text)
    USART_DeInit                             0x0800129d   Thumb Code   134  stm32f10x_usart.o(.text)
    USART_Init                               0x08001323   Thumb Code   210  stm32f10x_usart.o(.text)
    USART_StructInit                         0x080013f5   Thumb Code    24  stm32f10x_usart.o(.text)
    USART_ClockInit                          0x0800140d   Thumb Code    34  stm32f10x_usart.o(.text)
    USART_ClockStructInit                    0x0800142f   Thumb Code    12  stm32f10x_usart.o(.text)
    USART_Cmd                                0x0800143b   Thumb Code    24  stm32f10x_usart.o(.text)
    USART_ITConfig                           0x08001453   Thumb Code    74  stm32f10x_usart.o(.text)
    USART_DMACmd                             0x0800149d   Thumb Code    18  stm32f10x_usart.o(.text)
    USART_SetAddress                         0x080014af   Thumb Code    18  stm32f10x_usart.o(.text)
    USART_WakeUpConfig                       0x080014c1   Thumb Code    18  stm32f10x_usart.o(.text)
    USART_ReceiverWakeUpCmd                  0x080014d3   Thumb Code    24  stm32f10x_usart.o(.text)
    USART_LINBreakDetectLengthConfig         0x080014eb   Thumb Code    18  stm32f10x_usart.o(.text)
    USART_LINCmd                             0x080014fd   Thumb Code    24  stm32f10x_usart.o(.text)
    USART_SendData                           0x08001515   Thumb Code     8  stm32f10x_usart.o(.text)
    USART_ReceiveData                        0x0800151d   Thumb Code    10  stm32f10x_usart.o(.text)
    USART_SendBreak                          0x08001527   Thumb Code    10  stm32f10x_usart.o(.text)
    USART_SetGuardTime                       0x08001531   Thumb Code    16  stm32f10x_usart.o(.text)
    USART_SetPrescaler                       0x08001541   Thumb Code    16  stm32f10x_usart.o(.text)
    USART_SmartCardCmd                       0x08001551   Thumb Code    24  stm32f10x_usart.o(.text)
    USART_SmartCardNACKCmd                   0x08001569   Thumb Code    24  stm32f10x_usart.o(.text)
    USART_HalfDuplexCmd                      0x08001581   Thumb Code    24  stm32f10x_usart.o(.text)
    USART_OverSampling8Cmd                   0x08001599   Thumb Code    22  stm32f10x_usart.o(.text)
    USART_OneBitMethodCmd                    0x080015af   Thumb Code    24  stm32f10x_usart.o(.text)
    USART_IrDAConfig                         0x080015c7   Thumb Code    18  stm32f10x_usart.o(.text)
    USART_IrDACmd                            0x080015d9   Thumb Code    24  stm32f10x_usart.o(.text)
    USART_GetFlagStatus                      0x080015f1   Thumb Code    26  stm32f10x_usart.o(.text)
    USART_ClearFlag                          0x0800160b   Thumb Code    18  stm32f10x_usart.o(.text)
    USART_GetITStatus                        0x0800161d   Thumb Code    84  stm32f10x_usart.o(.text)
    USART_ClearITPendingBit                  0x08001671   Thumb Code    52  stm32f10x_usart.o(.text)
    RCC_DeInit                               0x080016a5   Thumb Code    64  stm32f10x_rcc.o(.text)
    RCC_HSEConfig                            0x080016e5   Thumb Code    70  stm32f10x_rcc.o(.text)
    RCC_GetFlagStatus                        0x0800172b   Thumb Code    56  stm32f10x_rcc.o(.text)
    RCC_WaitForHSEStartUp                    0x08001763   Thumb Code    56  stm32f10x_rcc.o(.text)
    RCC_AdjustHSICalibrationValue            0x0800179b   Thumb Code    20  stm32f10x_rcc.o(.text)
    RCC_HSICmd                               0x080017af   Thumb Code     6  stm32f10x_rcc.o(.text)
    RCC_PLLConfig                            0x080017b5   Thumb Code    24  stm32f10x_rcc.o(.text)
    RCC_PLLCmd                               0x080017cd   Thumb Code     6  stm32f10x_rcc.o(.text)
    RCC_SYSCLKConfig                         0x080017d3   Thumb Code    18  stm32f10x_rcc.o(.text)
    RCC_GetSYSCLKSource                      0x080017e5   Thumb Code    10  stm32f10x_rcc.o(.text)
    RCC_HCLKConfig                           0x080017ef   Thumb Code    18  stm32f10x_rcc.o(.text)
    RCC_PCLK1Config                          0x08001801   Thumb Code    18  stm32f10x_rcc.o(.text)
    RCC_PCLK2Config                          0x08001813   Thumb Code    20  stm32f10x_rcc.o(.text)
    RCC_ITConfig                             0x08001827   Thumb Code    26  stm32f10x_rcc.o(.text)
    RCC_USBCLKConfig                         0x08001841   Thumb Code     8  stm32f10x_rcc.o(.text)
    RCC_ADCCLKConfig                         0x08001849   Thumb Code    18  stm32f10x_rcc.o(.text)
    RCC_LSEConfig                            0x0800185b   Thumb Code    50  stm32f10x_rcc.o(.text)
    RCC_LSICmd                               0x0800188d   Thumb Code     6  stm32f10x_rcc.o(.text)
    RCC_RTCCLKConfig                         0x08001893   Thumb Code    12  stm32f10x_rcc.o(.text)
    RCC_RTCCLKCmd                            0x0800189f   Thumb Code     8  stm32f10x_rcc.o(.text)
    RCC_GetClocksFreq                        0x080018a7   Thumb Code   192  stm32f10x_rcc.o(.text)
    RCC_AHBPeriphClockCmd                    0x08001967   Thumb Code    26  stm32f10x_rcc.o(.text)
    RCC_APB2PeriphClockCmd                   0x08001981   Thumb Code    26  stm32f10x_rcc.o(.text)
    RCC_APB1PeriphClockCmd                   0x0800199b   Thumb Code    26  stm32f10x_rcc.o(.text)
    RCC_APB2PeriphResetCmd                   0x080019b5   Thumb Code    26  stm32f10x_rcc.o(.text)
    RCC_APB1PeriphResetCmd                   0x080019cf   Thumb Code    26  stm32f10x_rcc.o(.text)
    RCC_BackupResetCmd                       0x080019e9   Thumb Code     8  stm32f10x_rcc.o(.text)
    RCC_ClockSecuritySystemCmd               0x080019f1   Thumb Code     6  stm32f10x_rcc.o(.text)
    RCC_MCOConfig                            0x080019f7   Thumb Code     6  stm32f10x_rcc.o(.text)
    RCC_ClearFlag                            0x080019fd   Thumb Code    14  stm32f10x_rcc.o(.text)
    RCC_GetITStatus                          0x08001a0b   Thumb Code    20  stm32f10x_rcc.o(.text)
    RCC_ClearITPendingBit                    0x08001a1f   Thumb Code     6  stm32f10x_rcc.o(.text)
    FLASH_SetLatency                         0x08001a49   Thumb Code    18  stm32f10x_flash.o(.text)
    FLASH_HalfCycleAccessCmd                 0x08001a5b   Thumb Code    22  stm32f10x_flash.o(.text)
    FLASH_PrefetchBufferCmd                  0x08001a71   Thumb Code    22  stm32f10x_flash.o(.text)
    FLASH_Unlock                             0x08001a87   Thumb Code    12  stm32f10x_flash.o(.text)
    FLASH_UnlockBank1                        0x08001a93   Thumb Code    12  stm32f10x_flash.o(.text)
    FLASH_Lock                               0x08001a9f   Thumb Code    14  stm32f10x_flash.o(.text)
    FLASH_LockBank1                          0x08001aad   Thumb Code    14  stm32f10x_flash.o(.text)
    FLASH_GetBank1Status                     0x08001abb   Thumb Code    48  stm32f10x_flash.o(.text)
    FLASH_WaitForLastOperation               0x08001aeb   Thumb Code    38  stm32f10x_flash.o(.text)
    FLASH_ErasePage                          0x08001b11   Thumb Code    72  stm32f10x_flash.o(.text)
    FLASH_EraseAllPages                      0x08001b59   Thumb Code    68  stm32f10x_flash.o(.text)
    FLASH_WaitForLastBank1Operation          0x08001b9d   Thumb Code    38  stm32f10x_flash.o(.text)
    FLASH_EraseAllBank1Pages                 0x08001bc3   Thumb Code    68  stm32f10x_flash.o(.text)
    FLASH_GetReadOutProtectionStatus         0x08001c07   Thumb Code    20  stm32f10x_flash.o(.text)
    FLASH_EraseOptionBytes                   0x08001c1b   Thumb Code   150  stm32f10x_flash.o(.text)
    FLASH_ProgramWord                        0x08001cb1   Thumb Code   102  stm32f10x_flash.o(.text)
    FLASH_ProgramHalfWord                    0x08001d17   Thumb Code    60  stm32f10x_flash.o(.text)
    FLASH_ProgramOptionByteData              0x08001d53   Thumb Code    72  stm32f10x_flash.o(.text)
    FLASH_EnableWriteProtection              0x08001d9b   Thumb Code   200  stm32f10x_flash.o(.text)
    FLASH_ReadOutProtection                  0x08001e63   Thumb Code   156  stm32f10x_flash.o(.text)
    FLASH_UserOptionByteConfig               0x08001eff   Thumb Code    88  stm32f10x_flash.o(.text)
    FLASH_GetUserOptionByte                  0x08001f57   Thumb Code     8  stm32f10x_flash.o(.text)
    FLASH_GetWriteProtectionOptionByte       0x08001f5f   Thumb Code     6  stm32f10x_flash.o(.text)
    FLASH_GetPrefetchBufferStatus            0x08001f65   Thumb Code    20  stm32f10x_flash.o(.text)
    FLASH_ITConfig                           0x08001f79   Thumb Code    26  stm32f10x_flash.o(.text)
    FLASH_GetFlagStatus                      0x08001f93   Thumb Code    42  stm32f10x_flash.o(.text)
    FLASH_ClearFlag                          0x08001fbd   Thumb Code     6  stm32f10x_flash.o(.text)
    FLASH_GetStatus                          0x08001fc3   Thumb Code    48  stm32f10x_flash.o(.text)
    NVIC_PriorityGroupConfig                 0x08002005   Thumb Code    10  misc.o(.text)
    NVIC_Init                                0x0800200f   Thumb Code   100  misc.o(.text)
    NVIC_SetVectorTable                      0x08002073   Thumb Code    14  misc.o(.text)
    NVIC_SystemLPConfig                      0x08002081   Thumb Code    34  misc.o(.text)
    SysTick_CLKSourceConfig                  0x080020a3   Thumb Code    40  misc.o(.text)
    Reset_Handler                            0x080020e1   Thumb Code     8  startup_stm32f10x_hd.o(.text)
    ADC1_2_IRQHandler                        0x080020fb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    ADC3_IRQHandler                          0x080020fb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    CAN1_RX1_IRQHandler                      0x080020fb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    CAN1_SCE_IRQHandler                      0x080020fb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel1_IRQHandler                 0x080020fb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel2_IRQHandler                 0x080020fb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel3_IRQHandler                 0x080020fb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel4_IRQHandler                 0x080020fb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel5_IRQHandler                 0x080020fb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel6_IRQHandler                 0x080020fb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel7_IRQHandler                 0x080020fb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel1_IRQHandler                 0x080020fb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel2_IRQHandler                 0x080020fb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel3_IRQHandler                 0x080020fb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel4_5_IRQHandler               0x080020fb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI0_IRQHandler                         0x080020fb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI15_10_IRQHandler                     0x080020fb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI1_IRQHandler                         0x080020fb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI2_IRQHandler                         0x080020fb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI3_IRQHandler                         0x080020fb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI4_IRQHandler                         0x080020fb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI9_5_IRQHandler                       0x080020fb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    FLASH_IRQHandler                         0x080020fb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    FSMC_IRQHandler                          0x080020fb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C1_ER_IRQHandler                       0x080020fb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C1_EV_IRQHandler                       0x080020fb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C2_ER_IRQHandler                       0x080020fb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C2_EV_IRQHandler                       0x080020fb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    PVD_IRQHandler                           0x080020fb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    RCC_IRQHandler                           0x080020fb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    RTCAlarm_IRQHandler                      0x080020fb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    RTC_IRQHandler                           0x080020fb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SDIO_IRQHandler                          0x080020fb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SPI1_IRQHandler                          0x080020fb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SPI2_IRQHandler                          0x080020fb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SPI3_IRQHandler                          0x080020fb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TAMPER_IRQHandler                        0x080020fb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_BRK_IRQHandler                      0x080020fb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_CC_IRQHandler                       0x080020fb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_TRG_COM_IRQHandler                  0x080020fb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_UP_IRQHandler                       0x080020fb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM2_IRQHandler                          0x080020fb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM3_IRQHandler                          0x080020fb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM4_IRQHandler                          0x080020fb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM5_IRQHandler                          0x080020fb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM6_IRQHandler                          0x080020fb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM7_IRQHandler                          0x080020fb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_BRK_IRQHandler                      0x080020fb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_CC_IRQHandler                       0x080020fb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_TRG_COM_IRQHandler                  0x080020fb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_UP_IRQHandler                       0x080020fb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    UART4_IRQHandler                         0x080020fb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    UART5_IRQHandler                         0x080020fb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USART3_IRQHandler                        0x080020fb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USBWakeUp_IRQHandler                     0x080020fb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USB_HP_CAN1_TX_IRQHandler                0x080020fb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USB_LP_CAN1_RX0_IRQHandler               0x080020fb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    WWDG_IRQHandler                          0x080020fb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    __user_initial_stackheap                 0x080020fd   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    _sys_exit                                0x08002121   Thumb Code     4  usart.o(.text)
    fputc                                    0x08002125   Thumb Code    24  usart.o(.text)
    uart_init                                0x0800213d   Thumb Code   158  usart.o(.text)
    uart2_init                               0x080021db   Thumb Code   160  usart.o(.text)
    USART1_IRQHandler                        0x0800227b   Thumb Code    24  usart.o(.text)
    USART2_IRQHandler                        0x08002293   Thumb Code    40  usart.o(.text)
    delay_init                               0x080022d1   Thumb Code    50  delay.o(.text)
    delay_us                                 0x08002303   Thumb Code    72  delay.o(.text)
    delay_ms                                 0x0800234b   Thumb Code    72  delay.o(.text)
    NVIC_Configuration                       0x080023a5   Thumb Code    12  sys.o(.text)
    __use_no_semihosting                     0x080023b1   Thumb Code     2  use_no_semi_2.o(.text)
    __2printf                                0x080023b5   Thumb Code    20  noretval__2printf.o(.text)
    __2sprintf                               0x080023cd   Thumb Code    34  noretval__2sprintf.o(.text)
    _printf_pre_padding                      0x080023f5   Thumb Code    44  _printf_pad.o(.text)
    _printf_post_padding                     0x08002421   Thumb Code    34  _printf_pad.o(.text)
    _printf_truncate_signed                  0x08002443   Thumb Code    18  _printf_truncate.o(.text)
    _printf_truncate_unsigned                0x08002455   Thumb Code    18  _printf_truncate.o(.text)
    _printf_str                              0x08002467   Thumb Code    82  _printf_str.o(.text)
    _printf_int_dec                          0x080024b9   Thumb Code   104  _printf_dec.o(.text)
    _printf_wctomb                           0x08002531   Thumb Code   182  _printf_wctomb.o(.text)
    _printf_int_oct                          0x080025ed   Thumb Code    72  _printf_oct_int.o(.text)
    _printf_longlong_oct                     0x080025ed   Thumb Code     0  _printf_oct_int.o(.text)
    _printf_int_hex                          0x0800263d   Thumb Code    84  _printf_hex_int.o(.text)
    _printf_longlong_hex                     0x0800263d   Thumb Code     0  _printf_hex_int.o(.text)
    __printf                                 0x08002695   Thumb Code   388  __printf_flags_ss_wp.o(.text)
    strstr                                   0x0800281d   Thumb Code    36  strstr.o(.text)
    __use_two_region_memory                  0x08002841   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_escrow$2region                 0x08002843   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_expand$2region                 0x08002845   Thumb Code     2  heapauxi.o(.text)
    __I$use$semihosting                      0x08002847   Thumb Code     0  use_no_semi.o(.text)
    __use_no_semihosting_swi                 0x08002847   Thumb Code     2  use_no_semi.o(.text)
    _printf_int_common                       0x08002849   Thumb Code   178  _printf_intcommon.o(.text)
    _printf_char_common                      0x08002907   Thumb Code    32  _printf_char_common.o(.text)
    _sputc                                   0x0800292d   Thumb Code    10  _sputc.o(.text)
    _printf_cs_common                        0x08002937   Thumb Code    20  _printf_char.o(.text)
    _printf_char                             0x0800294b   Thumb Code    16  _printf_char.o(.text)
    _printf_string                           0x0800295b   Thumb Code     8  _printf_char.o(.text)
    _printf_char_file                        0x08002965   Thumb Code    32  _printf_char_file.o(.text)
    _printf_lcs_common                       0x08002989   Thumb Code    20  _printf_wchar.o(.text)
    _printf_wchar                            0x0800299d   Thumb Code    16  _printf_wchar.o(.text)
    _printf_wstring                          0x080029ad   Thumb Code     8  _printf_wchar.o(.text)
    _wcrtomb                                 0x080029b5   Thumb Code    64  _wcrtomb.o(.text)
    __rt_ctype_table                         0x080029f5   Thumb Code    16  rt_ctype_table.o(.text)
    ferror                                   0x08002a05   Thumb Code     8  ferror.o(.text)
    __user_setup_stackheap                   0x08002a0d   Thumb Code    74  sys_stackheap_outer.o(.text)
    __rt_locale                              0x08002a59   Thumb Code     8  rt_locale_intlibspace.o(.text)
    exit                                     0x08002a61   Thumb Code    18  exit.o(.text)
    __user_libspace                          0x08002a75   Thumb Code     8  libspace.o(.text)
    __user_perproc_libspace                  0x08002a75   Thumb Code     0  libspace.o(.text)
    __user_perthread_libspace                0x08002a75   Thumb Code     0  libspace.o(.text)
    strcmp                                   0x08002a7d   Thumb Code   128  strcmpv7m.o(.text)
    _is_digit                                0x08002afd   Thumb Code    14  __printf_wp.o(i._is_digit)
    _get_lc_ctype                            0x08002b0d   Thumb Code    44  lc_ctype_c.o(locale$$code)
    Region$$Table$$Base                      0x08002b7c   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x08002b9c   Number         0  anon$$obj.o(Region$$Table)
    __ctype                                  0x08002ba9   Data           0  lc_ctype_c.o(locale$$data)
    SystemCoreClock                          0x20000000   Data           4  system_stm32f10x.o(.data)
    AHBPrescTable                            0x20000004   Data          16  system_stm32f10x.o(.data)
    strx                                     0x20000014   Data           4  ec800.o(.data)
    extstrx                                  0x20000018   Data           4  ec800.o(.data)
    Readystrx                                0x2000001c   Data           4  ec800.o(.data)
    Errstrx                                  0x20000020   Data           4  ec800.o(.data)
    count                                    0x20000024   Data           1  aht20.o(.data)
    __stdout                                 0x2000003c   Data           4  usart.o(.data)
    RxCounter                                0x20000040   Data           1  usart.o(.data)
    USART_RX_STA                             0x20000042   Data           2  usart.o(.data)
    RxBuffer                                 0x20000048   Data         100  usart.o(.bss)
    __libspace_start                         0x200000ac   Data          96  libspace.o(.bss)
    __temporary_stack_top$libspace           0x2000010c   Data           0  libspace.o(.bss)



==============================================================================

Memory Map of the image

  Image Entry point : 0x08000131

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x00002cf4, Max: 0x00010000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x00002cac, Max: 0x00010000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x00000130   Data   RO          372    RESET               startup_stm32f10x_hd.o
    0x08000130   0x08000130   0x00000008   Code   RO          508  * !!!main             c_w.l(__main.o)
    0x08000138   0x08000138   0x00000034   Code   RO          699    !!!scatter          c_w.l(__scatter.o)
    0x0800016c   0x0800016c   0x0000001a   Code   RO          701    !!handler_copy      c_w.l(__scatter_copy.o)
    0x08000186   0x08000186   0x00000002   PAD
    0x08000188   0x08000188   0x0000001c   Code   RO          703    !!handler_zi        c_w.l(__scatter_zi.o)
    0x080001a4   0x080001a4   0x00000000   Code   RO          502    .ARM.Collect$$_printf_percent$$00000000  c_w.l(_printf_percent.o)
    0x080001a4   0x080001a4   0x00000006   Code   RO          499    .ARM.Collect$$_printf_percent$$00000008  c_w.l(_printf_i.o)
    0x080001aa   0x080001aa   0x00000006   Code   RO          500    .ARM.Collect$$_printf_percent$$00000009  c_w.l(_printf_d.o)
    0x080001b0   0x080001b0   0x00000006   Code   RO          501    .ARM.Collect$$_printf_percent$$0000000A  c_w.l(_printf_u.o)
    0x080001b6   0x080001b6   0x00000006   Code   RO          498    .ARM.Collect$$_printf_percent$$0000000B  c_w.l(_printf_o.o)
    0x080001bc   0x080001bc   0x00000006   Code   RO          497    .ARM.Collect$$_printf_percent$$0000000C  c_w.l(_printf_x.o)
    0x080001c2   0x080001c2   0x0000000a   Code   RO          526    .ARM.Collect$$_printf_percent$$00000012  c_w.l(_printf_l.o)
    0x080001cc   0x080001cc   0x00000006   Code   RO          495    .ARM.Collect$$_printf_percent$$00000013  c_w.l(_printf_c.o)
    0x080001d2   0x080001d2   0x00000006   Code   RO          496    .ARM.Collect$$_printf_percent$$00000014  c_w.l(_printf_s.o)
    0x080001d8   0x080001d8   0x00000006   Code   RO          503    .ARM.Collect$$_printf_percent$$00000015  c_w.l(_printf_lc.o)
    0x080001de   0x080001de   0x00000004   Code   RO          525    .ARM.Collect$$_printf_percent$$00000017  c_w.l(_printf_percent_end.o)
    0x080001e2   0x080001e2   0x00000002   Code   RO          569    .ARM.Collect$$libinit$$00000000  c_w.l(libinit.o)
    0x080001e4   0x080001e4   0x00000000   Code   RO          578    .ARM.Collect$$libinit$$00000002  c_w.l(libinit2.o)
    0x080001e4   0x080001e4   0x00000000   Code   RO          580    .ARM.Collect$$libinit$$00000004  c_w.l(libinit2.o)
    0x080001e4   0x080001e4   0x00000000   Code   RO          583    .ARM.Collect$$libinit$$0000000A  c_w.l(libinit2.o)
    0x080001e4   0x080001e4   0x00000000   Code   RO          585    .ARM.Collect$$libinit$$0000000C  c_w.l(libinit2.o)
    0x080001e4   0x080001e4   0x00000000   Code   RO          587    .ARM.Collect$$libinit$$0000000E  c_w.l(libinit2.o)
    0x080001e4   0x080001e4   0x00000006   Code   RO          588    .ARM.Collect$$libinit$$0000000F  c_w.l(libinit2.o)
    0x080001ea   0x080001ea   0x00000000   Code   RO          590    .ARM.Collect$$libinit$$00000011  c_w.l(libinit2.o)
    0x080001ea   0x080001ea   0x0000000c   Code   RO          591    .ARM.Collect$$libinit$$00000012  c_w.l(libinit2.o)
    0x080001f6   0x080001f6   0x00000000   Code   RO          592    .ARM.Collect$$libinit$$00000013  c_w.l(libinit2.o)
    0x080001f6   0x080001f6   0x00000000   Code   RO          594    .ARM.Collect$$libinit$$00000015  c_w.l(libinit2.o)
    0x080001f6   0x080001f6   0x00000000   Code   RO          596    .ARM.Collect$$libinit$$00000017  c_w.l(libinit2.o)
    0x080001f6   0x080001f6   0x00000000   Code   RO          598    .ARM.Collect$$libinit$$00000019  c_w.l(libinit2.o)
    0x080001f6   0x080001f6   0x00000000   Code   RO          600    .ARM.Collect$$libinit$$0000001B  c_w.l(libinit2.o)
    0x080001f6   0x080001f6   0x00000000   Code   RO          602    .ARM.Collect$$libinit$$0000001D  c_w.l(libinit2.o)
    0x080001f6   0x080001f6   0x00000000   Code   RO          604    .ARM.Collect$$libinit$$0000001F  c_w.l(libinit2.o)
    0x080001f6   0x080001f6   0x00000000   Code   RO          606    .ARM.Collect$$libinit$$00000021  c_w.l(libinit2.o)
    0x080001f6   0x080001f6   0x00000000   Code   RO          608    .ARM.Collect$$libinit$$00000023  c_w.l(libinit2.o)
    0x080001f6   0x080001f6   0x00000000   Code   RO          610    .ARM.Collect$$libinit$$00000025  c_w.l(libinit2.o)
    0x080001f6   0x080001f6   0x00000000   Code   RO          614    .ARM.Collect$$libinit$$0000002C  c_w.l(libinit2.o)
    0x080001f6   0x080001f6   0x00000000   Code   RO          616    .ARM.Collect$$libinit$$0000002E  c_w.l(libinit2.o)
    0x080001f6   0x080001f6   0x00000000   Code   RO          618    .ARM.Collect$$libinit$$00000030  c_w.l(libinit2.o)
    0x080001f6   0x080001f6   0x00000000   Code   RO          620    .ARM.Collect$$libinit$$00000032  c_w.l(libinit2.o)
    0x080001f6   0x080001f6   0x00000002   Code   RO          621    .ARM.Collect$$libinit$$00000033  c_w.l(libinit2.o)
    0x080001f8   0x080001f8   0x00000002   Code   RO          639    .ARM.Collect$$libshutdown$$00000000  c_w.l(libshutdown.o)
    0x080001fa   0x080001fa   0x00000000   Code   RO          649    .ARM.Collect$$libshutdown$$00000002  c_w.l(libshutdown2.o)
    0x080001fa   0x080001fa   0x00000000   Code   RO          651    .ARM.Collect$$libshutdown$$00000004  c_w.l(libshutdown2.o)
    0x080001fa   0x080001fa   0x00000000   Code   RO          653    .ARM.Collect$$libshutdown$$00000006  c_w.l(libshutdown2.o)
    0x080001fa   0x080001fa   0x00000000   Code   RO          656    .ARM.Collect$$libshutdown$$00000009  c_w.l(libshutdown2.o)
    0x080001fa   0x080001fa   0x00000000   Code   RO          659    .ARM.Collect$$libshutdown$$0000000C  c_w.l(libshutdown2.o)
    0x080001fa   0x080001fa   0x00000000   Code   RO          661    .ARM.Collect$$libshutdown$$0000000E  c_w.l(libshutdown2.o)
    0x080001fa   0x080001fa   0x00000000   Code   RO          664    .ARM.Collect$$libshutdown$$00000011  c_w.l(libshutdown2.o)
    0x080001fa   0x080001fa   0x00000002   Code   RO          665    .ARM.Collect$$libshutdown$$00000012  c_w.l(libshutdown2.o)
    0x080001fc   0x080001fc   0x00000000   Code   RO          512    .ARM.Collect$$rtentry$$00000000  c_w.l(__rtentry.o)
    0x080001fc   0x080001fc   0x00000000   Code   RO          530    .ARM.Collect$$rtentry$$00000002  c_w.l(__rtentry2.o)
    0x080001fc   0x080001fc   0x00000006   Code   RO          542    .ARM.Collect$$rtentry$$00000004  c_w.l(__rtentry4.o)
    0x08000202   0x08000202   0x00000000   Code   RO          532    .ARM.Collect$$rtentry$$00000009  c_w.l(__rtentry2.o)
    0x08000202   0x08000202   0x00000004   Code   RO          533    .ARM.Collect$$rtentry$$0000000A  c_w.l(__rtentry2.o)
    0x08000206   0x08000206   0x00000000   Code   RO          535    .ARM.Collect$$rtentry$$0000000C  c_w.l(__rtentry2.o)
    0x08000206   0x08000206   0x00000008   Code   RO          536    .ARM.Collect$$rtentry$$0000000D  c_w.l(__rtentry2.o)
    0x0800020e   0x0800020e   0x00000002   Code   RO          573    .ARM.Collect$$rtexit$$00000000  c_w.l(rtexit.o)
    0x08000210   0x08000210   0x00000000   Code   RO          623    .ARM.Collect$$rtexit$$00000002  c_w.l(rtexit2.o)
    0x08000210   0x08000210   0x00000004   Code   RO          624    .ARM.Collect$$rtexit$$00000003  c_w.l(rtexit2.o)
    0x08000214   0x08000214   0x00000006   Code   RO          625    .ARM.Collect$$rtexit$$00000004  c_w.l(rtexit2.o)
    0x0800021a   0x0800021a   0x00000002   PAD
    0x0800021c   0x0800021c   0x00000118   Code   RO            1    .text               main.o
    0x08000334   0x08000334   0x0000001a   Code   RO           94    .text               stm32f10x_it.o
    0x0800034e   0x0800034e   0x00000002   PAD
    0x08000350   0x08000350   0x0000024c   Code   RO          141    .text               system_stm32f10x.o
    0x0800059c   0x0800059c   0x00000064   Code   RO          162    .text               led.o
    0x08000600   0x08000600   0x0000002c   Code   RO          200    .text               wdg.o
    0x0800062c   0x0800062c   0x0000038c   Code   RO          218    .text               ec800.o
    0x080009b8   0x080009b8   0x00000140   Code   RO          244    .text               aht20.o
    0x08000af8   0x08000af8   0x00000408   Code   RO          268    .text               bsp_i2c.o
    0x08000f00   0x08000f00   0x0000035c   Code   RO          285    .text               stm32f10x_gpio.o
    0x0800125c   0x0800125c   0x00000040   Code   RO          297    .text               stm32f10x_iwdg.o
    0x0800129c   0x0800129c   0x00000408   Code   RO          309    .text               stm32f10x_usart.o
    0x080016a4   0x080016a4   0x000003a4   Code   RO          321    .text               stm32f10x_rcc.o
    0x08001a48   0x08001a48   0x000005bc   Code   RO          335    .text               stm32f10x_flash.o
    0x08002004   0x08002004   0x000000dc   Code   RO          347    .text               misc.o
    0x080020e0   0x080020e0   0x00000040   Code   RO          373    .text               startup_stm32f10x_hd.o
    0x08002120   0x08002120   0x000001b0   Code   RO          377    .text               usart.o
    0x080022d0   0x080022d0   0x000000d4   Code   RO          397    .text               delay.o
    0x080023a4   0x080023a4   0x0000000c   Code   RO          411    .text               sys.o
    0x080023b0   0x080023b0   0x00000002   Code   RO          425    .text               c_w.l(use_no_semi_2.o)
    0x080023b2   0x080023b2   0x00000002   PAD
    0x080023b4   0x080023b4   0x00000018   Code   RO          431    .text               c_w.l(noretval__2printf.o)
    0x080023cc   0x080023cc   0x00000028   Code   RO          433    .text               c_w.l(noretval__2sprintf.o)
    0x080023f4   0x080023f4   0x0000004e   Code   RO          437    .text               c_w.l(_printf_pad.o)
    0x08002442   0x08002442   0x00000024   Code   RO          439    .text               c_w.l(_printf_truncate.o)
    0x08002466   0x08002466   0x00000052   Code   RO          441    .text               c_w.l(_printf_str.o)
    0x080024b8   0x080024b8   0x00000078   Code   RO          443    .text               c_w.l(_printf_dec.o)
    0x08002530   0x08002530   0x000000bc   Code   RO          445    .text               c_w.l(_printf_wctomb.o)
    0x080025ec   0x080025ec   0x00000050   Code   RO          450    .text               c_w.l(_printf_oct_int.o)
    0x0800263c   0x0800263c   0x00000058   Code   RO          457    .text               c_w.l(_printf_hex_int.o)
    0x08002694   0x08002694   0x00000188   Code   RO          492    .text               c_w.l(__printf_flags_ss_wp.o)
    0x0800281c   0x0800281c   0x00000024   Code   RO          504    .text               c_w.l(strstr.o)
    0x08002840   0x08002840   0x00000006   Code   RO          506    .text               c_w.l(heapauxi.o)
    0x08002846   0x08002846   0x00000002   Code   RO          510    .text               c_w.l(use_no_semi.o)
    0x08002848   0x08002848   0x000000b2   Code   RO          513    .text               c_w.l(_printf_intcommon.o)
    0x080028fa   0x080028fa   0x00000002   PAD
    0x080028fc   0x080028fc   0x00000030   Code   RO          515    .text               c_w.l(_printf_char_common.o)
    0x0800292c   0x0800292c   0x0000000a   Code   RO          517    .text               c_w.l(_sputc.o)
    0x08002936   0x08002936   0x0000002c   Code   RO          519    .text               c_w.l(_printf_char.o)
    0x08002962   0x08002962   0x00000002   PAD
    0x08002964   0x08002964   0x00000024   Code   RO          521    .text               c_w.l(_printf_char_file.o)
    0x08002988   0x08002988   0x0000002c   Code   RO          523    .text               c_w.l(_printf_wchar.o)
    0x080029b4   0x080029b4   0x00000040   Code   RO          527    .text               c_w.l(_wcrtomb.o)
    0x080029f4   0x080029f4   0x00000010   Code   RO          544    .text               c_w.l(rt_ctype_table.o)
    0x08002a04   0x08002a04   0x00000008   Code   RO          548    .text               c_w.l(ferror.o)
    0x08002a0c   0x08002a0c   0x0000004a   Code   RO          552    .text               c_w.l(sys_stackheap_outer.o)
    0x08002a56   0x08002a56   0x00000002   PAD
    0x08002a58   0x08002a58   0x00000008   Code   RO          557    .text               c_w.l(rt_locale_intlibspace.o)
    0x08002a60   0x08002a60   0x00000012   Code   RO          562    .text               c_w.l(exit.o)
    0x08002a72   0x08002a72   0x00000002   PAD
    0x08002a74   0x08002a74   0x00000008   Code   RO          570    .text               c_w.l(libspace.o)
    0x08002a7c   0x08002a7c   0x00000080   Code   RO          575    .text               c_w.l(strcmpv7m.o)
    0x08002afc   0x08002afc   0x0000000e   Code   RO          485    i._is_digit         c_w.l(__printf_wp.o)
    0x08002b0a   0x08002b0a   0x00000002   PAD
    0x08002b0c   0x08002b0c   0x0000002c   Code   RO          560    locale$$code        c_w.l(lc_ctype_c.o)
    0x08002b38   0x08002b38   0x00000008   Data   RO          446    .constdata          c_w.l(_printf_wctomb.o)
    0x08002b40   0x08002b40   0x00000028   Data   RO          458    .constdata          c_w.l(_printf_hex_int.o)
    0x08002b68   0x08002b68   0x00000011   Data   RO          493    .constdata          c_w.l(__printf_flags_ss_wp.o)
    0x08002b79   0x08002b79   0x00000003   PAD
    0x08002b7c   0x08002b7c   0x00000020   Data   RO          697    Region$$Table       anon$$obj.o
    0x08002b9c   0x08002b9c   0x00000110   Data   RO          559    locale$$data        c_w.l(lc_ctype_c.o)


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x08002cac, Size: 0x00000710, Max: 0x00005000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x08002cac   0x00000014   Data   RW          142    .data               system_stm32f10x.o
    0x20000014   0x08002cc0   0x00000010   Data   RW          220    .data               ec800.o
    0x20000024   0x08002cd0   0x00000001   Data   RW          245    .data               aht20.o
    0x20000025   0x08002cd1   0x00000001   PAD
    0x20000026   0x08002cd2   0x00000002   Data   RW          269    .data               bsp_i2c.o
    0x20000028   0x08002cd4   0x00000014   Data   RW          322    .data               stm32f10x_rcc.o
    0x2000003c   0x08002ce8   0x00000008   Data   RW          379    .data               usart.o
    0x20000044   0x08002cf0   0x00000004   Data   RW          398    .data               delay.o
    0x20000048        -       0x00000064   Zero   RW          378    .bss                usart.o
    0x200000ac        -       0x00000060   Zero   RW          571    .bss                c_w.l(libspace.o)
    0x2000010c   0x08002cf4   0x00000004   PAD
    0x20000110        -       0x00000200   Zero   RW          371    HEAP                startup_stm32f10x_hd.o
    0x20000310        -       0x00000400   Zero   RW          370    STACK               startup_stm32f10x_hd.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       320          4          0          1          0       2183   aht20.o
      1032         14          0          2          0       5232   bsp_i2c.o
         0          0          0          0          0         32   core_cm3.o
       212         18          0          4          0       1131   delay.o
       908        276          0         16          0       2436   ec800.o
       100         14          0          0          0     204023   led.o
       280         44          0          0          0     206999   main.o
       220         22          0          0          0       1897   misc.o
        64         26        304          0       1536        828   startup_stm32f10x_hd.o
      1468         34          0          0          0       8302   stm32f10x_flash.o
       860         38          0          0          0       5829   stm32f10x_gpio.o
        26          0          0          0          0      24526   stm32f10x_it.o
        64          6          0          0          0       1325   stm32f10x_iwdg.o
       932         36          0         20          0       9052   stm32f10x_rcc.o
      1032         22          0          0          0       8552   stm32f10x_usart.o
        12          0          0          0          0        497   sys.o
       588         36          0         20          0       8529   system_stm32f10x.o
       432         22          0          8        100       3849   usart.o
        44          0          0          0          0        706   wdg.o

    ----------------------------------------------------------------------
      8596        <USER>        <GROUP>         72       1636     495928   Object Totals
         0          0         32          0          0          0   (incl. Generated)
         2          0          0          1          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

         8          0          0          0          0         68   __main.o
       392          4         17          0          0         92   __printf_flags_ss_wp.o
        14          0          0          0          0         68   __printf_wp.o
         0          0          0          0          0          0   __rtentry.o
        12          0          0          0          0          0   __rtentry2.o
         6          0          0          0          0          0   __rtentry4.o
        52          8          0          0          0          0   __scatter.o
        26          0          0          0          0          0   __scatter_copy.o
        28          0          0          0          0          0   __scatter_zi.o
         6          0          0          0          0          0   _printf_c.o
        44          0          0          0          0        108   _printf_char.o
        48          6          0          0          0         96   _printf_char_common.o
        36          4          0          0          0         80   _printf_char_file.o
         6          0          0          0          0          0   _printf_d.o
       120         16          0          0          0         92   _printf_dec.o
        88          4         40          0          0         88   _printf_hex_int.o
         6          0          0          0          0          0   _printf_i.o
       178          0          0          0          0         88   _printf_intcommon.o
        10          0          0          0          0          0   _printf_l.o
         6          0          0          0          0          0   _printf_lc.o
         6          0          0          0          0          0   _printf_o.o
        80          8          0          0          0         88   _printf_oct_int.o
        78          0          0          0          0        108   _printf_pad.o
         0          0          0          0          0          0   _printf_percent.o
         4          0          0          0          0          0   _printf_percent_end.o
         6          0          0          0          0          0   _printf_s.o
        82          0          0          0          0         80   _printf_str.o
        36          0          0          0          0         84   _printf_truncate.o
         6          0          0          0          0          0   _printf_u.o
        44          0          0          0          0        108   _printf_wchar.o
       188          6          8          0          0         92   _printf_wctomb.o
         6          0          0          0          0          0   _printf_x.o
        10          0          0          0          0         68   _sputc.o
        64          0          0          0          0         92   _wcrtomb.o
        18          0          0          0          0         80   exit.o
         8          0          0          0          0         68   ferror.o
         6          0          0          0          0        152   heapauxi.o
        44         10        272          0          0         76   lc_ctype_c.o
         2          0          0          0          0          0   libinit.o
        20          0          0          0          0          0   libinit2.o
         2          0          0          0          0          0   libshutdown.o
         2          0          0          0          0          0   libshutdown2.o
         8          4          0          0         96         68   libspace.o
        24          4          0          0          0         84   noretval__2printf.o
        40          6          0          0          0         84   noretval__2sprintf.o
        16          4          0          0          0         76   rt_ctype_table.o
         8          4          0          0          0         68   rt_locale_intlibspace.o
         2          0          0          0          0          0   rtexit.o
        10          0          0          0          0          0   rtexit2.o
       128          0          0          0          0         68   strcmpv7m.o
        36          0          0          0          0         80   strstr.o
        74          0          0          0          0         80   sys_stackheap_outer.o
         2          0          0          0          0         68   use_no_semi.o
         2          0          0          0          0         68   use_no_semi_2.o

    ----------------------------------------------------------------------
      2164         <USER>        <GROUP>          0        100       2620   Library Totals
        16          0          3          0          4          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      2148         88        337          0         96       2620   c_w.l

    ----------------------------------------------------------------------
      2164         <USER>        <GROUP>          0        100       2620   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     10760        700        676         72       1736     495684   Grand Totals
     10760        700        676         72       1736     495684   ELF Image Totals
     10760        700        676         72          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                11436 (  11.17kB)
    Total RW  Size (RW Data + ZI Data)              1808 (   1.77kB)
    Total ROM Size (Code + RO Data + RW Data)      11508 (  11.24kB)

==============================================================================

