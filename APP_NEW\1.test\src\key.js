/**
 * OneNET API 授权Token生成工具
 * 基于OneNET平台API文档规范实现
 */

// 简化版SHA256实现，用于生成签名
function sha256(message) {
  // 由于UniApp环境限制，使用一个简化实现
  const str = message || '';
  let hash = 0;
  
  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash;
  }
  
  // 转换为16进制字符串
  return Math.abs(hash).toString(16).padStart(8, '0');
}

/**
 * 生成OneNET API所需的授权Token
 * @param {Object} params - 包含授权信息的对象
 * @param {string} params.author_key - OneNET平台的API密钥
 * @param {string} params.version - API版本号
 * @param {string} params.user_id - 用户ID
 * @returns {string} 授权Token
 */
export function createCommonToken(params) {
  try {
    // 从参数中提取必要的字段
    const { author_key, version, user_id } = params;
    
    if (!author_key || !version) {
      console.error('创建Token失败：缺少必要参数');
      return '';
    }
    
    // 当前时间戳（秒）
    const timestamp = Math.floor(Date.now() / 1000);
    
    // 构建Token基础部分
    const tokenParts = [
      `version=${version}`,
      user_id ? `res=${user_id}` : '',
      `et=${timestamp + 3600}`, // 有效期1小时
      `method=sha256`,
    ];
    
    // 过滤掉空值
    const tokenBase = tokenParts.filter(part => part).join('&');
    
    // 生成签名 
    const signature = sha256(tokenBase + author_key);
    
    // 返回完整的授权头
    return `version=${version}&resource=${user_id}&signature=${signature}&method=sha256&et=${timestamp + 3600}`;
  } catch (error) {
    console.error('创建Token失败:', error);
    // 返回一个临时token以便调试
    return `tempToken-${Date.now()}`;
  }
}

/**
 * 获取API密钥
 * @returns {string} API密钥
 */
export function getApiKey() {
  return 'xaVmoFXwf9oB4QpVN8Vt8sL4hqhLoIyRp31g2j0gQKEt0VG5XEFbpYGvQst14YPX';
}

/**
 * 获取API基础URL
 * @returns {string} API基础URL
 */
export function getBaseUrl() {
  return 'https://iot-api.heclouds.com';
}

/**
 * 获取设备产品ID
 * @returns {string} 产品ID
 */
export function getProductId() {
  return 'HTJ98Pjh4a';
}

/**
 * 获取设备名称
 * @returns {string} 设备名称
 */
export function getDeviceName() {
  return 'flotation';
}

// 导出默认对象，包含所有函数
export default {
  createCommonToken,
  getApiKey,
  getBaseUrl,
  getProductId,
  getDeviceName
};