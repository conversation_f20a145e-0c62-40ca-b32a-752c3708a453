<template>
  <view class="health-container">
    <!-- Toast容器 -->
    <ToastContainer />

    <!-- 头部区域 -->
    <view class="header-section">
      <view class="date-info">
        <text class="date-text">{{ formatCurrentDate() }}</text>
      </view>
      <view class="title-section">
        <text class="main-title">健康活动</text>
        <view class="profile-avatar" @tap="onProfileTap">
          <view class="avatar-placeholder"></view>
        </view>
      </view>
    </view>

    <!-- 日期选择器 -->
    <view class="date-selector-container">
      <view class="date-selector">
        <view
          v-for="(day, index) in weekDays"
          :key="index"
          class="date-item"
          :class="{ active: day.isToday }"
          @tap="selectDate(day, index)"
        >
          <text class="day-name">{{ day.name }}</text>
          <text class="day-number">{{ day.number }}</text>
        </view>
      </view>
    </view>

    <!-- 健康指标卡片 -->
    <view class="health-metrics-container">
      <!-- 心率卡片 - 左侧大卡片 -->
      <view class="health-card heart-rate-card" @tap="onMetricTap('heartRate')">
        <view class="card-header">
          <view class="heart-icon">❤️</view>
        </view>
        <view class="card-main">
          <text class="card-value">{{ heartRate }}</text>
          <text class="card-unit">bpm心率</text>
        </view>
        <view class="card-chart">
          <view class="heart-wave">
            <view class="wave-line"></view>
            <view class="wave-line"></view>
            <view class="wave-line"></view>
            <view class="wave-line"></view>
            <view class="wave-line"></view>
          </view>
        </view>
      </view>

      <!-- 步数卡片 -->
      <view class="health-card steps-card" @tap="onMetricTap('steps')">
        <view class="card-content">
          <text class="card-value">{{ todaySteps }}</text>
          <text class="card-label">Steps</text>
        </view>
        <!-- 步数柱状图 -->
        <view class="steps-bar-chart">
          <svg class="steps-svg" viewBox="0 0 80 40" xmlns="http://www.w3.org/2000/svg">
            <defs>
              <linearGradient id="stepsBarGradient" x1="0%" y1="100%" x2="0%" y2="0%">
                <stop offset="0%" style="stop-color:rgba(255,255,255,0.7);stop-opacity:1" />
                <stop offset="50%" style="stop-color:rgba(255,255,255,0.9);stop-opacity:1" />
                <stop offset="100%" style="stop-color:rgba(255,255,255,1);stop-opacity:1" />
              </linearGradient>
              <filter id="stepsGlow">
                <feGaussianBlur stdDeviation="1" result="coloredBlur"/>
                <feMerge>
                  <feMergeNode in="coloredBlur"/>
                  <feMergeNode in="SourceGraphic"/>
                </feMerge>
              </filter>
            </defs>
            <!-- 动态柱状图 -->
            <rect x="8" y="25" width="8" height="15" fill="url(#stepsBarGradient)" rx="4" filter="url(#stepsGlow)">
              <animate attributeName="height" values="15;18;15" dur="2s" repeatCount="indefinite"/>
              <animate attributeName="y" values="25;22;25" dur="2s" repeatCount="indefinite"/>
            </rect>
            <rect x="20" y="18" width="8" height="22" fill="url(#stepsBarGradient)" rx="4" filter="url(#stepsGlow)">
              <animate attributeName="height" values="22;26;22" dur="2.5s" repeatCount="indefinite"/>
              <animate attributeName="y" values="18;14;18" dur="2.5s" repeatCount="indefinite"/>
            </rect>
            <rect x="32" y="28" width="8" height="12" fill="url(#stepsBarGradient)" rx="4" filter="url(#stepsGlow)">
              <animate attributeName="height" values="12;15;12" dur="1.8s" repeatCount="indefinite"/>
              <animate attributeName="y" values="28;25;28" dur="1.8s" repeatCount="indefinite"/>
            </rect>
            <rect x="44" y="12" width="8" height="28" fill="url(#stepsBarGradient)" rx="4" filter="url(#stepsGlow)">
              <animate attributeName="height" values="28;32;28" dur="3s" repeatCount="indefinite"/>
              <animate attributeName="y" values="12;8;12" dur="3s" repeatCount="indefinite"/>
            </rect>
            <rect x="56" y="20" width="8" height="20" fill="url(#stepsBarGradient)" rx="4" filter="url(#stepsGlow)">
              <animate attributeName="height" values="20;24;20" dur="2.2s" repeatCount="indefinite"/>
              <animate attributeName="y" values="20;16;20" dur="2.2s" repeatCount="indefinite"/>
            </rect>
          </svg>
        </view>
      </view>

      <!-- 血氧卡片 -->
      <view class="health-card oxygen-card" @tap="onMetricTap('oxygen')">
        <view class="card-content">
          <text class="card-value">{{ bloodOxygen }}</text>
          <text class="card-label">血氧</text>
        </view>
        <!-- 血氧环形图 -->
        <view class="oxygen-ring-chart">
          <svg class="ring-svg" viewBox="0 0 80 80" xmlns="http://www.w3.org/2000/svg">
            <defs>
              <linearGradient id="oxygenRingGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" style="stop-color:rgba(255,255,255,1);stop-opacity:1" />
                <stop offset="50%" style="stop-color:rgba(255,255,255,0.9);stop-opacity:1" />
                <stop offset="100%" style="stop-color:rgba(255,255,255,0.7);stop-opacity:1" />
              </linearGradient>
              <filter id="oxygenGlow">
                <feGaussianBlur stdDeviation="2" result="coloredBlur"/>
                <feMerge>
                  <feMergeNode in="coloredBlur"/>
                  <feMergeNode in="SourceGraphic"/>
                </feMerge>
              </filter>
            </defs>
            <!-- 背景圆环 -->
            <circle
              cx="40"
              cy="40"
              r="28"
              fill="none"
              stroke="rgba(255,255,255,0.15)"
              stroke-width="8"
            />
            <!-- 进度圆环 -->
            <circle
              cx="40"
              cy="40"
              r="28"
              fill="none"
              stroke="url(#oxygenRingGradient)"
              stroke-width="8"
              stroke-linecap="round"
              :stroke-dasharray="`${bloodOxygen * 1.76} 176`"
              stroke-dashoffset="44"
              transform="rotate(-90 40 40)"
              filter="url(#oxygenGlow)"
              class="oxygen-progress-ring"
            />
            <!-- 内部装饰圆点 -->
            <circle cx="40" cy="40" r="3" fill="rgba(255,255,255,0.8)">
              <animate attributeName="r" values="3;5;3" dur="2s" repeatCount="indefinite"/>
              <animate attributeName="opacity" values="0.8;0.4;0.8" dur="2s" repeatCount="indefinite"/>
            </circle>
          </svg>
          <view class="ring-center-text">
            <text class="ring-percentage">{{ bloodOxygen }}%</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 趋势图表卡片 -->
    <view class="chart-card">
      <view class="chart-header">
        <view class="chart-title-section">
          <text class="chart-title">健康趋势</text>
          <text class="chart-subtitle">7天数据分析</text>
        </view>
        <view class="time-selector">
          <text class="time-option active">7天</text>
          <text class="time-option">30天</text>
        </view>
      </view>
      
      <view class="chart-container">
        <canvas 
          class="trend-chart" 
          canvas-id="healthTrendChart" 
          :style="{ width: chartWidth + 'px', height: '200px' }"
        ></canvas>
        <view v-if="isLoadingChart" class="chart-loading">
          <view class="loading-spinner"></view>
          <text class="loading-text">分析中...</text>
        </view>
      </view>
      
      <view class="chart-legend">
        <view class="legend-item">
          <view class="legend-dot health"></view>
          <text class="legend-text">健康分数</text>
        </view>
        <view class="legend-item">
          <view class="legend-dot exposure"></view>
          <text class="legend-text">暴露水平</text>
        </view>
      </view>
    </view>

    <!-- 健康洞察卡片 -->
    <view class="insights-card">
      <view class="insights-header">
        <view class="insights-title-section">
          <text class="insights-title">健康洞察</text>
          <text class="insights-subtitle">AI智能分析</text>
        </view>
        <view class="ai-badge">
          <text class="ai-icon">🤖</text>
          <text class="ai-text">AI驱动</text>
        </view>
      </view>
      
      <view class="insights-list">
        <view 
          v-for="(insight, index) in healthAdviceList" 
          :key="index"
          class="insight-item"
          @tap="onAdviceTap(insight, index)"
        >
          <view class="insight-icon-container">
            <text class="insight-emoji">{{ insight.icon }}</text>
          </view>
          <view class="insight-content">
            <text class="insight-title-text">{{ insight.text }}</text>
            <text class="insight-description">{{ insight.detail }}</text>
          </view>
          <view class="insight-arrow">
            <text class="arrow-text">›</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部导航 -->
    <BottomNavigation current="health" />
  </view>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { radiationState } from '../../utils/dataStore.js'
import BottomNavigation from '../../components/BottomNavigation.vue'
import ToastContainer from '../../components/ToastContainer.vue'
import { toastManager } from '../../utils/toastManager.js'

// 响应式数据
const healthScore = ref(85)
const exposureLevel = ref(15.6)
const chartWidth = ref(350)
const isLoadingChart = ref(false)
const scrollLeft = ref(0)

// 健康指标数据
const heartRate = ref(88)
const todaySteps = ref(450)
const bloodOxygen = ref(98)

// 图表数据
const stepsChartData = ref([60, 80, 45, 90, 75])

// 生成实际日期数据
const generateWeekDays = () => {
  const days = []
  const today = new Date()
  const dayNames = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat']

  // 生成前后各3天，共7天
  for (let i = -3; i <= 3; i++) {
    const date = new Date(today)
    date.setDate(today.getDate() + i)

    days.push({
      name: dayNames[date.getDay()],
      number: date.getDate().toString(),
      fullDate: date.toISOString().split('T')[0],
      isToday: i === 0
    })
  }

  return days
}

const weekDays = ref(generateWeekDays())

// 监测时长
const monitoringDuration = computed(() => {
  const hours = Math.floor(Math.random() * 12) + 6
  return { hours }
})

// 风险等级
const riskLevel = computed(() => {
  const level = Math.floor(exposureLevel.value / 10) + 1
  return {
    level: Math.min(level, 4),
    icon: level <= 2 ? '✅' : level === 3 ? '⚠️' : '🚨'
  }
})

// 健康建议列表
const healthAdviceList = computed(() => {
  const advice = []
  
  if (exposureLevel.value > 20) {
    advice.push({
      icon: '⚠️',
      text: '辐射暴露偏高',
      detail: '建议减少在高辐射环境中的停留时间',
      type: 'important',
      priority: 'high'
    })
  }
  
  if (healthScore.value < 80) {
    advice.push({
      icon: '💪',
      text: '增强体质锻炼',
      detail: '适当的运动可以提高身体抵抗力',
      type: 'suggestion',
      priority: 'medium'
    })
  }
  
  advice.push({
    icon: '💧',
    text: '保持充足水分',
    detail: '每天饮水量应达到2000ml以上',
    type: 'info',
    priority: 'low'
  })
  
  return advice
})

// 日期选择函数
const selectDate = (day, index) => {
  weekDays.value.forEach(d => d.isToday = false)
  day.isToday = true

  // 滚动到选中的日期
  scrollLeft.value = index * 80 - 160

  console.log('选择日期:', day)
  toastManager.success(`已切换到 ${day.name} ${day.number}日 的健康数据`, {
    duration: 2000,
    showCountdown: false
  })
}

// 当前日期格式化
const formatCurrentDate = () => {
  const today = new Date()
  const month = today.getMonth() + 1
  const date = today.getDate()
  return `${month}月${date}日`
}

// 时间格式化函数
const formatTime = (timestamp) => {
  const date = new Date(timestamp)
  const hours = date.getHours()
  const minutes = date.getMinutes()
  const ampm = hours >= 12 ? 'PM' : 'AM'
  const displayHours = hours % 12 || 12
  return `${displayHours}:${minutes.toString().padStart(2, '0')} ${ampm} - ${displayHours + 1}:${minutes.toString().padStart(2, '0')} ${ampm}`
}

// 日期格式化函数
const formatDate = (timestamp) => {
  const date = new Date(timestamp)
  const options = { 
    weekday: 'long', 
    year: 'numeric', 
    month: 'long', 
    day: 'numeric' 
  }
  return date.toLocaleDateString('zh-CN', options)
}

// 健康状态文本
const getHealthStatusText = (score) => {
  if (score >= 90) return '优秀状态'
  if (score >= 80) return '良好状态'
  if (score >= 70) return '一般状态'
  return '需要关注'
}

// 指标卡片点击事件
const onMetricTap = (metricType) => {
  console.log('点击了指标:', metricType)

  const metricNames = {
    heartRate: '心率监测',
    steps: '步数统计',
    oxygen: '血氧饱和度'
  }

  const metricName = metricNames[metricType] || metricType
  toastManager.info(`正在打开${metricName}详情页面...`, {
    duration: 2500,
    showCountdown: false
  })
}

// 用户头像点击事件
const onProfileTap = () => {
  toastManager.info('个人健康档案功能开发中...', {
    duration: 2000,
    showCountdown: false
  })
}

// 健康洞察点击事件
const onAdviceTap = (advice, index) => {
  console.log('点击了建议:', advice)
  uni.showModal({
    title: advice.text,
    content: advice.detail + '\n\n是否查看详细建议？',
    confirmText: '查看',
    cancelText: '取消'
  })
}

// 页面生命周期
onMounted(() => {
  console.log('健康页面已加载')
  // 初始化图表等
})

onUnmounted(() => {
  console.log('健康页面已卸载')
})
</script>

<style scoped>
/* 健康页面容器 - 参考提供的图片设计 */
.health-container {
  min-height: 100vh;
  background: linear-gradient(180deg, #F7F9FC 0%, #FFFFFF 50%, #F8FAFF 100%);
  padding-bottom: 100px;
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'SF Pro Text', sans-serif;
  position: relative;
}

.health-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 200px;
  background: radial-gradient(ellipse at top, rgba(99, 102, 241, 0.05) 0%, transparent 70%);
  pointer-events: none;
}

/* 头部区域 - 向上移动并美化 */
.header-section {
  padding: 60px 20px 20px;
  background: linear-gradient(180deg, #F8FAFC 0%, #FFFFFF 100%);
  position: relative;
}

.header-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent 0%, rgba(0, 212, 170, 0.3) 50%, transparent 100%);
}

.date-info {
  margin-bottom: 12px;
}

.date-text {
  font-size: 15px;
  color: #8E8E93;
  font-weight: 500;
  letter-spacing: 0.5px;
}

.title-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 28px;
}

.main-title {
  font-size: 34px;
  font-weight: 800;
  color: #1A1A1A;
  line-height: 1.1;
  letter-spacing: -0.5px;
}

.profile-avatar {
  width: 44px;
  height: 44px;
  border-radius: 22px;
  overflow: hidden;
  background: linear-gradient(135deg, #F2F2F7 0%, #E5E5EA 100%);
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.8);
}

.profile-avatar:active {
  transform: scale(0.95);
}

.profile-avatar:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  transform: translateY(-1px);
}

.avatar-image {
  width: 100%;
  height: 100%;
}

/* 可滑动日期选择器 */
.date-scroll-container {
  padding: 0 0 28px;
  background: linear-gradient(180deg, #FFFFFF 0%, #F8FAFC 100%);
  position: relative;
}

.date-scroll-container::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 20px;
  right: 20px;
  height: 2px;
  background: linear-gradient(90deg, transparent 0%, #00D4AA 50%, transparent 100%);
  border-radius: 1px;
}

.date-selector {
  display: flex;
  gap: 14px;
  padding: 0 20px;
  width: max-content;
}

.date-item {
  min-width: 72px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 20px;
  padding: 18px 14px;
  text-align: center;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  flex-shrink: 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid rgba(0, 0, 0, 0.04);
}

.date-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.date-item.active {
  background: linear-gradient(135deg, #00D4AA 0%, #00B894 100%);
  transform: scale(1.08) translateY(-3px);
  box-shadow: 0 8px 24px rgba(0, 212, 170, 0.4);
  border: 1px solid rgba(0, 212, 170, 0.3);
}

.day-name {
  display: block;
  font-size: 13px;
  color: #8E8E93;
  font-weight: 600;
  margin-bottom: 6px;
  letter-spacing: 0.3px;
  text-transform: uppercase;
}

.date-item.active .day-name {
  color: rgba(255, 255, 255, 0.9);
}

.day-number {
  display: block;
  font-size: 26px;
  font-weight: 800;
  color: #1A1A1A;
  line-height: 1;
}

.date-item.active .day-number {
  color: #ffffff;
}

/* 健康指标卡片容器 - 参考提供的图片布局 */
.health-metrics-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  margin: 0 20px 24px;
}

/* 健康卡片基础样式 */
.health-card {
  border-radius: 24px;
  padding: 24px;
  position: relative;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  min-height: 140px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  backdrop-filter: blur(10px);
}

.health-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  border-radius: 24px;
  pointer-events: none;
}

.health-card:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.15);
}

.health-card:active {
  transform: translateY(-2px) scale(1.01);
}

/* 心率卡片 - 左上角，占据两行 */
.heart-rate-card {
  grid-row: span 2;
  background: linear-gradient(135deg, #667EEA 0%, #764BA2 50%, #5B73DB 100%);
  color: #ffffff;
  box-shadow: 0 12px 40px rgba(102, 126, 234, 0.35);
  position: relative;
  overflow: hidden;
}

.heart-rate-card::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
  animation: heartGlow 4s ease-in-out infinite;
}

.heart-rate-card:hover {
  box-shadow: 0 20px 60px rgba(102, 126, 234, 0.45);
  transform: translateY(-6px) scale(1.02);
}

/* 步数卡片 - 右上角 */
.steps-card {
  background: linear-gradient(135deg, #4FACFE 0%, #00F2FE 50%, #43CBFF 100%);
  color: #ffffff;
  box-shadow: 0 12px 40px rgba(79, 172, 254, 0.35);
  position: relative;
  overflow: hidden;
}

.steps-card::before {
  content: '';
  position: absolute;
  top: -50%;
  right: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.15) 0%, transparent 60%);
  animation: stepsShimmer 3s ease-in-out infinite;
}

.steps-card:hover {
  box-shadow: 0 20px 60px rgba(79, 172, 254, 0.45);
  transform: translateY(-6px) scale(1.02);
}

/* 血氧卡片 - 右下角 */
.oxygen-card {
  background: linear-gradient(135deg, #11998E 0%, #38EF7D 50%, #20BF6B 100%);
  color: #ffffff;
  box-shadow: 0 12px 40px rgba(17, 153, 142, 0.35);
  position: relative;
  overflow: hidden;
}

.oxygen-card::before {
  content: '';
  position: absolute;
  bottom: -50%;
  right: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.12) 0%, transparent 65%);
  animation: oxygenPulse 5s ease-in-out infinite;
}

.oxygen-card:hover {
  box-shadow: 0 20px 60px rgba(17, 153, 142, 0.45);
  transform: translateY(-6px) scale(1.02);
}

/* 卡片图标 */
.card-icon {
  position: absolute;
  top: 24px;
  left: 24px;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 12px;
  backdrop-filter: blur(10px);
}

.icon-emoji {
  font-size: 20px;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
}

/* 卡片内容 */
.card-content {
  margin-top: 70px;
  z-index: 2;
  position: relative;
}

.card-value {
  font-size: 42px;
  font-weight: 900;
  line-height: 1;
  margin-bottom: 6px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  letter-spacing: -1px;
}

.card-unit {
  font-size: 18px;
  opacity: 0.85;
  margin-left: 6px;
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.card-label {
  font-size: 15px;
  opacity: 0.9;
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  letter-spacing: 0.3px;
}

/* 心率波形图 */
.heart-wave-chart {
  position: absolute;
  bottom: 24px;
  right: 24px;
  width: 140px;
  height: 50px;
  z-index: 3;
}

.wave-svg {
  width: 100%;
  height: 100%;
  border-radius: 12px;
}

.heart-wave-path {
  filter: drop-shadow(0 2px 8px rgba(255, 255, 255, 0.4));
}

.pulse-dot {
  filter: drop-shadow(0 0 8px rgba(255, 255, 255, 0.8));
}

/* 步数柱状图 */
.steps-bar-chart {
  position: absolute;
  bottom: 24px;
  right: 24px;
  width: 80px;
  height: 40px;
  z-index: 3;
}

.steps-svg {
  width: 100%;
  height: 100%;
  filter: drop-shadow(0 3px 6px rgba(255, 255, 255, 0.3));
}

/* 血氧环形图 */
.oxygen-ring-chart {
  position: absolute;
  bottom: 24px;
  right: 24px;
  width: 80px;
  height: 80px;
  z-index: 3;
}

.ring-svg {
  width: 100%;
  height: 100%;
  filter: drop-shadow(0 3px 12px rgba(255, 255, 255, 0.4));
}

.oxygen-progress-ring {
  transition: stroke-dasharray 1.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.ring-center-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  z-index: 4;
}

.ring-percentage {
  font-size: 13px;
  font-weight: 800;
  color: #ffffff;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  letter-spacing: -0.5px;
}



/* 趋势图表卡片 */
.chart-card {
  margin: 0 20px 24px;
  background: rgba(142, 142, 147, 0.12);
  border-radius: 20px;
  padding: 20px;
  backdrop-filter: blur(10px);
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
}

.chart-title-section {
  flex: 1;
}

.chart-title {
  font-size: 18px;
  font-weight: 600;
  color: #000000;
  line-height: 1.2;
  margin-bottom: 4px;
}

.chart-subtitle {
  font-size: 14px;
  color: #8E8E93;
  font-weight: 500;
}

.time-selector {
  display: flex;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 8px;
  padding: 2px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.time-option {
  padding: 6px 12px;
  font-size: 12px;
  font-weight: 600;
  color: #8E8E93;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.time-option.active {
  background: #ffffff;
  color: #000000;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.chart-container {
  position: relative;
  margin-bottom: 16px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.trend-chart {
  width: 100%;
  height: 200px;
  border-radius: 8px;
}

.chart-loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

.loading-spinner {
  width: 24px;
  height: 24px;
  border: 2px solid rgba(142, 142, 147, 0.3);
  border-top: 2px solid #00D4AA;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 14px;
  color: #8E8E93;
  font-weight: 500;
}

.chart-legend {
  display: flex;
  justify-content: center;
  gap: 24px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.legend-dot {
  width: 8px;
  height: 8px;
  border-radius: 4px;
}

.legend-dot.health {
  background: #FF6B6B;
}

.legend-dot.exposure {
  background: #00D4AA;
}

.legend-text {
  font-size: 12px;
  color: #8E8E93;
  font-weight: 500;
}

/* 健康洞察卡片 */
.insights-card {
  margin: 0 20px 24px;
  background: rgba(142, 142, 147, 0.12);
  border-radius: 20px;
  padding: 20px;
  backdrop-filter: blur(10px);
}

.insights-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
}

.insights-title-section {
  flex: 1;
}

.insights-title {
  font-size: 18px;
  font-weight: 600;
  color: #000000;
  line-height: 1.2;
  margin-bottom: 4px;
}

.insights-subtitle {
  font-size: 14px;
  color: #8E8E93;
  font-weight: 500;
}

.ai-badge {
  background: linear-gradient(135deg, #8B5CF6 0%, #7C3AED 100%);
  border-radius: 12px;
  padding: 6px 12px;
  display: flex;
  align-items: center;
  gap: 6px;
  box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
}

.ai-icon {
  font-size: 14px;
}

.ai-text {
  font-size: 12px;
  color: #ffffff;
  font-weight: 600;
}

.insights-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.insight-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.insight-item:hover {
  background: rgba(255, 255, 255, 1);
  transform: translateX(4px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.insight-icon-container {
  width: 40px;
  height: 40px;
  border-radius: 12px;
  background: linear-gradient(135deg, #F59E0B 0%, #D97706 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
}

.insight-emoji {
  font-size: 18px;
}

.insight-content {
  flex: 1;
}

.insight-title-text {
  font-size: 14px;
  font-weight: 600;
  color: #000000;
  line-height: 1.3;
  margin-bottom: 4px;
}

.insight-description {
  font-size: 12px;
  color: #8E8E93;
  line-height: 1.4;
}

.insight-arrow {
  color: #8E8E93;
  font-size: 18px;
  font-weight: 600;
}

/* 响应式设计 */
@media (max-width: 375px) {
  .date-selector {
    gap: 8px;
    padding: 0 16px;
  }

  .date-item {
    min-width: 60px;
    padding: 12px 8px;
  }

  .health-metrics-container {
    grid-template-columns: 1fr;
    gap: 12px;
    margin: 0 16px 20px;
  }

  .heart-rate-card {
    grid-row: span 1;
  }

  .chart-card,
  .insights-card {
    margin: 0 16px 20px;
  }

  .header-section {
    padding: 50px 16px 12px;
  }

  .main-title {
    font-size: 28px;
  }

  .card-value {
    font-size: 28px;
  }
}

@media (min-width: 768px) {
  .health-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 0 32px 120px;
  }

  .health-metrics-container {
    grid-template-columns: repeat(3, 1fr);
    margin: 0 0 32px;
  }

  .heart-rate-card {
    grid-row: span 2;
    grid-column: span 1;
  }

  .chart-card,
  .insights-card {
    margin: 0 0 32px;
  }

  .date-item {
    min-width: 80px;
  }
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.header-section {
  animation: slideInLeft 0.8s ease-out;
}

.date-scroll-container {
  animation: fadeInUp 0.8s ease-out 0.2s both;
}

.health-metrics-container {
  animation: scaleIn 0.8s ease-out 0.4s both;
}

.health-card:nth-child(1) {
  animation-delay: 0.5s;
}

.health-card:nth-child(2) {
  animation-delay: 0.6s;
}

.health-card:nth-child(3) {
  animation-delay: 0.7s;
}

.chart-card {
  animation: fadeInUp 0.8s ease-out 0.8s both;
}

.insights-card {
  animation: fadeInUp 0.8s ease-out 0.9s both;
}

/* 脉冲动画 */
@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.8;
  }
}

.heart-rate-card .card-icon {
  animation: pulse 2s ease-in-out infinite;
}

/* 卡片特效动画 */
@keyframes heartGlow {
  0%, 100% {
    transform: rotate(0deg) scale(1);
    opacity: 0.1;
  }
  50% {
    transform: rotate(180deg) scale(1.1);
    opacity: 0.2;
  }
}

@keyframes stepsShimmer {
  0%, 100% {
    transform: translateX(-100%) rotate(45deg);
    opacity: 0.15;
  }
  50% {
    transform: translateX(100%) rotate(45deg);
    opacity: 0.25;
  }
}

@keyframes oxygenPulse {
  0%, 100% {
    transform: scale(1);
    opacity: 0.12;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.2;
  }
}

/* 响应式设计 */
@media (max-width: 480px) {
  .health-metrics-container {
    gap: 16px;
    padding: 0 16px;
  }

  .health-card {
    min-height: 120px;
    padding: 20px;
  }

  .card-value {
    font-size: 36px;
  }

  .heart-wave-chart,
  .steps-bar-chart,
  .oxygen-ring-chart {
    width: 60px;
    height: 30px;
    bottom: 16px;
    right: 16px;
  }

  .oxygen-ring-chart {
    height: 60px;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .health-container {
    background: #000000;
  }

  .main-title,
  .activity-title,
  .chart-title,
  .insights-title,
  .insight-title-text {
    color: #ffffff;
  }

  .date-text,
  .activity-time,
  .chart-subtitle,
  .insights-subtitle,
  .metric-label,
  .insight-description,
  .legend-text {
    color: #8E8E93;
  }

  .activity-card,
  .chart-card,
  .insights-card {
    background: rgba(255, 255, 255, 0.05);
  }

  .metric-item,
  .health-metric-item,
  .insight-item,
  .chart-container {
    background: rgba(255, 255, 255, 0.1);
  }
}
</style>
