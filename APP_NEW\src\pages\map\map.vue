<template>
  <view class="map-container">
    <!-- 状态栏 -->
    <view class="status-bar"></view>
    
    <!-- 页面头部 -->
    <view class="page-header">
      <view class="header-content">
        <view class="page-title">
          <text class="title-text">位置地图</text>
          <text class="subtitle-text">实时定位 • 智能导航</text>
        </view>
        <view class="location-status" :class="locationStatus.class" @tap="refreshLocation">
          <view class="status-icon">{{ locationStatus.icon }}</view>
        </view>
      </view>
    </view>

    <!-- 当前位置信息卡片 -->
    <view class="location-info-card">
      <view class="location-main">
        <view class="location-icon-wrapper">
          <svg class="location-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"></path>
            <circle cx="12" cy="10" r="3"></circle>
          </svg>
          <view class="location-pulse"></view>
        </view>
        <view class="location-details">
          <text class="location-name">{{ currentLocation.name }}</text>
          <text class="location-address">{{ currentLocation.address }}</text>
          <view class="coordinates">
            <text class="coordinate-text">{{ formatCoordinate(currentLocation.latitude, currentLocation.longitude) }}</text>
          </view>
        </view>
      </view>
      <view class="location-actions">
        <view class="action-btn" @tap="shareLocation">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M4 12v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8"></path>
            <path d="M16 6l-4-4-4 4"></path>
            <path d="M12 2v13"></path>
          </svg>
        </view>
      </view>
    </view>

    <!-- 搜索栏 -->
    <view class="search-section">
      <view class="search-input-container">
        <svg class="search-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <circle cx="11" cy="11" r="8"></circle>
          <path d="M21 21l-4.35-4.35"></path>
        </svg>
        <input 
          type="text" 
          class="search-input" 
          placeholder="搜索地点或地址" 
          v-model="searchQuery" 
          @confirm="searchLocation"
          @input="onSearchInput"
        />
        <view class="search-clear" v-if="searchQuery" @tap="clearSearch">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <line x1="18" y1="6" x2="6" y2="18"></line>
            <line x1="6" y1="6" x2="18" y2="18"></line>
          </svg>
        </view>
      </view>
      
      <!-- 搜索建议 -->
      <view class="search-suggestions" v-if="searchSuggestions.length > 0">
        <view 
          class="suggestion-item" 
          v-for="(suggestion, index) in searchSuggestions" 
          :key="index"
          @tap="selectSuggestion(suggestion)"
        >
          <view class="suggestion-icon">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"></path>
              <circle cx="12" cy="10" r="3"></circle>
            </svg>
          </view>
          <view class="suggestion-content">
            <text class="suggestion-name">{{ suggestion.name }}</text>
            <text class="suggestion-address">{{ suggestion.address }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 地图容器 -->
    <view class="map-section">
      <view class="map-header">
        <text class="map-title">位置视图</text>
        <view class="map-controls">
          <view class="control-btn" :class="{ active: mapType === 'satellite' }" @tap="toggleMapType">
            <text>卫星</text>
          </view>
          <view class="control-btn" :class="{ active: showTraffic }" @tap="toggleTraffic">
            <text>路况</text>
          </view>
        </view>
      </view>
      
      <view class="map-wrapper">
        <map
          :latitude="currentLocation.latitude"
          :longitude="currentLocation.longitude"
          :scale="mapScale"
          :markers="mapMarkers"
          :polyline="mapPolylines"
          :show-location="true"
          :enable-traffic="showTraffic"
          class="map-view"
          @markertap="onMarkerTap"
          @tap="onMapTap"
        ></map>
        
        <!-- 地图覆盖层控件 -->
        <view class="map-overlay-controls">
          <view class="control-group">
            <view class="zoom-btn" @tap="zoomIn">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <line x1="12" y1="5" x2="12" y2="19"></line>
                <line x1="5" y1="12" x2="19" y2="12"></line>
              </svg>
            </view>
            <view class="zoom-btn" @tap="zoomOut">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <line x1="5" y1="12" x2="19" y2="12"></line>
              </svg>
            </view>
          </view>
          
          <view class="locate-btn" @tap="centerToCurrentLocation">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"></path>
              <circle cx="12" cy="10" r="3"></circle>
            </svg>
          </view>
        </view>
      </view>
    </view>

    <!-- 附近地点 -->
    <view class="nearby-section">
      <view class="section-header">
        <text class="section-title">附近地点</text>
        <view class="section-filter">
          <view 
            class="filter-tab" 
            :class="{ active: nearbyFilter === item.key }" 
            v-for="item in nearbyFilters" 
            :key="item.key"
            @tap="setNearbyFilter(item.key)"
          >
            <text>{{ item.label }}</text>
          </view>
        </view>
      </view>
      
      <scroll-view class="nearby-list" scroll-y>
        <view 
          class="nearby-item" 
          v-for="(place, index) in nearbyPlaces" 
          :key="index"
          @tap="navigateToPlace(place)"
        >
          <view class="place-icon" :class="place.type">
            <text>{{ getPlaceIcon(place.type) }}</text>
          </view>
          <view class="place-info">
            <text class="place-name">{{ place.name }}</text>
            <text class="place-address">{{ place.address }}</text>
            <view class="place-meta">
              <text class="place-distance">{{ place.distance }}m</text>
              <text class="place-time">{{ place.walkTime }}分钟步行</text>
            </view>
          </view>
          <view class="place-actions">
            <view class="action-icon" @tap.stop="getDirections(place)">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M3 3l3 9 9 3 9-9-9-9-9 3z"></path>
                <path d="M6 12l6-6"></path>
              </svg>
            </view>
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 辐射监测数据覆盖层 -->
    <view class="radiation-overlay" v-if="showRadiationData">
      <view class="overlay-header">
        <text class="overlay-title">辐射监测数据</text>
        <view class="overlay-close" @tap="hideRadiationData">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <line x1="18" y1="6" x2="6" y2="18"></line>
            <line x1="6" y1="6" x2="18" y2="18"></line>
          </svg>
        </view>
      </view>
      <view class="radiation-data">
        <view class="data-item">
          <text class="data-label">当前剂量率</text>
          <text class="data-value">{{ radiationState.currentData.doseRate.toFixed(3) }} μSv/h</text>
        </view>
        <view class="data-item">
          <text class="data-label">累积剂量</text>
          <text class="data-value">{{ radiationState.currentData.doseSum.toFixed(6) }} μSv</text>
        </view>
      </view>
    </view>

    <!-- 底部导航栏 -->
    <BottomNavigation currentPage="map" />
  </view>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { radiationState } from '../../utils/dataStore.js'
import BottomNavigation from '../../components/BottomNavigation.vue'

// 响应式数据
const searchQuery = ref('')
const mapScale = ref(16)
const mapType = ref('standard')
const showTraffic = ref(false)
const showRadiationData = ref(false)
const nearbyFilter = ref('all')

// 当前位置信息
const currentLocation = ref({
  latitude: 39.9042,
  longitude: 116.4074,
  name: '当前位置',
  address: '北京市朝阳区...'
})

// 位置状态
const locationStatus = computed(() => {
  // 根据GPS精度和信号强度返回状态
  return {
    class: 'good',
    icon: '📍'
  }
})

// 搜索建议
const searchSuggestions = ref([])

// 附近筛选选项
const nearbyFilters = ref([
  { key: 'all', label: '全部' },
  { key: 'restaurant', label: '餐厅' },
  { key: 'hospital', label: '医院' },
  { key: 'gas', label: '加油站' },
  { key: 'shopping', label: '购物' }
])

// 附近地点
const nearbyPlaces = ref([
  {
    name: '中关村医院',
    address: '北京市海淀区中关村南大街',
    type: 'hospital',
    distance: 1200,
    walkTime: 15,
    latitude: 39.9042,
    longitude: 116.4074
  },
  {
    name: '肯德基(中关村店)',
    address: '北京市海淀区中关村大街',
    type: 'restaurant',
    distance: 800,
    walkTime: 10,
    latitude: 39.9052,
    longitude: 116.4084
  },
  {
    name: '中石油加油站',
    address: '北京市海淀区知春路',
    type: 'gas',
    distance: 600,
    walkTime: 8,
    latitude: 39.9062,
    longitude: 116.4094
  }
])

// 地图标记
const mapMarkers = computed(() => {
  const markers = [
    {
      id: 1,
      latitude: currentLocation.value.latitude,
      longitude: currentLocation.value.longitude,
      title: '当前位置',
      iconPath: '/static/icons/location.png',
      width: 30,
      height: 30
    }
  ]
  
  // 添加附近地点标记
  nearbyPlaces.value.forEach((place, index) => {
    markers.push({
      id: index + 2,
      latitude: place.latitude,
      longitude: place.longitude,
      title: place.name,
      iconPath: `/static/icons/${place.type}.png`,
      width: 25,
      height: 25
    })
  })
  
  return markers
})

// 地图路线
const mapPolylines = ref([])

// 格式化坐标
const formatCoordinate = (lat, lng) => {
  if (!lat || !lng) return '获取位置中...'
  return `${lat.toFixed(4)}°N, ${lng.toFixed(4)}°E`
}

// 获取地点图标
const getPlaceIcon = (type) => {
  const icons = {
    hospital: '🏥',
    restaurant: '🍽️',
    gas: '⛽',
    shopping: '🛍️',
    default: '📍'
  }
  return icons[type] || icons.default
}

// 刷新位置
const refreshLocation = () => {
  uni.showLoading({ title: '定位中...' })
  
  uni.getLocation({
    type: 'gcj02',
    success: (res) => {
      currentLocation.value = {
        latitude: res.latitude,
        longitude: res.longitude,
        name: '当前位置',
        address: '获取地址中...'
      }
      
      // 获取地址信息
      getAddressFromCoordinates(res.latitude, res.longitude)
      
      uni.hideLoading()
      uni.showToast({
        title: '定位成功',
        icon: 'success'
      })
    },
    fail: () => {
      uni.hideLoading()
      uni.showToast({
        title: '定位失败',
        icon: 'error'
      })
    }
  })
}

// 根据坐标获取地址
const getAddressFromCoordinates = (lat, lng) => {
  // 这里应该调用地图服务的逆地理编码API
  // 模拟获取地址
  setTimeout(() => {
    currentLocation.value.address = '北京市朝阳区建国门外大街1号'
  }, 1000)
}

// 分享位置
const shareLocation = () => {
  const shareText = `我的位置: ${currentLocation.value.name}\n地址: ${currentLocation.value.address}\n坐标: ${formatCoordinate(currentLocation.value.latitude, currentLocation.value.longitude)}`
  
  uni.setClipboardData({
    data: shareText,
    success: () => {
      uni.showToast({
        title: '位置信息已复制',
        icon: 'success'
      })
    }
  })
}

// 搜索输入处理
const onSearchInput = () => {
  if (searchQuery.value.length > 1) {
    // 模拟搜索建议
    searchSuggestions.value = [
      {
        name: '北京大学',
        address: '北京市海淀区颐和园路5号'
      },
      {
        name: '清华大学',
        address: '北京市海淀区清华园1号'
      }
    ].filter(item => 
      item.name.includes(searchQuery.value) || 
      item.address.includes(searchQuery.value)
    )
  } else {
    searchSuggestions.value = []
  }
}

// 清除搜索
const clearSearch = () => {
  searchQuery.value = ''
  searchSuggestions.value = []
}

// 选择搜索建议
const selectSuggestion = (suggestion) => {
  searchQuery.value = suggestion.name
  searchSuggestions.value = []
  searchLocation()
}

// 搜索位置
const searchLocation = () => {
  if (!searchQuery.value) return
  
  uni.showLoading({ title: '搜索中...' })
  
  // 模拟搜索结果
  setTimeout(() => {
    uni.hideLoading()
    uni.showToast({
      title: '搜索完成',
      icon: 'success'
    })
  }, 1500)
}

// 切换地图类型
const toggleMapType = () => {
  mapType.value = mapType.value === 'standard' ? 'satellite' : 'standard'
  uni.showToast({
    title: `已切换到${mapType.value === 'satellite' ? '卫星' : '标准'}地图`,
    icon: 'success'
  })
}

// 切换路况
const toggleTraffic = () => {
  showTraffic.value = !showTraffic.value
  uni.showToast({
    title: showTraffic.value ? '已开启路况' : '已关闭路况',
    icon: 'success'
  })
}

// 放大地图
const zoomIn = () => {
  if (mapScale.value < 20) {
    mapScale.value += 1
  }
}

// 缩小地图
const zoomOut = () => {
  if (mapScale.value > 5) {
    mapScale.value -= 1
  }
}

// 回到当前位置
const centerToCurrentLocation = () => {
  refreshLocation()
}

// 设置附近筛选
const setNearbyFilter = (filter) => {
  nearbyFilter.value = filter
  
  // 这里可以根据筛选条件重新获取附近地点
  if (filter === 'all') {
    // 显示所有地点
  } else {
    // 筛选特定类型的地点
  }
}

// 导航到地点
const navigateToPlace = (place) => {
  uni.openLocation({
    latitude: place.latitude,
    longitude: place.longitude,
    name: place.name,
    address: place.address,
    success: () => {
      console.log('导航成功')
    },
    fail: () => {
      uni.showToast({
        title: '导航失败',
        icon: 'error'
      })
    }
  })
}

// 获取路线指引
const getDirections = (place) => {
  uni.showActionSheet({
    itemList: ['步行路线', '驾车路线', '公交路线'],
    success: (res) => {
      const types = ['walking', 'driving', 'transit']
      const type = types[res.tapIndex]
      
      // 这里应该调用路线规划API
      uni.showToast({
        title: `正在规划${['步行', '驾车', '公交'][res.tapIndex]}路线`,
        icon: 'success'
      })
    }
  })
}

// 地图点击事件
const onMapTap = (e) => {
  console.log('地图点击:', e)
}

// 标记点击事件
const onMarkerTap = (e) => {
  const markerId = e.detail.markerId
  const marker = mapMarkers.value.find(m => m.id === markerId)
  
  if (marker) {
    uni.showModal({
      title: marker.title,
      content: '查看详细信息？',
      success: (res) => {
        if (res.confirm) {
          // 显示标记详情
        }
      }
    })
  }
}

// 显示辐射数据
const showRadiationDataOverlay = () => {
  showRadiationData.value = true
}

// 隐藏辐射数据
const hideRadiationData = () => {
  showRadiationData.value = false
}

// 生命周期
onMounted(() => {
  // 初始化获取位置
  refreshLocation()
  
  // 模拟辐射数据更新
  const radiationInterval = setInterval(() => {
    // 这里可以更新地图上的辐射数据显示
  }, 5000)
  
  onUnmounted(() => {
    clearInterval(radiationInterval)
  })
})
</script>

<style scoped>
.map-container {
  position: relative;
  width: 100%;
  height: 100vh;
  background: #ffffff;
  background-image: 
    radial-gradient(circle at 20% 20%, rgba(102, 126, 234, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(118, 75, 162, 0.05) 0%, transparent 50%);
  overflow: hidden;
}

.status-bar {
  height: 44px;
  background: transparent;
}

/* 页面头部 */
.page-header {
  position: relative;
  padding: 20px 24px 16px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.9) 100%);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.page-title {
  flex: 1;
}

.title-text {
  font-size: 28px;
  font-weight: 700;
  color: #1a1a1a;
  line-height: 1.2;
  margin-bottom: 4px;
}

.subtitle-text {
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

.location-status {
  width: 48px;
  height: 48px;
  border-radius: 24px;
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 20px rgba(79, 172, 254, 0.3);
  transition: all 0.3s ease;
}

.location-status.good {
  background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
}

.location-status:active {
  transform: scale(0.95);
}

.status-icon {
  font-size: 20px;
}

/* 位置信息卡片 */
.location-info-card {
  margin: 20px 24px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 20px;
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.1),
    0 4px 16px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.location-main {
  display: flex;
  align-items: center;
  flex: 1;
}

.location-icon-wrapper {
  position: relative;
  width: 56px;
  height: 56px;
  border-radius: 28px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
}

.location-icon {
  width: 28px;
  height: 28px;
  color: white;
}

.location-pulse {
  position: absolute;
  top: -4px;
  left: -4px;
  right: -4px;
  bottom: -4px;
  border-radius: 50%;
  border: 2px solid rgba(102, 126, 234, 0.3);
  animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.7;
  }
}

.location-details {
  flex: 1;
}

.location-name {
  font-size: 18px;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 4px;
}

.location-address {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}

.coordinates {
  background: rgba(102, 126, 234, 0.1);
  padding: 4px 12px;
  border-radius: 12px;
  display: inline-block;
}

.coordinate-text {
  font-size: 12px;
  color: #667eea;
  font-weight: 500;
  font-family: monospace;
}

.location-actions {
  margin-left: 16px;
}

.action-btn {
  width: 44px;
  height: 44px;
  border-radius: 22px;
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.action-btn:active {
  transform: scale(0.95);
}

.action-btn svg {
  width: 20px;
  height: 20px;
  color: white;
}

/* 搜索区域 */
.search-section {
  margin: 0 24px 20px;
  position: relative;
}

.search-input-container {
  position: relative;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-radius: 16px;
  padding: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
}

.search-icon {
  width: 20px;
  height: 20px;
  color: #666;
  margin-right: 12px;
}

.search-input {
  flex: 1;
  font-size: 16px;
  border: none;
  outline: none;
  background: transparent;
  color: #1a1a1a;
}

.search-input::placeholder {
  color: #999;
}

.search-clear {
  width: 24px;
  height: 24px;
  border-radius: 12px;
  background: rgba(102, 126, 234, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 8px;
  transition: all 0.3s ease;
}

.search-clear:active {
  transform: scale(0.95);
}

.search-clear svg {
  width: 14px;
  height: 14px;
  color: #667eea;
}

/* 搜索建议 */
.search-suggestions {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-radius: 12px;
  margin-top: 8px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.8);
  overflow: hidden;
  z-index: 100;
}

.suggestion-item {
  display: flex;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.suggestion-item:last-child {
  border-bottom: none;
}

.suggestion-item:active {
  background: rgba(102, 126, 234, 0.1);
}

.suggestion-icon {
  width: 36px;
  height: 36px;
  border-radius: 18px;
  background: rgba(102, 126, 234, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
}

.suggestion-icon svg {
  width: 18px;
  height: 18px;
  color: #667eea;
}

.suggestion-content {
  flex: 1;
}

.suggestion-name {
  font-size: 16px;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 4px;
}

.suggestion-address {
  font-size: 14px;
  color: #666;
}

/* 地图区域 */
.map-section {
  margin: 0 24px 20px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.8);
}

.map-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.map-title {
  font-size: 18px;
  font-weight: 600;
  color: #1a1a1a;
}

.map-controls {
  display: flex;
  gap: 8px;
}

.control-btn {
  padding: 8px 16px;
  border-radius: 20px;
  background: rgba(102, 126, 234, 0.1);
  border: 1px solid rgba(102, 126, 234, 0.2);
  transition: all 0.3s ease;
}

.control-btn.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-color: transparent;
}

.control-btn text {
  font-size: 14px;
  font-weight: 500;
  color: #667eea;
}

.control-btn.active text {
  color: white;
}

.control-btn:active {
  transform: scale(0.95);
}

.map-wrapper {
  position: relative;
  height: 300px;
}

.map-view {
  width: 100%;
  height: 100%;
}

/* 地图覆盖层控件 */
.map-overlay-controls {
  position: absolute;
  top: 16px;
  right: 16px;
  display: flex;
  flex-direction: column;
  gap: 8px;
  z-index: 10;
}

.control-group {
  display: flex;
  flex-direction: column;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.zoom-btn {
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: transparent;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.zoom-btn:last-child {
  border-bottom: none;
}

.zoom-btn:active {
  background: rgba(102, 126, 234, 0.1);
}

.zoom-btn svg {
  width: 20px;
  height: 20px;
  color: #667eea;
}

.locate-btn {
  width: 44px;
  height: 44px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
}

.locate-btn:active {
  transform: scale(0.95);
}

.locate-btn svg {
  width: 20px;
  height: 20px;
  color: white;
}

/* 附近地点 */
.nearby-section {
  margin: 0 24px 120px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.8);
}

.section-header {
  padding: 20px 20px 16px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 16px;
}

.section-filter {
  display: flex;
  gap: 8px;
  overflow-x: auto;
}

.filter-tab {
  padding: 8px 16px;
  border-radius: 20px;
  background: rgba(102, 126, 234, 0.1);
  border: 1px solid rgba(102, 126, 234, 0.2);
  white-space: nowrap;
  transition: all 0.3s ease;
}

.filter-tab.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-color: transparent;
}

.filter-tab text {
  font-size: 14px;
  font-weight: 500;
  color: #667eea;
}

.filter-tab.active text {
  color: white;
}

.filter-tab:active {
  transform: scale(0.95);
}

.nearby-list {
  max-height: 300px;
  padding: 0 20px 20px;
}

.nearby-item {
  display: flex;
  align-items: center;
  padding: 16px 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.nearby-item:last-child {
  border-bottom: none;
}

.nearby-item:active {
  background: rgba(102, 126, 234, 0.05);
}

.place-icon {
  width: 48px;
  height: 48px;
  border-radius: 24px;
  background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 20px;
}

.place-icon.hospital {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
}

.place-icon.restaurant {
  background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
}

.place-icon.gas {
  background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
}

.place-icon.shopping {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
}

.place-info {
  flex: 1;
}

.place-name {
  font-size: 16px;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 4px;
}

.place-address {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}

.place-meta {
  display: flex;
  gap: 16px;
}

.place-distance, .place-time {
  font-size: 12px;
  color: #999;
  font-weight: 500;
}

.place-actions {
  margin-left: 16px;
}

.action-icon {
  width: 36px;
  height: 36px;
  border-radius: 18px;
  background: rgba(102, 126, 234, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.action-icon:active {
  transform: scale(0.95);
  background: rgba(102, 126, 234, 0.2);
}

.action-icon svg {
  width: 16px;
  height: 16px;
  color: #667eea;
}

/* 辐射数据覆盖层 */
.radiation-overlay {
  position: fixed;
  bottom: 120px;
  left: 24px;
  right: 24px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.8);
  z-index: 1000;
  animation: slideUp 0.3s ease;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(100%);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.overlay-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.overlay-title {
  font-size: 16px;
  font-weight: 600;
  color: #1a1a1a;
}

.overlay-close {
  width: 32px;
  height: 32px;
  border-radius: 16px;
  background: rgba(102, 126, 234, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.overlay-close:active {
  transform: scale(0.95);
}

.overlay-close svg {
  width: 16px;
  height: 16px;
  color: #667eea;
}

.radiation-data {
  padding: 20px;
  display: flex;
  gap: 20px;
}

.data-item {
  flex: 1;
  text-align: center;
}

.data-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}

.data-value {
  font-size: 18px;
  font-weight: 600;
  color: #1a1a1a;
  font-family: monospace;
}

/* 响应式设计 */
@media (max-width: 375px) {
  .page-header {
    padding: 16px 20px 12px;
  }
  
  .title-text {
    font-size: 24px;
  }
  
  .location-info-card {
    margin: 16px 20px;
    padding: 16px;
  }
  
  .search-section,
  .map-section,
  .nearby-section {
    margin-left: 20px;
    margin-right: 20px;
  }
  
  .location-icon-wrapper {
    width: 48px;
    height: 48px;
    border-radius: 24px;
  }
  
  .location-icon {
    width: 24px;
    height: 24px;
  }
}

/* 加载动画 */
.map-container * {
  animation-duration: 0.8s;
  animation-fill-mode: both;
  animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

.page-header {
  animation-name: fadeInDown;
  animation-delay: 0.1s;
}

.location-info-card {
  animation-name: fadeInUp;
  animation-delay: 0.2s;
}

.search-section {
  animation-name: fadeInUp;
  animation-delay: 0.3s;
}

.map-section {
  animation-name: fadeInUp;
  animation-delay: 0.4s;
}

.nearby-section {
  animation-name: fadeInUp;
  animation-delay: 0.5s;
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style> 