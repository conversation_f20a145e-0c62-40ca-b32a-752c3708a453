# 智能家居系统接口文档

本文档详细描述了智能家居系统的所有API接口信息，包含接口URL、请求方式、参数和响应格式等内容。

**基础URL**: `https://zarkychmvpiv.sealoshzh.site`

**请求头认证**: 除登录注册外的接口都需要在请求头中携带token
```
Authorization: Bearer {token}
```

**通用返回格式**:
```json
{
  "code": 200,     // 状态码：200成功，4xx客户端错误，5xx服务器错误
  "message": "操作信息",
  "data": { ... }  // 响应数据，失败时为null
}
```

## 目录

1. [用户管理接口](#1-用户管理接口)
2. [设备管理接口](#2-设备管理接口)
3. [房间管理接口](#3-房间管理接口)
4. [数据监控接口](#4-数据监控接口)

## 1. 用户管理接口

### 1.1 用户注册

- **URL**: `/api/user/register`
- **方法**: `POST`
- **请求体**:
  ```json
  {
    "username": "用户名",
    "password": "密码",
    "email": "邮箱地址",
    "phone": "手机号码",
    "verificationCode": "验证码"
  }
  ```
- **成功响应** (200):
  ```json
  {
    "code": 200,
    "message": "注册成功",
    "data": {
      "userId": "用户ID",
      "username": "用户名",
      "token": "认证令牌"
    }
  }
  ```
- **失败响应**:
  - 400 参数错误:
    ```json
    {
      "code": 400,
      "message": "缺少必要参数",
      "data": null
    }
    ```
  - 400 验证码错误:
    ```json
    {
      "code": 400,
      "message": "验证码错误",
      "data": null
    }
    ```
  - 400 用户已存在:
    ```json
    {
      "code": 400,
      "message": "用户名、邮箱或手机号已被注册",
      "data": null
    }
    ```
  - 500 服务器错误:
    ```json
    {
      "code": 500,
      "message": "服务器错误",
      "data": null
    }
    ```

### 1.2 用户登录

- **URL**: `/api/user/login`
- **方法**: `POST`
- **请求体**:
  ```json
  {
    "username": "用户名/邮箱/手机号",
    "password": "密码"
  }
  ```
- **成功响应** (200):
  ```json
  {
    "code": 200,
    "message": "登录成功",
    "data": {
      "userId": "用户ID",
      "username": "用户名",
      "token": "认证令牌",
      "avatar": "头像URL"
    }
  }
  ```
- **失败响应**:
  - 400 参数错误:
    ```json
    {
      "code": 400,
      "message": "用户名和密码不能为空",
      "data": null
    }
    ```
  - 401 用户不存在:
    ```json
    {
      "code": 401,
      "message": "用户不存在",
      "data": null
    }
    ```
  - 401 密码错误:
    ```json
    {
      "code": 401,
      "message": "密码错误",
      "data": null
    }
    ```
  - 500 服务器错误:
    ```json
    {
      "code": 500,
      "message": "服务器错误",
      "data": null
    }
    ```

### 1.3 发送验证码

- **URL**: `/api/user/sendVerificationCode`
- **方法**: `POST`
- **请求体**:
  ```json
  {
    "phone": "手机号码"
  }
  ```
- **成功响应** (200):
  ```json
  {
    "code": 200,
    "message": "验证码发送成功",
    "data": {
      "expireTime": 300 // 验证码有效期（秒）
    }
  }
  ```
- **失败响应**:
  - 400 参数错误:
    ```json
    {
      "code": 400,
      "message": "手机号不能为空",
      "data": null
    }
    ```
  - 500 服务器错误:
    ```json
    {
      "code": 500,
      "message": "服务器错误",
      "data": null
    }
    ```

### 1.4 获取用户信息

- **URL**: `/api/user/info`
- **方法**: `GET`
- **请求头**: 
  ```
  Authorization: Bearer {token}
  ```
- **成功响应** (200):
  ```json
  {
    "code": 200,
    "message": "获取成功",
    "data": {
      "userId": "用户ID",
      "username": "用户名",
      "email": "邮箱",
      "phone": "手机号",
      "avatar": "头像URL",
      "createTime": "账号创建时间"
    }
  }
  ```
- **失败响应**:
  - 401 认证失败:
    ```json
    {
      "code": 401,
      "message": "未提供认证令牌",
      "data": null
    }
    ```
  - 404 用户不存在:
    ```json
    {
      "code": 404,
      "message": "用户不存在",
      "data": null
    }
    ```
  - 500 服务器错误:
    ```json
    {
      "code": 500,
      "message": "服务器错误",
      "data": null
    }
    ```

### 1.5 修改用户信息

- **URL**: `/api/user/updateInfo`
- **方法**: `PUT`
- **请求头**: 
  ```
  Authorization: Bearer {token}
  ```
- **请求体**:
  ```json
  {
    "username": "新用户名",
    "email": "新邮箱",
    "avatar": "新头像"
  }
  ```
- **成功响应** (200):
  ```json
  {
    "code": 200,
    "message": "修改成功",
    "data": {
      "userId": "用户ID",
      "username": "新用户名",
      "email": "新邮箱",
      "avatar": "新头像URL"
    }
  }
  ```
- **失败响应**:
  - 401 认证失败:
    ```json
    {
      "code": 401,
      "message": "未提供认证令牌",
      "data": null
    }
    ```
  - 404 用户不存在:
    ```json
    {
      "code": 404,
      "message": "用户不存在",
      "data": null
    }
    ```
  - 500 服务器错误:
    ```json
    {
      "code": 500,
      "message": "服务器错误",
      "data": null
    }
    ```

### 1.6 修改密码

- **URL**: `/api/user/changePassword`
- **方法**: `PUT`
- **请求头**: 
  ```
  Authorization: Bearer {token}
  ```
- **请求体**:
  ```json
  {
    "oldPassword": "旧密码",
    "newPassword": "新密码"
  }
  ```
- **成功响应** (200):
  ```json
  {
    "code": 200,
    "message": "密码修改成功",
    "data": null
  }
  ```
- **失败响应**:
  - 400 参数错误:
    ```json
    {
      "code": 400,
      "message": "旧密码和新密码不能为空",
      "data": null
    }
    ```
  - 401 认证失败:
    ```json
    {
      "code": 401,
      "message": "未提供认证令牌",
      "data": null
    }
    ```
  - 401 密码错误:
    ```json
    {
      "code": 401,
      "message": "旧密码错误",
      "data": null
    }
    ```
  - 404 用户不存在:
    ```json
    {
      "code": 404,
      "message": "用户不存在",
      "data": null
    }
    ```
  - 500 服务器错误:
    ```json
    {
      "code": 500,
      "message": "服务器错误",
      "data": null
    }
    ```

### 1.7 重置密码

- **URL**: `/api/user/resetPassword`
- **方法**: `POST`
- **请求体**:
  ```json
  {
    "phone": "手机号",
    "verificationCode": "验证码",
    "newPassword": "新密码"
  }
  ```
- **成功响应** (200):
  ```json
  {
    "code": 200,
    "message": "密码重置成功",
    "data": null
  }
  ```
- **失败响应**:
  - 400 参数错误:
    ```json
    {
      "code": 400,
      "message": "缺少必要参数",
      "data": null
    }
    ```
  - 400 验证码错误:
    ```json
    {
      "code": 400,
      "message": "验证码错误",
      "data": null
    }
    ```
  - 404 用户不存在:
    ```json
    {
      "code": 404,
      "message": "用户不存在",
      "data": null
    }
    ```
  - 500 服务器错误:
    ```json
    {
      "code": 500,
      "message": "服务器错误",
      "data": null
    }
    ```

## 2. 设备管理接口

### 2.1 获取设备列表

- **URL**: `/api/device/list`
- **方法**: `GET`
- **请求头**: 
  ```
  Authorization: Bearer {token}
  ```
- **请求参数**:
  ```
  roomId (可选): 房间ID，不传则获取所有设备
  ```
- **成功响应** (200):
  ```json
  {
    "code": 200,
    "message": "获取成功",
    "data": {
      "devices": [
        {
          "deviceId": "设备ID",
          "deviceName": "设备名称",
          "deviceType": "设备类型",
          "status": "设备状态",
          "roomId": "房间ID",
          "roomName": "房间名称",
          "icon": "设备图标",
          "isOnline": true,
          "currentValue": "当前值",
          "lastUpdateTime": "最后更新时间"
        }
      ],
      "total": 10
    }
  }
  ```
- **失败响应**:
  - 401 认证失败:
    ```json
    {
      "code": 401,
      "message": "未提供认证令牌",
      "data": null
    }
    ```
  - 500 服务器错误:
    ```json
    {
      "code": 500,
      "message": "服务器错误",
      "data": null
    }
    ```

### 2.2 控制设备

- **URL**: `/api/device/control`
- **方法**: `POST`
- **请求头**: 
  ```
  Authorization: Bearer {token}
  ```
- **请求体**:
  ```json
  {
    "deviceId": "设备ID",
    "action": "操作类型",
    "value": "设置值"
  }
  ```
- **成功响应** (200):
  ```json
  {
    "code": 200,
    "message": "操作成功",
    "data": {
      "deviceId": "设备ID",
      "status": "新状态",
      "currentValue": "当前值"
    }
  }
  ```
- **失败响应**:
  - 400 参数错误:
    ```json
    {
      "code": 400,
      "message": "参数错误",
      "data": null
    }
    ```
  - 401 认证失败:
    ```json
    {
      "code": 401,
      "message": "未提供认证令牌",
      "data": null
    }
    ```
  - 404 设备不存在:
    ```json
    {
      "code": 404,
      "message": "设备不存在",
      "data": null
    }
    ```
  - 500 控制失败:
    ```json
    {
      "code": 500,
      "message": "控制设备失败: {错误原因}",
      "data": null
    }
    ```
  - 500 服务器错误:
    ```json
    {
      "code": 500,
      "message": "服务器错误",
      "data": null
    }
    ```

### 2.3 添加设备

- **URL**: `/api/device/add`
- **方法**: `POST`
- **请求头**: 
  ```
  Authorization: Bearer {token}
  ```
- **请求体**:
  ```json
  {
    "deviceName": "设备名称",
    "deviceType": "设备类型",
    "roomId": "房间ID",
    "icon": "设备图标"
  }
  ```
- **成功响应** (200):
  ```json
  {
    "code": 200,
    "message": "添加成功",
    "data": {
      "deviceId": "新设备ID",
      "deviceName": "设备名称",
      "deviceType": "设备类型",
      "roomId": "房间ID",
      "status": "初始状态",
      "icon": "设备图标"
    }
  }
  ```
- **失败响应**:
  - 400 参数错误:
    ```json
    {
      "code": 400,
      "message": "缺少必要参数",
      "data": null
    }
    ```
  - 401 认证失败:
    ```json
    {
      "code": 401,
      "message": "未提供认证令牌",
      "data": null
    }
    ```
  - 404 房间不存在:
    ```json
    {
      "code": 404,
      "message": "指定的房间不存在",
      "data": null
    }
    ```
  - 500 服务器错误:
    ```json
    {
      "code": 500,
      "message": "服务器错误",
      "data": null
    }
    ```

### 2.4 修改设备信息

- **URL**: `/api/device/update`
- **方法**: `PUT`
- **请求头**: 
  ```
  Authorization: Bearer {token}
  ```
- **请求体**:
  ```json
  {
    "deviceId": "设备ID",
    "deviceName": "新设备名称",
    "roomId": "新房间ID",
    "icon": "新设备图标"
  }
  ```
- **成功响应** (200):
  ```json
  {
    "code": 200,
    "message": "修改成功",
    "data": {
      "deviceId": "设备ID",
      "deviceName": "新设备名称",
      "roomId": "新房间ID",
      "roomName": "新房间名称",
      "icon": "新设备图标"
    }
  }
  ```
- **失败响应**:
  - 400 参数错误:
    ```json
    {
      "code": 400,
      "message": "缺少设备ID",
      "data": null
    }
    ```
  - 401 认证失败:
    ```json
    {
      "code": 401,
      "message": "未提供认证令牌",
      "data": null
    }
    ```
  - 404 设备不存在:
    ```json
    {
      "code": 404,
      "message": "设备不存在",
      "data": null
    }
    ```
  - 404 房间不存在:
    ```json
    {
      "code": 404,
      "message": "指定的房间不存在",
      "data": null
    }
    ```
  - 500 服务器错误:
    ```json
    {
      "code": 500,
      "message": "服务器错误",
      "data": null
    }
    ```

### 2.5 删除设备

- **URL**: `/api/device/delete`
- **方法**: `DELETE`
- **请求头**: 
  ```
  Authorization: Bearer {token}
  ```
- **请求参数**:
  ```
  deviceId: 设备ID
  ```
- **成功响应** (200):
  ```json
  {
    "code": 200,
    "message": "删除成功",
    "data": null
  }
  ```
- **失败响应**:
  - 400 参数错误:
    ```json
    {
      "code": 400,
      "message": "缺少设备ID",
      "data": null
    }
    ```
  - 401 认证失败:
    ```json
    {
      "code": 401,
      "message": "未提供认证令牌",
      "data": null
    }
    ```
  - 404 设备不存在:
    ```json
    {
      "code": 404,
      "message": "设备不存在",
      "data": null
    }
    ```
  - 500 服务器错误:
    ```json
    {
      "code": 500,
      "message": "服务器错误",
      "data": null
    }
    ```

## 3. 房间管理接口

### 3.1 获取房间列表

- **URL**: `/api/room/list`
- **方法**: `GET`
- **请求头**: 
  ```
  Authorization: Bearer {token}
  ```
- **成功响应** (200):
  ```json
  {
    "code": 200,
    "message": "获取成功",
    "data": {
      "rooms": [
        {
          "roomId": "房间ID",
          "roomName": "房间名称",
          "deviceCount": 5,
          "icon": "房间图标"
        }
      ],
      "total": 4
    }
  }
  ```
- **失败响应**:
  - 401 认证失败:
    ```json
    {
      "code": 401,
      "message": "未提供认证令牌",
      "data": null
    }
    ```
  - 500 服务器错误:
    ```json
    {
      "code": 500,
      "message": "服务器错误",
      "data": null
    }
    ```

### 3.2 添加房间

- **URL**: `/api/room/add`
- **方法**: `POST`
- **请求头**: 
  ```
  Authorization: Bearer {token}
  ```
- **请求体**:
  ```json
  {
    "roomName": "房间名称",
    "icon": "房间图标"
  }
  ```
- **成功响应** (200):
  ```json
  {
    "code": 200,
    "message": "添加成功",
    "data": {
      "roomId": "新房间ID",
      "roomName": "房间名称",
      "icon": "房间图标"
    }
  }
  ```
- **失败响应**:
  - 400 参数错误:
    ```json
    {
      "code": 400,
      "message": "缺少房间名称",
      "data": null
    }
    ```
  - 401 认证失败:
    ```json
    {
      "code": 401,
      "message": "未提供认证令牌",
      "data": null
    }
    ```
  - 500 服务器错误:
    ```json
    {
      "code": 500,
      "message": "服务器错误",
      "data": null
    }
    ```

## 4. 数据监控接口

### 4.1 获取设备实时数据

- **URL**: `/api/data/realtime`
- **方法**: `GET`
- **请求头**: 
  ```
  Authorization: Bearer {token}
  ```
- **请求参数**:
  ```
  deviceId: 设备ID
  ```
- **成功响应** (200):
  ```json
  {
    "code": 200,
    "message": "获取成功",
    "data": {
      "deviceId": "设备ID",
      "deviceName": "设备名称",
      "status": "设备状态",
      "currentValue": "当前值",
      "lastUpdateTime": "最后更新时间",
      "power": "功率",
      "additionalData": {
        // 不同类型设备的特定数据
        "mode": "工作模式", // pH传感器特有
        "pumpName": "水泵名称", // 水泵控制器特有
        "pumpId": 1 // 水泵控制器特有
      }
    }
  }
  ```
- **失败响应**:
  - 400 参数错误:
    ```json
    {
      "code": 400,
      "message": "缺少设备ID",
      "data": null
    }
    ```
  - 401 认证失败:
    ```json
    {
      "code": 401,
      "message": "未提供认证令牌",
      "data": null
    }
    ```
  - 404 设备不存在:
    ```json
    {
      "code": 404,
      "message": "设备不存在",
      "data": null
    }
    ```
  - 500 服务器错误:
    ```json
    {
      "code": 500,
      "message": "服务器错误",
      "data": null
    }
    ```

### 4.2 获取设备历史数据

- **URL**: `/api/data/history`
- **方法**: `GET`
- **请求头**: 
  ```
  Authorization: Bearer {token}
  ```
- **请求参数**:
  ```
  deviceId: 设备ID
  startTime: 开始时间 (ISO格式)
  endTime: 结束时间 (ISO格式)
  type: 数据类型 (ph/water/pump/power/temperature/humidity/other)
  interval: 数据间隔（hour/day/week/month，可选）
  ```
- **成功响应** (200):
  ```json
  {
    "code": 200,
    "message": "获取成功",
    "data": {
      "deviceId": "设备ID",
      "deviceName": "设备名称",
      "type": "数据类型",
      "unit": "单位",
      "dataPoints": [
        {
          "timestamp": "时间戳",
          "value": "值"
        }
      ],
      "statistics": {
        "max": "最大值",
        "min": "最小值",
        "avg": "平均值"
      }
    }
  }
  ```
- **失败响应**:
  - 400 参数错误:
    ```json
    {
      "code": 400,
      "message": "缺少必要参数",
      "data": null
    }
    ```
  - 401 认证失败:
    ```json
    {
      "code": 401,
      "message": "未提供认证令牌",
      "data": null
    }
    ```
  - 404 设备不存在:
    ```json
    {
      "code": 404,
      "message": "设备不存在",
      "data": null
    }
    ```
  - 500 服务器错误:
    ```json
    {
      "code": 500,
      "message": "服务器错误",
      "data": null
    }
    ```

### 4.3 获取能耗统计

- **URL**: `/api/data/energyStats`
- **方法**: `GET`
- **请求头**: 
  ```
  Authorization: Bearer {token}
  ```
- **请求参数**:
  ```
  period: 统计周期（day/week/month/year）
  ```
- **成功响应** (200):
  ```json
  {
    "code": 200,
    "message": "获取成功",
    "data": {
      "totalEnergy": "总能耗",
      "unit": "kWh",
      "comparisonRatio": "与上期比较百分比",
      "details": [
        {
          "timestamp": "时间",
          "value": "能耗值"
        }
      ],
      "deviceDistribution": [
        {
          "deviceId": "设备ID",
          "deviceName": "设备名称",
          "energy": "能耗值",
          "percentage": "百分比"
        }
      ]
    }
  }
  ```
- **失败响应**:
  - 401 认证失败:
    ```json
    {
      "code": 401,
      "message": "未提供认证令牌",
      "data": null
    }
    ```
  - 500 服务器错误:
    ```json
    {
      "code": 500,
      "message": "服务器错误",
      "data": null
    }
    ```

## 注意事项

1. 所有需要认证的接口必须在请求头中携带有效的token，否则会返回401错误。
2. 对于分页接口，如未提供分页参数，默认返回第一页数据。
3. 时间戳格式应为ISO标准格式，例如：`2023-04-01T08:30:00Z`。
4. 设备类型包括：pumpControl(水泵控制)、sensor(传感器)、switch(开关)、light(灯光)、temperature(温度)、humidity(湿度)、other(其他)。
5. 测试环境使用的验证码固定为 `123456`。 