<template>
  <view class="loading-container" v-if="visible">
    <view class="loading-overlay" v-if="overlay" @tap="handleOverlayTap"></view>
    <view class="loading-content" :class="{ 'with-overlay': overlay }">
      <view class="spinner-container">
        <view class="spinner" :class="type">
          <view class="dot" v-if="type === 'dots'"></view>
          <view class="dot" v-if="type === 'dots'"></view>
          <view class="dot" v-if="type === 'dots'"></view>
          <view class="ring" v-if="type === 'ring'"></view>
          <view class="pulse" v-if="type === 'pulse'"></view>
        </view>
      </view>
      
      <text class="loading-text" v-if="text">{{ text }}</text>
      
      <view class="loading-progress" v-if="showProgress">
        <view class="progress-bar">
          <view class="progress-fill" :style="{ width: progress + '%' }"></view>
        </view>
        <text class="progress-text">{{ progress }}%</text>
      </view>
      
      <text class="cancel-btn" v-if="showCancel" @tap="handleCancel">取消</text>
    </view>
  </view>
</template>

<script>
export default {
  name: 'LoadingSpinner',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    text: {
      type: String,
      default: ''
    },
    type: {
      type: String,
      default: 'ring' // ring, dots, pulse
    },
    overlay: {
      type: Boolean,
      default: true
    },
    showProgress: {
      type: Boolean,
      default: false
    },
    progress: {
      type: Number,
      default: 0
    },
    showCancel: {
      type: Boolean,
      default: false
    }
  },
  emits: ['cancel', 'overlay-tap'],
  setup(props, { emit }) {
    const handleCancel = () => {
      emit('cancel')
    }
    
    const handleOverlayTap = () => {
      emit('overlay-tap')
    }
    
    return {
      handleCancel,
      handleOverlayTap
    }
  }
}
</script>

<style scoped>
.loading-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(5px);
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20rpx;
  padding: 40rpx;
  background: rgba(26, 26, 46, 0.9);
  border-radius: 20rpx;
  border: 1px solid rgba(255, 255, 255, 0.1);
  z-index: 1;
}

.loading-content.with-overlay {
  backdrop-filter: blur(20px);
}

.spinner-container {
  width: 80rpx;
  height: 80rpx;
  position: relative;
}

/* Ring Spinner */
.spinner.ring {
  width: 100%;
  height: 100%;
  border: 4rpx solid rgba(255, 255, 255, 0.1);
  border-top: 4rpx solid #3cc51f;
  border-radius: 50%;
  animation: ring-spin 1s linear infinite;
}

@keyframes ring-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Dots Spinner */
.spinner.dots {
  display: flex;
  gap: 8rpx;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.spinner.dots .dot {
  width: 12rpx;
  height: 12rpx;
  background: #3cc51f;
  border-radius: 50%;
  animation: dots-bounce 1.4s ease-in-out both infinite;
}

.spinner.dots .dot:nth-child(1) { animation-delay: -0.32s; }
.spinner.dots .dot:nth-child(2) { animation-delay: -0.16s; }
.spinner.dots .dot:nth-child(3) { animation-delay: 0s; }

@keyframes dots-bounce {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1.2);
    opacity: 1;
  }
}

/* Pulse Spinner */
.spinner.pulse {
  width: 100%;
  height: 100%;
  background: #3cc51f;
  border-radius: 50%;
  animation: pulse-scale 1.5s ease-in-out infinite;
}

@keyframes pulse-scale {
  0% {
    transform: scale(0);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 0;
  }
}

.loading-text {
  font-size: 26rpx;
  color: #ffffff;
  text-align: center;
  max-width: 400rpx;
  line-height: 1.4;
}

.loading-progress {
  width: 300rpx;
  display: flex;
  flex-direction: column;
  gap: 10rpx;
  align-items: center;
}

.progress-bar {
  width: 100%;
  height: 8rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #3cc51f, #4caf50);
  border-radius: 4rpx;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.7);
}

.cancel-btn {
  padding: 15rpx 30rpx;
  background: rgba(220, 53, 69, 0.1);
  border: 1px solid rgba(220, 53, 69, 0.3);
  border-radius: 20rpx;
  color: #dc3545;
  font-size: 24rpx;
  font-weight: 500;
  transition: all 0.3s ease;
}

.cancel-btn:active {
  transform: scale(0.95);
  background: rgba(220, 53, 69, 0.2);
}
</style> 