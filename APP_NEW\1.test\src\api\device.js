/**
 * 设备相关接口
 */
import request from '../utils/request';

/**
 * 获取设备列表
 * @param {Object} params 查询参数
 * @returns {Promise} Promise对象
 */
export function getDeviceList(params) {
  return request.get('/api/device/list', params);
}

/**
 * 控制设备
 * @param {Object} data 控制信息
 * @returns {Promise} Promise对象
 */
export function controlDevice(data) {
  return request.post('/api/device/control', data);
}

/**
 * 添加设备
 * @param {Object} data 设备信息
 * @returns {Promise} Promise对象
 */
export function addDevice(data) {
  return request.post('/api/device/add', data);
}

/**
 * 修改设备信息
 * @param {Object} data 设备信息
 * @returns {Promise} Promise对象
 */
export function updateDevice(data) {
  return request.put('/api/device/update', data);
}

/**
 * 删除设备
 * @param {String} deviceId 设备ID
 * @returns {Promise} Promise对象
 */
export function deleteDevice(deviceId) {
  return request.delete('/api/device/delete', { deviceId });
} 