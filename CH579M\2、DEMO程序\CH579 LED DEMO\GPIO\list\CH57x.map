Component: ARM Compiler 5.06 update 7 (build 960) Tool: armlink [4d3601]

==============================================================================

Section Cross References

    main.o(.text) refers to ch57x_gpio.o(.text) for GPIOB_ModeCfg
    ch57x_adc.o(.text) refers to idiv.o(.text) for __aeabi_idivmod
    ch57x_clk.o(.text) refers to ch57x_sys.o(.text) for mDelayuS
    ch57x_clk.o(.text) refers to ch57x_pwr.o(.text) for PowerMonitor
    ch57x_clk.o(.text) refers to ch57x_clk.o(i.__ARM_common_switch8) for __ARM_common_switch8
    ch57x_clk.o(.text) refers to idiv.o(.text) for __aeabi_idivmod
    ch57x_clk.o(.text) refers to uidiv.o(.text) for __aeabi_uidivmod
    ch57x_flash.o(.text) refers to ch57x_pwr.o(.text) for PowerMonitor
    ch57x_flash.o(.text) refers to ch57x_flash.o(.data) for codeflash_access_flag1
    ch57x_gpio.o(.text) refers to ch57x_clk.o(i.__ARM_common_switch8) for __ARM_common_switch8
    ch57x_pwm.o(.text) refers to ch57x_clk.o(i.__ARM_common_switch8) for __ARM_common_switch8
    ch57x_pwr.o(.text) refers to ch57x_sys.o(.text) for mDelayuS
    ch57x_uart1.o(.text) refers to ch57x_clk.o(.text) for GetSysClock
    ch57x_uart1.o(.text) refers to uidiv.o(.text) for __aeabi_uidivmod
    ch57x_int.o(.text) refers to ch57x_pwr.o(.text) for PowerMonitor
    startup_armcm0.o(RESET) refers to startup_armcm0.o(.text) for Reset_Handler
    startup_armcm0.o(RESET) refers to ch57x_int.o(.text) for NMI_Handler
    startup_armcm0.o(.text) refers to ch57x_clk.o(.text) for SystemInit
    startup_armcm0.o(.text) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry10a.o(.ARM.Collect$$$$0000000F) for __rt_final_cpp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry11a.o(.ARM.Collect$$$$00000011) for __rt_final_exit
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry12b.o(.ARM.Collect$$$$0000000E) for __rt_lib_shutdown_fini
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry7b.o(.ARM.Collect$$$$00000008) for _main_clock
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry8b.o(.ARM.Collect$$$$0000000A) for _main_cpp_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry9a.o(.ARM.Collect$$$$0000000B) for _main_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry5.o(.ARM.Collect$$$$00000004) for _main_scatterload
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry2.o(.ARM.Collect$$$$00000001) for _main_stk
    idiv.o(.text) refers to uidiv.o(.text) for __aeabi_uidivmod
    entry2.o(.ARM.Collect$$$$00000001) refers to entry2.o(.ARM.Collect$$$$00002712) for __lit__00000000
    entry2.o(__vectab_stack_and_reset_area) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry5.o(.ARM.Collect$$$$00000004) refers to init.o(.text) for __scatterload
    entry9a.o(.ARM.Collect$$$$0000000B) refers to main.o(.text) for main
    entry9b.o(.ARM.Collect$$$$0000000C) refers to main.o(.text) for main
    init.o(.text) refers to entry5.o(.ARM.Collect$$$$00000004) for __main_after_scatterload


==============================================================================

Removing Unused input sections from the image.

    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing main.o(.bss), (100 bytes).
    Removing main.o(.data), (21 bytes).
    Removing ch57x_adc.o(.rev16_text), (4 bytes).
    Removing ch57x_adc.o(.revsh_text), (4 bytes).
    Removing ch57x_adc.o(.text), (564 bytes).
    Removing ch57x_clk.o(.rev16_text), (4 bytes).
    Removing ch57x_clk.o(.revsh_text), (4 bytes).
    Removing ch57x_flash.o(.rev16_text), (4 bytes).
    Removing ch57x_flash.o(.revsh_text), (4 bytes).
    Removing ch57x_flash.o(.text), (1072 bytes).
    Removing ch57x_flash.o(.data), (2 bytes).
    Removing ch57x_gpio.o(.rev16_text), (4 bytes).
    Removing ch57x_gpio.o(.revsh_text), (4 bytes).
    Removing ch57x_lcd.o(.rev16_text), (4 bytes).
    Removing ch57x_lcd.o(.revsh_text), (4 bytes).
    Removing ch57x_lcd.o(.text), (12 bytes).
    Removing ch57x_pwm.o(.rev16_text), (4 bytes).
    Removing ch57x_pwm.o(.revsh_text), (4 bytes).
    Removing ch57x_pwm.o(.text), (272 bytes).
    Removing ch57x_pwr.o(.rev16_text), (4 bytes).
    Removing ch57x_pwr.o(.revsh_text), (4 bytes).
    Removing ch57x_spi0.o(.rev16_text), (4 bytes).
    Removing ch57x_spi0.o(.revsh_text), (4 bytes).
    Removing ch57x_spi0.o(.text), (840 bytes).
    Removing ch57x_sys.o(.rev16_text), (4 bytes).
    Removing ch57x_sys.o(.revsh_text), (4 bytes).
    Removing ch57x_timer0.o(.rev16_text), (4 bytes).
    Removing ch57x_timer0.o(.revsh_text), (4 bytes).
    Removing ch57x_timer0.o(.text), (88 bytes).
    Removing ch57x_uart1.o(.rev16_text), (4 bytes).
    Removing ch57x_uart1.o(.revsh_text), (4 bytes).
    Removing ch57x_uart1.o(.text), (200 bytes).
    Removing ch57x_int.o(.rev16_text), (4 bytes).
    Removing ch57x_int.o(.revsh_text), (4 bytes).
    Removing startup_armcm0.o(STACK), (1024 bytes).
    Removing startup_armcm0.o(HEAP), (1024 bytes).

38 unused section(s) (total 5323 bytes) removed from the image.

==============================================================================

Adding Veneers to the image

    Adding TT veneer (12 bytes, Long) for call to 'PowerMonitor' from ch57x_int.o(.text).

1 Veneer(s) (total 12 bytes) added to the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    RESET                                    0x00000000   Section      144  startup_armcm0.o(RESET)
    ../clib/microlib/division.c              0x00000000   Number         0  idiv.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uidiv.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry12b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry5.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry2.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry12a.o ABSOLUTE
    ..\SRC\Startup\startup_ARMCM0.s          0x00000000   Number         0  startup_armcm0.o ABSOLUTE
    ..\SRC\StdPeriphDriver\CH57x_adc.c       0x00000000   Number         0  ch57x_adc.o ABSOLUTE
    ..\SRC\StdPeriphDriver\CH57x_clk.c       0x00000000   Number         0  ch57x_clk.o ABSOLUTE
    ..\SRC\StdPeriphDriver\CH57x_flash.c     0x00000000   Number         0  ch57x_flash.o ABSOLUTE
    ..\SRC\StdPeriphDriver\CH57x_gpio.c      0x00000000   Number         0  ch57x_gpio.o ABSOLUTE
    ..\SRC\StdPeriphDriver\CH57x_int.c       0x00000000   Number         0  ch57x_int.o ABSOLUTE
    ..\SRC\StdPeriphDriver\CH57x_lcd.c       0x00000000   Number         0  ch57x_lcd.o ABSOLUTE
    ..\SRC\StdPeriphDriver\CH57x_pwm.c       0x00000000   Number         0  ch57x_pwm.o ABSOLUTE
    ..\SRC\StdPeriphDriver\CH57x_pwr.c       0x00000000   Number         0  ch57x_pwr.o ABSOLUTE
    ..\SRC\StdPeriphDriver\CH57x_spi0.c      0x00000000   Number         0  ch57x_spi0.o ABSOLUTE
    ..\SRC\StdPeriphDriver\CH57x_sys.c       0x00000000   Number         0  ch57x_sys.o ABSOLUTE
    ..\SRC\StdPeriphDriver\CH57x_timer0.c    0x00000000   Number         0  ch57x_timer0.o ABSOLUTE
    ..\SRC\StdPeriphDriver\CH57x_uart1.c     0x00000000   Number         0  ch57x_uart1.o ABSOLUTE
    ..\\SRC\\StdPeriphDriver\\CH57x_adc.c    0x00000000   Number         0  ch57x_adc.o ABSOLUTE
    ..\\SRC\\StdPeriphDriver\\CH57x_clk.c    0x00000000   Number         0  ch57x_clk.o ABSOLUTE
    ..\\SRC\\StdPeriphDriver\\CH57x_flash.c  0x00000000   Number         0  ch57x_flash.o ABSOLUTE
    ..\\SRC\\StdPeriphDriver\\CH57x_gpio.c   0x00000000   Number         0  ch57x_gpio.o ABSOLUTE
    ..\\SRC\\StdPeriphDriver\\CH57x_int.c    0x00000000   Number         0  ch57x_int.o ABSOLUTE
    ..\\SRC\\StdPeriphDriver\\CH57x_lcd.c    0x00000000   Number         0  ch57x_lcd.o ABSOLUTE
    ..\\SRC\\StdPeriphDriver\\CH57x_pwm.c    0x00000000   Number         0  ch57x_pwm.o ABSOLUTE
    ..\\SRC\\StdPeriphDriver\\CH57x_pwr.c    0x00000000   Number         0  ch57x_pwr.o ABSOLUTE
    ..\\SRC\\StdPeriphDriver\\CH57x_spi0.c   0x00000000   Number         0  ch57x_spi0.o ABSOLUTE
    ..\\SRC\\StdPeriphDriver\\CH57x_sys.c    0x00000000   Number         0  ch57x_sys.o ABSOLUTE
    ..\\SRC\\StdPeriphDriver\\CH57x_timer0.c 0x00000000   Number         0  ch57x_timer0.o ABSOLUTE
    ..\\SRC\\StdPeriphDriver\\CH57x_uart1.c  0x00000000   Number         0  ch57x_uart1.o ABSOLUTE
    Main.c                                   0x00000000   Number         0  main.o ABSOLUTE
    Main.c                                   0x00000000   Number         0  main.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    handlers.s                               0x00000000   Number         0  handlers.o ABSOLUTE
    init.s                                   0x00000000   Number         0  init.o ABSOLUTE
    .ARM.Collect$$$$00000000                 0x00000090   Section        0  entry.o(.ARM.Collect$$$$00000000)
    .ARM.Collect$$$$00000001                 0x00000090   Section        4  entry2.o(.ARM.Collect$$$$00000001)
    .ARM.Collect$$$$00000004                 0x00000094   Section        4  entry5.o(.ARM.Collect$$$$00000004)
    .ARM.Collect$$$$00000008                 0x00000098   Section        0  entry7b.o(.ARM.Collect$$$$00000008)
    .ARM.Collect$$$$0000000A                 0x00000098   Section        0  entry8b.o(.ARM.Collect$$$$0000000A)
    .ARM.Collect$$$$0000000B                 0x00000098   Section        8  entry9a.o(.ARM.Collect$$$$0000000B)
    .ARM.Collect$$$$0000000E                 0x000000a0   Section        4  entry12b.o(.ARM.Collect$$$$0000000E)
    .ARM.Collect$$$$0000000F                 0x000000a4   Section        0  entry10a.o(.ARM.Collect$$$$0000000F)
    .ARM.Collect$$$$00000011                 0x000000a4   Section        0  entry11a.o(.ARM.Collect$$$$00000011)
    .ARM.Collect$$$$00002712                 0x000000a4   Section        4  entry2.o(.ARM.Collect$$$$00002712)
    __lit__00000000                          0x000000a4   Data           4  entry2.o(.ARM.Collect$$$$00002712)
    .text                                    0x000000a8   Section        0  main.o(.text)
    .text                                    0x0000011c   Section        0  ch57x_clk.o(.text)
    .text                                    0x00000a90   Section        0  ch57x_gpio.o(.text)
    .text                                    0x00000d14   Section        0  ch57x_pwr.o(.text)
    .text                                    0x00000fa4   Section        0  ch57x_sys.o(.text)
    .text                                    0x000010d4   Section       28  startup_armcm0.o(.text)
    .text                                    0x000010f0   Section        0  uidiv.o(.text)
    .text                                    0x0000111c   Section        0  idiv.o(.text)
    .text                                    0x00001144   Section       36  init.o(.text)
    i.__ARM_common_switch8                   0x00001168   Section        0  ch57x_clk.o(i.__ARM_common_switch8)
    i.__scatterload_copy                     0x00001184   Section       14  handlers.o(i.__scatterload_copy)
    i.__scatterload_null                     0x00001192   Section        2  handlers.o(i.__scatterload_null)
    i.__scatterload_zeroinit                 0x00001194   Section       14  handlers.o(i.__scatterload_zeroinit)
    .text                                    0x20004000   Section        0  ch57x_int.o(.text)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv3M$S$PE$A:L22$X:L11$S22$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OSPACE$EBA8$MICROLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    __Vectors                                0x00000000   Data           4  startup_armcm0.o(RESET)
    __arm_fini_                               - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __decompress                              - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _microlib_exit                            - Undefined Weak Reference
    __Vectors_End                            0x00000090   Data           0  startup_armcm0.o(RESET)
    __Vectors_Size                           0x00000090   Number         0  startup_armcm0.o ABSOLUTE
    __main                                   0x00000091   Thumb Code     0  entry.o(.ARM.Collect$$$$00000000)
    _main_stk                                0x00000091   Thumb Code     0  entry2.o(.ARM.Collect$$$$00000001)
    _main_scatterload                        0x00000095   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    __main_after_scatterload                 0x00000099   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    _main_clock                              0x00000099   Thumb Code     0  entry7b.o(.ARM.Collect$$$$00000008)
    _main_cpp_init                           0x00000099   Thumb Code     0  entry8b.o(.ARM.Collect$$$$0000000A)
    _main_init                               0x00000099   Thumb Code     0  entry9a.o(.ARM.Collect$$$$0000000B)
    __rt_lib_shutdown_fini                   0x000000a1   Thumb Code     0  entry12b.o(.ARM.Collect$$$$0000000E)
    __rt_final_cpp                           0x000000a5   Thumb Code     0  entry10a.o(.ARM.Collect$$$$0000000F)
    __rt_final_exit                          0x000000a5   Thumb Code     0  entry11a.o(.ARM.Collect$$$$00000011)
    Delay                                    0x000000a9   Thumb Code    24  main.o(.text)
    main                                     0x000000c1   Thumb Code    84  main.o(.text)
    SystemInit                               0x0000011d   Thumb Code    50  ch57x_clk.o(.text)
    SetSysClock                              0x0000014f   Thumb Code   342  ch57x_clk.o(.text)
    GetSysClock                              0x000002a5   Thumb Code    68  ch57x_clk.o(.text)
    HClk32M_Select                           0x000002e9   Thumb Code    58  ch57x_clk.o(.text)
    LClk32K_Select                           0x00000323   Thumb Code    56  ch57x_clk.o(.text)
    HSECFG_Current                           0x0000035b   Thumb Code    36  ch57x_clk.o(.text)
    HSECFG_Capacitance                       0x0000037f   Thumb Code    34  ch57x_clk.o(.text)
    LSECFG_Current                           0x000003a1   Thumb Code    40  ch57x_clk.o(.text)
    LSECFG_Capacitance                       0x000003c9   Thumb Code    38  ch57x_clk.o(.text)
    Calibration_LSI                          0x000003ef   Thumb Code   490  ch57x_clk.o(.text)
    RTC_InitTime                             0x000005d9   Thumb Code   388  ch57x_clk.o(.text)
    RTC_GetTime                              0x0000075d   Thumb Code   468  ch57x_clk.o(.text)
    RTC_SetCycle32k                          0x00000931   Thumb Code    90  ch57x_clk.o(.text)
    RTC_GetCycle32k                          0x0000098b   Thumb Code    16  ch57x_clk.o(.text)
    RTC_TMRFunCfg                            0x0000099b   Thumb Code    50  ch57x_clk.o(.text)
    RTC_TRIGFunCfg                           0x000009cd   Thumb Code    70  ch57x_clk.o(.text)
    RTC_ModeFunDisable                       0x00000a13   Thumb Code    52  ch57x_clk.o(.text)
    RTC_GetITFlag                            0x00000a47   Thumb Code    30  ch57x_clk.o(.text)
    RTC_ClearITFlag                          0x00000a65   Thumb Code    36  ch57x_clk.o(.text)
    GPIOA_ModeCfg                            0x00000a91   Thumb Code   146  ch57x_gpio.o(.text)
    GPIOB_ModeCfg                            0x00000b23   Thumb Code   166  ch57x_gpio.o(.text)
    GPIOA_ITModeCfg                          0x00000bc9   Thumb Code   116  ch57x_gpio.o(.text)
    GPIOB_ITModeCfg                          0x00000c3d   Thumb Code   140  ch57x_gpio.o(.text)
    GPIOPinRemap                             0x00000cc9   Thumb Code    36  ch57x_gpio.o(.text)
    GPIOAGPPCfg                              0x00000ced   Thumb Code    36  ch57x_gpio.o(.text)
    PWR_DCDCCfg                              0x00000d15   Thumb Code   110  ch57x_pwr.o(.text)
    PWR_UnitModCfg                           0x00000d83   Thumb Code   154  ch57x_pwr.o(.text)
    PWR_PeriphClkCfg                         0x00000e1d   Thumb Code    62  ch57x_pwr.o(.text)
    PWR_PeriphWakeUpCfg                      0x00000e5b   Thumb Code    62  ch57x_pwr.o(.text)
    PowerMonitor                             0x00000e99   Thumb Code    90  ch57x_pwr.o(.text)
    LowPower_Idle                            0x00000ef3   Thumb Code    16  ch57x_pwr.o(.text)
    EnterCodeUpgrade                         0x00000f03   Thumb Code   148  ch57x_pwr.o(.text)
    SYS_GetInfoSta                           0x00000fa5   Thumb Code    28  ch57x_sys.o(.text)
    SYS_ResetExecute                         0x00000fc1   Thumb Code    28  ch57x_sys.o(.text)
    SYS_DisableAllIrq                        0x00000fdd   Thumb Code    18  ch57x_sys.o(.text)
    SYS_RecoverIrq                           0x00000fef   Thumb Code     6  ch57x_sys.o(.text)
    SYS_GetSysTickCnt                        0x00000ff5   Thumb Code     6  ch57x_sys.o(.text)
    WWDG_ITCfg                               0x00000ffb   Thumb Code    48  ch57x_sys.o(.text)
    WWDG_ResetCfg                            0x0000102b   Thumb Code    48  ch57x_sys.o(.text)
    WWDG_ClearFlag                           0x0000105b   Thumb Code    28  ch57x_sys.o(.text)
    mDelayuS                                 0x00001077   Thumb Code    34  ch57x_sys.o(.text)
    mDelaymS                                 0x00001099   Thumb Code    26  ch57x_sys.o(.text)
    fputc                                    0x000010b3   Thumb Code    16  ch57x_sys.o(.text)
    Reset_Handler                            0x000010d5   Thumb Code     8  startup_armcm0.o(.text)
    HardFault_Handler                        0x000010df   Thumb Code     0  startup_armcm0.o(.text)
    SVC_Handler                              0x000010df   Thumb Code     2  startup_armcm0.o(.text)
    PendSV_Handler                           0x000010e1   Thumb Code     2  startup_armcm0.o(.text)
    SysTick_Handler                          0x000010e3   Thumb Code     2  startup_armcm0.o(.text)
    ADC_IRQHandler                           0x000010e5   Thumb Code     0  startup_armcm0.o(.text)
    BB_IRQHandler                            0x000010e5   Thumb Code     0  startup_armcm0.o(.text)
    ETH_IRQHandler                           0x000010e5   Thumb Code     0  startup_armcm0.o(.text)
    GPIO_IRQHandler                          0x000010e5   Thumb Code     0  startup_armcm0.o(.text)
    LED_IRQHandler                           0x000010e5   Thumb Code     0  startup_armcm0.o(.text)
    LLE_IRQHandler                           0x000010e5   Thumb Code     0  startup_armcm0.o(.text)
    RTC_IRQHandler                           0x000010e5   Thumb Code     0  startup_armcm0.o(.text)
    SLAVE_IRQHandler                         0x000010e5   Thumb Code     0  startup_armcm0.o(.text)
    SPI0_IRQHandler                          0x000010e5   Thumb Code     0  startup_armcm0.o(.text)
    SPI1_IRQHandler                          0x000010e5   Thumb Code     0  startup_armcm0.o(.text)
    TMR0_IRQHandler                          0x000010e5   Thumb Code     0  startup_armcm0.o(.text)
    TMR1_IRQHandler                          0x000010e5   Thumb Code     0  startup_armcm0.o(.text)
    TMR2_IRQHandler                          0x000010e5   Thumb Code     0  startup_armcm0.o(.text)
    TMR3_IRQHandler                          0x000010e5   Thumb Code     0  startup_armcm0.o(.text)
    UART0_IRQHandler                         0x000010e5   Thumb Code     0  startup_armcm0.o(.text)
    UART1_IRQHandler                         0x000010e5   Thumb Code     0  startup_armcm0.o(.text)
    UART2_IRQHandler                         0x000010e5   Thumb Code     0  startup_armcm0.o(.text)
    UART3_IRQHandler                         0x000010e5   Thumb Code     0  startup_armcm0.o(.text)
    USB_IRQHandler                           0x000010e5   Thumb Code     0  startup_armcm0.o(.text)
    WDT_IRQHandler                           0x000010e5   Thumb Code     0  startup_armcm0.o(.text)
    __aeabi_uidiv                            0x000010f1   Thumb Code     0  uidiv.o(.text)
    __aeabi_uidivmod                         0x000010f1   Thumb Code    44  uidiv.o(.text)
    __aeabi_idiv                             0x0000111d   Thumb Code     0  idiv.o(.text)
    __aeabi_idivmod                          0x0000111d   Thumb Code    40  idiv.o(.text)
    __scatterload                            0x00001145   Thumb Code    28  init.o(.text)
    __scatterload_rt2                        0x00001145   Thumb Code     0  init.o(.text)
    __ARM_common_switch8                     0x00001169   Thumb Code    28  ch57x_clk.o(i.__ARM_common_switch8)
    __scatterload_copy                       0x00001185   Thumb Code    14  handlers.o(i.__scatterload_copy)
    __scatterload_null                       0x00001193   Thumb Code     2  handlers.o(i.__scatterload_null)
    __scatterload_zeroinit                   0x00001195   Thumb Code    14  handlers.o(i.__scatterload_zeroinit)
    Region$$Table$$Base                      0x000011a4   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x000011b4   Number         0  anon$$obj.o(Region$$Table)
    NMI_Handler                              0x20004001   Thumb Code   146  ch57x_int.o(.text)
    LowPower_Halt_1                          0x20004093   Thumb Code   128  ch57x_int.o(.text)
    LowPower_Halt_2                          0x20004113   Thumb Code   174  ch57x_int.o(.text)
    LowPower_Sleep                           0x200041c1   Thumb Code   190  ch57x_int.o(.text)
    LowPower_Shutdown                        0x2000427f   Thumb Code   204  ch57x_int.o(.text)
    Long Thumb to Thumb Veneer to PowerMonitor 0x2000435d   Thumb Code    12  anon$$obj.o(Veneer$$Code)
    __initial_sp                             0x20008000   Number         0  startup_armcm0.o ABSOLUTE



==============================================================================

Memory Map of the image

  Image Entry point : 0x00000091

  Load Region LR_IROM1 (Base: 0x00000000, Size: 0x0000151c, Max: 0x0003e800, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x00000000, Load base: 0x00000000, Size: 0x000011b4, Max: 0x0003e800, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x00000000   0x00000000   0x00000090   Data   RO          397    RESET               startup_armcm0.o
    0x00000090   0x00000090   0x00000000   Code   RO          402  * .ARM.Collect$$$$00000000  mc_p.l(entry.o)
    0x00000090   0x00000090   0x00000004   Code   RO          409    .ARM.Collect$$$$00000001  mc_p.l(entry2.o)
    0x00000094   0x00000094   0x00000004   Code   RO          412    .ARM.Collect$$$$00000004  mc_p.l(entry5.o)
    0x00000098   0x00000098   0x00000000   Code   RO          414    .ARM.Collect$$$$00000008  mc_p.l(entry7b.o)
    0x00000098   0x00000098   0x00000000   Code   RO          416    .ARM.Collect$$$$0000000A  mc_p.l(entry8b.o)
    0x00000098   0x00000098   0x00000008   Code   RO          417    .ARM.Collect$$$$0000000B  mc_p.l(entry9a.o)
    0x000000a0   0x000000a0   0x00000004   Code   RO          424    .ARM.Collect$$$$0000000E  mc_p.l(entry12b.o)
    0x000000a4   0x000000a4   0x00000000   Code   RO          419    .ARM.Collect$$$$0000000F  mc_p.l(entry10a.o)
    0x000000a4   0x000000a4   0x00000000   Code   RO          421    .ARM.Collect$$$$00000011  mc_p.l(entry11a.o)
    0x000000a4   0x000000a4   0x00000004   Code   RO          410    .ARM.Collect$$$$00002712  mc_p.l(entry2.o)
    0x000000a8   0x000000a8   0x00000074   Code   RO            3    .text               main.o
    0x0000011c   0x0000011c   0x00000974   Code   RO          172    .text               ch57x_clk.o
    0x00000a90   0x00000a90   0x00000284   Code   RO          217    .text               ch57x_gpio.o
    0x00000d14   0x00000d14   0x00000290   Code   RO          277    .text               ch57x_pwr.o
    0x00000fa4   0x00000fa4   0x00000130   Code   RO          317    .text               ch57x_sys.o
    0x000010d4   0x000010d4   0x0000001c   Code   RO          398    .text               startup_armcm0.o
    0x000010f0   0x000010f0   0x0000002c   Code   RO          405    .text               mc_p.l(uidiv.o)
    0x0000111c   0x0000111c   0x00000028   Code   RO          407    .text               mc_p.l(idiv.o)
    0x00001144   0x00001144   0x00000024   Code   RO          425    .text               mc_p.l(init.o)
    0x00001168   0x00001168   0x0000001c   Code   RO          187    i.__ARM_common_switch8  ch57x_clk.o
    0x00001184   0x00001184   0x0000000e   Code   RO          429    i.__scatterload_copy  mc_p.l(handlers.o)
    0x00001192   0x00001192   0x00000002   Code   RO          430    i.__scatterload_null  mc_p.l(handlers.o)
    0x00001194   0x00001194   0x0000000e   Code   RO          431    i.__scatterload_zeroinit  mc_p.l(handlers.o)
    0x000011a2   0x000011a2   0x00000002   PAD
    0x000011a4   0x000011a4   0x00000010   Data   RO          427    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM2 (Exec base: 0x20004000, Load base: 0x000011b4, Size: 0x00000368, Max: 0x00004000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20004000   0x000011b4   0x0000035c   Code   RO          377    .text               ch57x_int.o
    0x2000435c   0x00001510   0x0000000c   Ven    RO          435    Veneer$$Code        anon$$obj.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

         0          0          0          0          0      46524   ch57x_adc.o
      2448         60          0          0          0       5942   ch57x_clk.o
       644         20          0          0          0       1655   ch57x_gpio.o
       860         18          0          0          0       1679   ch57x_int.o
       656         14          0          0          0       1866   ch57x_pwr.o
       304         18          0          0          0       2209   ch57x_sys.o
       116          8          0          0          0       3257   main.o
        28         10        144          0          0        576   startup_armcm0.o

    ----------------------------------------------------------------------
      5068        <USER>        <GROUP>          0          0      63708   Object Totals
        12          4         16          0          0          0   (incl. Generated)
         0          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

         0          0          0          0          0          0   entry.o
         0          0          0          0          0          0   entry10a.o
         0          0          0          0          0          0   entry11a.o
         4          0          0          0          0          0   entry12b.o
         8          4          0          0          0          0   entry2.o
         4          0          0          0          0          0   entry5.o
         0          0          0          0          0          0   entry7b.o
         0          0          0          0          0          0   entry8b.o
         8          4          0          0          0          0   entry9a.o
        30          0          0          0          0          0   handlers.o
        40          0          0          0          0         72   idiv.o
        36          8          0          0          0         68   init.o
        44          0          0          0          0         72   uidiv.o

    ----------------------------------------------------------------------
       176         <USER>          <GROUP>          0          0        212   Library Totals
         2          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

       174         16          0          0          0        212   mc_p.l

    ----------------------------------------------------------------------
       176         <USER>          <GROUP>          0          0        212   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

      5244        168        160          0          0      63544   Grand Totals
      5244        168        160          0          0      63544   ELF Image Totals
      5244        168        160          0          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                 5404 (   5.28kB)
    Total RW  Size (RW Data + ZI Data)                 0 (   0.00kB)
    Total ROM Size (Code + RO Data + RW Data)       5404 (   5.28kB)

==============================================================================

