<template>
  <view class="enhanced-chart-container">
    <view class="chart-header">
      <view class="chart-title">
        <text class="title-text">{{ title }}</text>
        <text class="subtitle-text">{{ subtitle }}</text>
      </view>
      <view class="chart-actions">
        <view class="action-btn" @tap="refreshChart">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M23 4v6h-6"></path>
            <path d="M20.49 15a9 9 0 1 1-2.12-9.36L23 10"></path>
          </svg>
        </view>
        <view class="action-btn" @tap="exportChart">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
            <polyline points="7,10 12,15 17,10"></polyline>
            <line x1="12" y1="15" x2="12" y2="3"></line>
          </svg>
        </view>
      </view>
    </view>
    
    <view class="chart-content">
      <canvas 
        class="chart-canvas" 
        :canvas-id="canvasId"
        :style="{ width: chartWidth + 'px', height: chartHeight + 'px' }"
        @touchstart="onTouchStart"
        @touchmove="onTouchMove"
        @touchend="onTouchEnd"
      ></canvas>
      
      <!-- 数据点悬浮提示 -->
      <view class="chart-tooltip" v-if="showTooltip" :style="tooltipStyle">
        <view class="tooltip-content">
          <text class="tooltip-title">{{ tooltipData.title }}</text>
          <text class="tooltip-value">{{ tooltipData.value }}</text>
          <text class="tooltip-time">{{ tooltipData.time }}</text>
        </view>
      </view>
      
      <!-- 加载状态 -->
      <view class="chart-loading" v-if="isLoading">
        <view class="loading-spinner"></view>
        <text class="loading-text">加载中...</text>
      </view>
    </view>
    
    <!-- 图表统计信息 -->
    <view class="chart-stats">
      <view class="stat-item">
        <view class="stat-icon stat-max">↗</view>
        <view class="stat-content">
          <text class="stat-label">最大值</text>
          <text class="stat-value">{{ maxValue }}</text>
        </view>
      </view>
      <view class="stat-item">
        <view class="stat-icon stat-min">↘</view>
        <view class="stat-content">
          <text class="stat-label">最小值</text>
          <text class="stat-value">{{ minValue }}</text>
        </view>
      </view>
      <view class="stat-item">
        <view class="stat-icon stat-avg">→</view>
        <view class="stat-content">
          <text class="stat-label">平均值</text>
          <text class="stat-value">{{ avgValue }}</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted, watch, nextTick } from 'vue'

const props = defineProps({
  title: {
    type: String,
    default: '数据图表'
  },
  subtitle: {
    type: String,
    default: '实时监测'
  },
  data: {
    type: Array,
    default: () => []
  },
  chartType: {
    type: String,
    default: 'line' // line, bar, area
  },
  chartWidth: {
    type: Number,
    default: 350
  },
  chartHeight: {
    type: Number,
    default: 260
  },
  primaryColor: {
    type: String,
    default: '#667eea'
  },
  secondaryColor: {
    type: String,
    default: '#764ba2'
  }
})

const emit = defineEmits(['refresh', 'export'])

// 响应式数据
const canvasId = ref(`chart-${Date.now()}`)
const isLoading = ref(false)
const showTooltip = ref(false)
const tooltipData = ref({})
const tooltipStyle = ref({})

let chartContext = null
let animationId = null

// 计算属性
const maxValue = computed(() => {
  if (props.data.length === 0) return '0'
  const max = Math.max(...props.data.map(item => item.value || 0))
  return max.toFixed(3)
})

const minValue = computed(() => {
  if (props.data.length === 0) return '0'
  const min = Math.min(...props.data.map(item => item.value || 0))
  return min.toFixed(3)
})

const avgValue = computed(() => {
  if (props.data.length === 0) return '0'
  const sum = props.data.reduce((acc, item) => acc + (item.value || 0), 0)
  return (sum / props.data.length).toFixed(3)
})

// 绘制图表
const drawChart = () => {
  if (!chartContext || props.data.length === 0) return
  
  const canvas = chartContext
  const { chartWidth, chartHeight } = props
  
  // 清空画布
  canvas.clearRect(0, 0, chartWidth, chartHeight)
  
  // 设置样式
  const padding = 40
  const gridColor = '#f1f5f9'
  const textColor = '#64748b'
  const primaryColor = props.primaryColor
  const secondaryColor = props.secondaryColor
  
  // 绘制网格
  drawGrid(canvas, padding, gridColor, textColor)
  
  // 绘制数据
  if (props.chartType === 'line') {
    drawLineChart(canvas, padding, primaryColor, secondaryColor)
  } else if (props.chartType === 'bar') {
    drawBarChart(canvas, padding, primaryColor, secondaryColor)
  } else if (props.chartType === 'area') {
    drawAreaChart(canvas, padding, primaryColor, secondaryColor)
  }
  
  // 绘制数据点
  drawDataPoints(canvas, padding, primaryColor)
}

// 绘制网格
const drawGrid = (canvas, padding, gridColor, textColor) => {
  const { chartWidth, chartHeight } = props
  const gridWidth = chartWidth - padding * 2
  const gridHeight = chartHeight - padding * 2
  
  canvas.setStrokeStyle(gridColor)
  canvas.setLineWidth(1)
  
  // 绘制水平网格线
  for (let i = 0; i <= 5; i++) {
    const y = padding + (gridHeight / 5) * i
    canvas.beginPath()
    canvas.moveTo(padding, y)
    canvas.lineTo(chartWidth - padding, y)
    canvas.stroke()
  }
  
  // 绘制垂直网格线
  for (let i = 0; i <= 6; i++) {
    const x = padding + (gridWidth / 6) * i
    canvas.beginPath()
    canvas.moveTo(x, padding)
    canvas.lineTo(x, chartHeight - padding)
    canvas.stroke()
  }
}

// 绘制折线图
const drawLineChart = (canvas, padding, primaryColor, secondaryColor) => {
  if (props.data.length < 2) return
  
  const { chartWidth, chartHeight } = props
  const gridWidth = chartWidth - padding * 2
  const gridHeight = chartHeight - padding * 2
  
  const maxVal = Math.max(...props.data.map(item => item.value || 0))
  const minVal = Math.min(...props.data.map(item => item.value || 0))
  const range = maxVal - minVal || 1
  
  // 创建渐变
  const gradient = canvas.createLinearGradient(0, padding, 0, chartHeight - padding)
  gradient.addColorStop(0, primaryColor)
  gradient.addColorStop(1, secondaryColor)
  
  canvas.setStrokeStyle(gradient)
  canvas.setLineWidth(3)
  canvas.setLineCap('round')
  canvas.setLineJoin('round')
  
  canvas.beginPath()
  
  props.data.forEach((item, index) => {
    const x = padding + (gridWidth / (props.data.length - 1)) * index
    const y = padding + gridHeight - ((item.value - minVal) / range) * gridHeight
    
    if (index === 0) {
      canvas.moveTo(x, y)
    } else {
      canvas.lineTo(x, y)
    }
  })
  
  canvas.stroke()
}

// 绘制柱状图
const drawBarChart = (canvas, padding, primaryColor, secondaryColor) => {
  const { chartWidth, chartHeight } = props
  const gridWidth = chartWidth - padding * 2
  const gridHeight = chartHeight - padding * 2
  
  const maxVal = Math.max(...props.data.map(item => item.value || 0))
  const barWidth = gridWidth / props.data.length * 0.8
  
  props.data.forEach((item, index) => {
    const x = padding + (gridWidth / props.data.length) * index + (gridWidth / props.data.length - barWidth) / 2
    const barHeight = (item.value / maxVal) * gridHeight
    const y = chartHeight - padding - barHeight
    
    // 创建渐变
    const gradient = canvas.createLinearGradient(0, y, 0, y + barHeight)
    gradient.addColorStop(0, primaryColor)
    gradient.addColorStop(1, secondaryColor)
    
    canvas.setFillStyle(gradient)
    canvas.fillRect(x, y, barWidth, barHeight)
  })
}

// 绘制面积图
const drawAreaChart = (canvas, padding, primaryColor, secondaryColor) => {
  if (props.data.length < 2) return
  
  const { chartWidth, chartHeight } = props
  const gridWidth = chartWidth - padding * 2
  const gridHeight = chartHeight - padding * 2
  
  const maxVal = Math.max(...props.data.map(item => item.value || 0))
  const minVal = Math.min(...props.data.map(item => item.value || 0))
  const range = maxVal - minVal || 1
  
  // 创建渐变
  const gradient = canvas.createLinearGradient(0, padding, 0, chartHeight - padding)
  gradient.addColorStop(0, primaryColor + '80')
  gradient.addColorStop(1, secondaryColor + '20')
  
  canvas.setFillStyle(gradient)
  
  canvas.beginPath()
  canvas.moveTo(padding, chartHeight - padding)
  
  props.data.forEach((item, index) => {
    const x = padding + (gridWidth / (props.data.length - 1)) * index
    const y = padding + gridHeight - ((item.value - minVal) / range) * gridHeight
    canvas.lineTo(x, y)
  })
  
  canvas.lineTo(chartWidth - padding, chartHeight - padding)
  canvas.closePath()
  canvas.fill()
  
  // 绘制顶部线条
  drawLineChart(canvas, padding, primaryColor, secondaryColor)
}

// 绘制数据点
const drawDataPoints = (canvas, padding, primaryColor) => {
  const { chartWidth, chartHeight } = props
  const gridWidth = chartWidth - padding * 2
  const gridHeight = chartHeight - padding * 2
  
  const maxVal = Math.max(...props.data.map(item => item.value || 0))
  const minVal = Math.min(...props.data.map(item => item.value || 0))
  const range = maxVal - minVal || 1
  
  canvas.setFillStyle('#ffffff')
  canvas.setStrokeStyle(primaryColor)
  canvas.setLineWidth(2)
  
  props.data.forEach((item, index) => {
    const x = padding + (gridWidth / (props.data.length - 1)) * index
    const y = padding + gridHeight - ((item.value - minVal) / range) * gridHeight
    
    canvas.beginPath()
    canvas.arc(x, y, 4, 0, 2 * Math.PI)
    canvas.fill()
    canvas.stroke()
  })
}

// 触摸事件处理
const onTouchStart = (e) => {
  // 处理触摸开始
}

const onTouchMove = (e) => {
  // 处理触摸移动，显示数据点信息
}

const onTouchEnd = (e) => {
  showTooltip.value = false
}

// 刷新图表
const refreshChart = () => {
  isLoading.value = true
  emit('refresh')
  
  setTimeout(() => {
    isLoading.value = false
    drawChart()
  }, 500)
}

// 导出图表
const exportChart = () => {
  emit('export')
  uni.showToast({
    title: '导出成功',
    icon: 'success'
  })
}

// 监听数据变化
watch(() => props.data, () => {
  nextTick(() => {
    drawChart()
  })
}, { deep: true })

// 组件挂载
onMounted(() => {
  nextTick(() => {
    chartContext = uni.createCanvasContext(canvasId.value)
    drawChart()
  })
})
</script>

<style scoped>
.enhanced-chart-container {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-radius: 24px;
  margin: 16px;
  overflow: hidden;
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.1),
    0 4px 16px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.8);
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.chart-title {
  flex: 1;
}

.title-text {
  font-size: 18px;
  font-weight: 700;
  color: #1a1a1a;
  margin-bottom: 4px;
}

.subtitle-text {
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

.chart-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  width: 36px;
  height: 36px;
  border-radius: 18px;
  background: rgba(102, 126, 234, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.action-btn:active {
  transform: scale(0.95);
  background: rgba(102, 126, 234, 0.2);
}

.action-btn svg {
  width: 18px;
  height: 18px;
  color: #667eea;
}

.chart-content {
  position: relative;
  padding: 20px;
}

.chart-canvas {
  display: block;
  margin: 0 auto;
}

.chart-tooltip {
  position: absolute;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 8px 12px;
  border-radius: 8px;
  font-size: 12px;
  pointer-events: none;
  z-index: 10;
}

.tooltip-content {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.tooltip-title {
  font-weight: 600;
}

.tooltip-value {
  font-size: 14px;
  font-weight: 700;
}

.tooltip-time {
  opacity: 0.8;
}

.chart-loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid rgba(102, 126, 234, 0.2);
  border-top: 3px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 14px;
  color: #666;
}

.chart-stats {
  display: flex;
  padding: 16px 20px 20px;
  gap: 16px;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
}

.stat-item {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 12px;
}

.stat-icon {
  width: 32px;
  height: 32px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  font-weight: 700;
  color: white;
}

.stat-max {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.stat-min {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
}

.stat-avg {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

.stat-content {
  display: flex;
  flex-direction: column;
}

.stat-label {
  font-size: 12px;
  color: #666;
  margin-bottom: 2px;
}

.stat-value {
  font-size: 16px;
  font-weight: 700;
  color: #1a1a1a;
}

/* 响应式设计 */
@media (max-width: 375px) {
  .enhanced-chart-container {
    margin: 12px;
  }
  
  .chart-header {
    padding: 16px;
  }
  
  .title-text {
    font-size: 16px;
  }
  
  .chart-content {
    padding: 16px;
  }
  
  .chart-stats {
    flex-direction: column;
    gap: 12px;
  }
}
</style> 