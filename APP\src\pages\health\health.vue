<template>
  <view class="health-container">
    <!-- 健康状态总览 -->
    <view class="health-overview">
      <view class="overview-header">
        <text class="overview-title">健康监测</text>
        <view class="health-score">
          <text class="score-value">{{ healthScore }}</text>
          <text class="score-label">健康指数</text>
        </view>
      </view>
      
      <view class="status-indicator" :class="healthStatusClass">
        <text class="status-icon">{{ healthStatusIcon }}</text>
        <text class="status-text">{{ healthStatusText }}</text>
      </view>
    </view>

    <!-- 核心健康指标卡片 -->
    <view class="health-cards">
      <!-- 心率卡片 -->
      <view class="health-card heart-rate">
        <view class="card-header">
          <text class="card-icon">❤️</text>
          <text class="card-title">心率</text>
          <view class="card-status" :class="heartRateStatus.class">
            <text class="status-dot"></text>
          </view>
        </view>
        
        <view class="card-main">
          <view class="metric-display">
            <text class="metric-value">{{ healthState.current.heartRate }}</text>
            <text class="metric-unit">BPM</text>
          </view>
          
          <view class="metric-chart">
            <canvas canvas-id="heartRateChart" class="mini-chart"></canvas>
          </view>
        </view>
        
        <view class="card-footer">
          <text class="metric-label">{{ heartRateStatus.text }}</text>
          <text class="metric-range">正常: 60-100</text>
        </view>
      </view>

      <!-- 血氧卡片 -->
      <view class="health-card spo2">
        <view class="card-header">
          <text class="card-icon">🫁</text>
          <text class="card-title">血氧</text>
          <view class="card-status" :class="spO2Status.class">
            <text class="status-dot"></text>
          </view>
        </view>
        
        <view class="card-main">
          <view class="metric-display">
            <text class="metric-value">{{ healthState.current.spO2 }}</text>
            <text class="metric-unit">%</text>
          </view>
          
          <view class="metric-chart">
            <canvas canvas-id="spO2Chart" class="mini-chart"></canvas>
          </view>
        </view>
        
        <view class="card-footer">
          <text class="metric-label">{{ spO2Status.text }}</text>
          <text class="metric-range">正常: >95%</text>
        </view>
      </view>

      <!-- 体温卡片 -->
      <view class="health-card body-temp">
        <view class="card-header">
          <text class="card-icon">🌡️</text>
          <text class="card-title">体温</text>
          <view class="card-status" :class="bodyTempStatus.class">
            <text class="status-dot"></text>
          </view>
        </view>
        
        <view class="card-main">
          <view class="metric-display">
            <text class="metric-value">{{ healthState.current.bodyTemp.toFixed(1) }}</text>
            <text class="metric-unit">°C</text>
          </view>
          
          <view class="metric-chart">
            <canvas canvas-id="bodyTempChart" class="mini-chart"></canvas>
          </view>
        </view>
        
        <view class="card-footer">
          <text class="metric-label">{{ bodyTempStatus.text }}</text>
          <text class="metric-range">正常: 36.0-37.3°C</text>
        </view>
      </view>

      <!-- 步数卡片 -->
      <view class="health-card steps">
        <view class="card-header">
          <text class="card-icon">👟</text>
          <text class="card-title">步数</text>
          <view class="steps-progress">
            <text class="progress-text">{{ (healthState.current.steps / 10000 * 100).toFixed(0) }}%</text>
          </view>
        </view>
        
        <view class="card-main">
          <view class="metric-display">
            <text class="metric-value">{{ healthState.current.steps.toLocaleString() }}</text>
            <text class="metric-unit">步</text>
          </view>
          
          <view class="steps-ring">
            <canvas canvas-id="stepsRing" class="ring-chart"></canvas>
            <view class="ring-center">
              <text class="ring-goal">10,000</text>
              <text class="ring-label">目标</text>
            </view>
          </view>
        </view>
        
        <view class="card-footer">
          <text class="metric-label">今日活动</text>
          <text class="metric-range">目标: 10,000步</text>
        </view>
      </view>
    </view>

    <!-- 详细分析选项卡 -->
    <view class="analysis-tabs">
      <view class="tab-item" 
        :class="{ active: activeAnalysisTab === 'trends' }" 
        @tap="switchAnalysisTab('trends')">
        <text class="tab-text">趋势分析</text>
      </view>
      <view class="tab-item" 
        :class="{ active: activeAnalysisTab === 'daily' }" 
        @tap="switchAnalysisTab('daily')">
        <text class="tab-text">每日报告</text>
      </view>
      <view class="tab-item" 
        :class="{ active: activeAnalysisTab === 'advice' }" 
        @tap="switchAnalysisTab('advice')">
        <text class="tab-text">健康建议</text>
      </view>
    </view>

    <!-- 趋势分析 -->
    <view class="analysis-section" v-if="activeAnalysisTab === 'trends'">
      <view class="trend-chart-card">
        <view class="chart-header">
          <text class="chart-title">24小时健康趋势</text>
          <view class="chart-controls">
            <text class="control-btn" 
              :class="{ active: trendPeriod === '24h' }"
              @tap="setTrendPeriod('24h')">24小时</text>
            <text class="control-btn"
              :class="{ active: trendPeriod === '7d' }"
              @tap="setTrendPeriod('7d')">7天</text>
            <text class="control-btn"
              :class="{ active: trendPeriod === '30d' }"
              @tap="setTrendPeriod('30d')">30天</text>
          </view>
        </view>
        
        <view class="chart-container">
          <canvas canvas-id="healthTrendChart" class="trend-chart"></canvas>
        </view>
        
        <view class="chart-legend">
          <view class="legend-item">
            <view class="legend-color heart"></view>
            <text class="legend-text">心率</text>
          </view>
          <view class="legend-item">
            <view class="legend-color spo2"></view>
            <text class="legend-text">血氧</text>
          </view>
          <view class="legend-item">
            <view class="legend-color temp"></view>
            <text class="legend-text">体温</text>
          </view>
        </view>
      </view>

      <!-- 健康统计 -->
      <view class="health-stats">
        <view class="stats-header">
          <text class="stats-title">健康统计</text>
          <text class="stats-period">{{ trendPeriodText }}</text>
        </view>
        
        <view class="stats-grid">
          <view class="stat-item">
            <text class="stat-label">平均心率</text>
            <text class="stat-value">{{ healthState.dailyStats.avgHeartRate }} BPM</text>
            <view class="stat-trend up">
              <text class="trend-icon">📈</text>
              <text class="trend-text">+2 BPM</text>
            </view>
          </view>
          
          <view class="stat-item">
            <text class="stat-label">平均血氧</text>
            <text class="stat-value">{{ healthState.dailyStats.avgSpO2 }}%</text>
            <view class="stat-trend stable">
              <text class="trend-icon">➖</text>
              <text class="trend-text">稳定</text>
            </view>
          </view>
          
          <view class="stat-item">
            <text class="stat-label">最高心率</text>
            <text class="stat-value">{{ healthState.dailyStats.maxHeartRate }} BPM</text>
            <view class="stat-trend down">
              <text class="trend-icon">📉</text>
              <text class="trend-text">-5 BPM</text>
            </view>
          </view>
          
          <view class="stat-item">
            <text class="stat-label">最低心率</text>
            <text class="stat-value">{{ healthState.dailyStats.minHeartRate }} BPM</text>
            <view class="stat-trend up">
              <text class="trend-icon">📈</text>
              <text class="trend-text">+3 BPM</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 每日报告 -->
    <view class="analysis-section" v-if="activeAnalysisTab === 'daily'">
      <view class="daily-report">
        <view class="report-header">
          <text class="report-title">今日健康报告</text>
          <text class="report-date">{{ formatDate(Date.now()) }}</text>
        </view>
        
        <view class="report-summary">
          <view class="summary-card" :class="dailyRating.class">
            <text class="summary-icon">{{ dailyRating.icon }}</text>
            <text class="summary-title">{{ dailyRating.title }}</text>
            <text class="summary-desc">{{ dailyRating.description }}</text>
          </view>
        </view>
        
        <view class="report-details">
          <view class="detail-section">
            <text class="section-title">心血管健康</text>
            <view class="detail-content">
              <view class="detail-item">
                <text class="detail-label">静息心率</text>
                <text class="detail-value">{{ healthState.dailyStats.minHeartRate }} BPM</text>
                <view class="detail-status good">
                  <text class="status-text">良好</text>
                </view>
              </view>
              <view class="detail-item">
                <text class="detail-label">心率变异性</text>
                <text class="detail-value">42 ms</text>
                <view class="detail-status good">
                  <text class="status-text">正常</text>
                </view>
              </view>
            </view>
          </view>
          
          <view class="detail-section">
            <text class="section-title">呼吸系统</text>
            <view class="detail-content">
              <view class="detail-item">
                <text class="detail-label">血氧饱和度</text>
                <text class="detail-value">{{ healthState.dailyStats.avgSpO2 }}%</text>
                <view class="detail-status excellent">
                  <text class="status-text">优秀</text>
                </view>
              </view>
              <view class="detail-item">
                <text class="detail-label">呼吸频率</text>
                <text class="detail-value">16 次/分</text>
                <view class="detail-status good">
                  <text class="status-text">正常</text>
                </view>
              </view>
            </view>
          </view>
          
          <view class="detail-section">
            <text class="section-title">体温调节</text>
            <view class="detail-content">
              <view class="detail-item">
                <text class="detail-label">平均体温</text>
                <text class="detail-value">{{ healthState.current.bodyTemp.toFixed(1) }}°C</text>
                <view class="detail-status good">
                  <text class="status-text">正常</text>
                </view>
              </view>
              <view class="detail-item">
                <text class="detail-label">体温变化</text>
                <text class="detail-value">0.3°C</text>
                <view class="detail-status good">
                  <text class="status-text">稳定</text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 健康建议 -->
    <view class="analysis-section" v-if="activeAnalysisTab === 'advice'">
      <view class="health-advice">
        <view class="advice-header">
          <text class="advice-title">个性化健康建议</text>
          <text class="advice-subtitle">基于您的健康数据和辐射暴露情况</text>
        </view>
        
        <view class="advice-categories">
          <view class="advice-category">
            <view class="category-header">
              <text class="category-icon">🏃‍♂️</text>
              <text class="category-title">运动建议</text>
            </view>
            <view class="advice-list">
              <view class="advice-item">
                <text class="advice-text">建议每日进行30分钟中等强度有氧运动</text>
                <view class="advice-priority high">重要</view>
              </view>
              <view class="advice-item">
                <text class="advice-text">当前步数已达成78%，继续加油！</text>
                <view class="advice-priority medium">一般</view>
              </view>
              <view class="advice-item">
                <text class="advice-text">心率较低，可适当增加运动强度</text>
                <view class="advice-priority medium">一般</view>
              </view>
            </view>
          </view>
          
          <view class="advice-category">
            <view class="category-header">
              <text class="category-icon">🛡️</text>
              <text class="category-title">辐射防护</text>
            </view>
            <view class="advice-list">
              <view class="advice-item">
                <text class="advice-text">当前辐射水平安全，继续保持监测</text>
                <view class="advice-priority low">提醒</view>
              </view>
              <view class="advice-item">
                <text class="advice-text">建议补充抗氧化维生素，增强抵抗力</text>
                <view class="advice-priority medium">一般</view>
              </view>
              <view class="advice-item">
                <text class="advice-text">保持充足睡眠，有助于细胞修复</text>
                <view class="advice-priority high">重要</view>
              </view>
            </view>
          </view>
          
          <view class="advice-category">
            <view class="category-header">
              <text class="category-icon">🥗</text>
              <text class="category-title">营养建议</text>
            </view>
            <view class="advice-list">
              <view class="advice-item">
                <text class="advice-text">多摄入富含维生素C和E的食物</text>
                <view class="advice-priority high">重要</view>
              </view>
              <view class="advice-item">
                <text class="advice-text">增加叶酸和β-胡萝卜素的摄入</text>
                <view class="advice-priority medium">一般</view>
              </view>
              <view class="advice-item">
                <text class="advice-text">保持充足的水分摄入，每日2L以上</text>
                <view class="advice-priority medium">一般</view>
              </view>
            </view>
          </view>
          
          <view class="advice-category">
            <view class="category-header">
              <text class="category-icon">😴</text>
              <text class="category-title">睡眠优化</text>
            </view>
            <view class="advice-list">
              <view class="advice-item">
                <text class="advice-text">建议23:00前入睡，保证7-8小时睡眠</text>
                <view class="advice-priority high">重要</view>
              </view>
              <view class="advice-item">
                <text class="advice-text">睡前1小时避免使用电子设备</text>
                <view class="advice-priority medium">一般</view>
              </view>
              <view class="advice-item">
                <text class="advice-text">保持卧室温度在18-22°C</text>
                <view class="advice-priority low">提醒</view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 健康数据同步按钮 -->
    <view class="sync-button" @tap="syncHealthData">
      <text class="sync-icon">🔄</text>
      <text class="sync-text">同步健康数据</text>
    </view>

    <!-- 健康提醒设置浮动按钮 -->
    <view class="fab-health" @tap="showHealthSettings">
      <text class="fab-icon">⚙️</text>
    </view>
  </view>
</template>

<script>
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { healthState, radiationState } from '../../utils/dataStore.js'
import dataStore from '../../utils/dataStore.js'

export default {
  name: 'HealthMonitor',
  setup() {
    const activeAnalysisTab = ref('trends')
    const trendPeriod = ref('24h')
    
    // 图表上下文
    const chartContexts = reactive({
      heartRate: null,
      spO2: null,
      bodyTemp: null,
      steps: null,
      trend: null
    })

    // 计算属性
    const healthScore = computed(() => {
      const { heartRate, spO2, bodyTemp } = healthState.current
      
      // 简单的健康评分算法
      let score = 100
      
      // 心率评分
      if (heartRate < 60 || heartRate > 100) score -= 10
      else if (heartRate >= 70 && heartRate <= 85) score += 5
      
      // 血氧评分
      if (spO2 < 95) score -= 15
      else if (spO2 >= 98) score += 5
      
      // 体温评分
      if (bodyTemp < 36.0 || bodyTemp > 37.3) score -= 8
      else if (bodyTemp >= 36.3 && bodyTemp <= 36.8) score += 3
      
      // 辐射影响评分
      const doseRate = radiationState.currentData.doseRate
      if (doseRate > radiationState.settings.maxDoseRate) score -= 20
      
      return Math.max(Math.min(score, 100), 0)
    })

    const healthStatusClass = computed(() => {
      const score = healthScore.value
      if (score >= 85) return 'excellent'
      if (score >= 70) return 'good'
      if (score >= 55) return 'fair'
      return 'poor'
    })

    const healthStatusIcon = computed(() => {
      const score = healthScore.value
      if (score >= 85) return '🌟'
      if (score >= 70) return '😊'
      if (score >= 55) return '😐'
      return '😟'
    })

    const healthStatusText = computed(() => {
      const score = healthScore.value
      if (score >= 85) return '健康状态优秀'
      if (score >= 70) return '健康状态良好'
      if (score >= 55) return '健康状态一般'
      return '需要关注健康'
    })

    const heartRateStatus = computed(() => {
      const hr = healthState.current.heartRate
      if (hr < 60) return { class: 'low', text: '心率偏低' }
      if (hr > 100) return { class: 'high', text: '心率偏高' }
      if (hr >= 70 && hr <= 85) return { class: 'optimal', text: '心率最佳' }
      return { class: 'normal', text: '心率正常' }
    })

    const spO2Status = computed(() => {
      const spo2 = healthState.current.spO2
      if (spo2 < 90) return { class: 'critical', text: '血氧危险' }
      if (spo2 < 95) return { class: 'low', text: '血氧偏低' }
      if (spo2 >= 98) return { class: 'optimal', text: '血氧优秀' }
      return { class: 'normal', text: '血氧正常' }
    })

    const bodyTempStatus = computed(() => {
      const temp = healthState.current.bodyTemp
      if (temp < 35.0) return { class: 'critical', text: '体温过低' }
      if (temp > 38.0) return { class: 'high', text: '发热' }
      if (temp > 37.3) return { class: 'elevated', text: '体温偏高' }
      if (temp < 36.0) return { class: 'low', text: '体温偏低' }
      return { class: 'normal', text: '体温正常' }
    })

    const trendPeriodText = computed(() => {
      const periodMap = {
        '24h': '过去24小时',
        '7d': '过去7天',
        '30d': '过去30天'
      }
      return periodMap[trendPeriod.value] || '过去24小时'
    })

    const dailyRating = computed(() => {
      const score = healthScore.value
      if (score >= 85) {
        return {
          class: 'excellent',
          icon: '🌟',
          title: '今日状态优秀',
          description: '各项健康指标都很理想，继续保持良好的生活习惯！'
        }
      }
      if (score >= 70) {
        return {
          class: 'good',
          icon: '😊',
          title: '今日状态良好',
          description: '整体健康状况不错，注意保持规律作息和适量运动。'
        }
      }
      if (score >= 55) {
        return {
          class: 'fair',
          icon: '😐',
          title: '今日状态一般',
          description: '部分指标需要改善，建议关注运动和饮食调节。'
        }
      }
      return {
        class: 'poor',
        icon: '😟',
        title: '需要关注健康',
        description: '多项指标异常，建议及时调整生活方式或咨询医生。'
      }
    })

    // 方法
    const switchAnalysisTab = (tab) => {
      activeAnalysisTab.value = tab
      setTimeout(() => {
        if (tab === 'trends') {
          drawHealthTrendChart()
        }
      }, 100)
    }

    const setTrendPeriod = (period) => {
      trendPeriod.value = period
      setTimeout(() => {
        drawHealthTrendChart()
      }, 100)
    }

    const formatDate = (timestamp) => {
      return new Date(timestamp).toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        weekday: 'long'
      })
    }

    const syncHealthData = () => {
      uni.showLoading({
        title: '同步中...'
      })
      
      // 模拟同步延迟
      setTimeout(() => {
        uni.hideLoading()
        uni.showToast({
          title: '同步成功',
          icon: 'success'
        })
        
        // 更新健康数据
        updateHealthCharts()
      }, 2000)
    }

    const showHealthSettings = () => {
      uni.showActionSheet({
        itemList: ['健康提醒设置', '数据导出', '隐私设置', '关于健康监测'],
        success: (res) => {
          switch (res.tapIndex) {
            case 0:
              uni.showToast({ title: '健康提醒设置', icon: 'none' })
              break
            case 1:
              exportHealthData()
              break
            case 2:
              uni.showToast({ title: '隐私设置', icon: 'none' })
              break
            case 3:
              uni.showToast({ title: '关于健康监测', icon: 'none' })
              break
          }
        }
      })
    }

    const exportHealthData = () => {
      const data = dataStore.exportData('health')
      uni.showModal({
        title: '导出健康数据',
        content: '健康数据已准备完成，您可以将其保存或分享',
        confirmText: '确定',
        success: (res) => {
          if (res.confirm) {
            uni.showToast({
              title: '导出成功',
              icon: 'success'
            })
          }
        }
      })
    }

    // 图表绘制方法
    const initHealthCharts = () => {
      initHeartRateChart()
      initSpO2Chart()
      initBodyTempChart()
      initStepsRing()
      drawHealthTrendChart()
    }

    const initHeartRateChart = () => {
      const ctx = uni.createCanvasContext('heartRateChart')
      if (!ctx) return
      
      chartContexts.heartRate = ctx
      drawMiniChart(ctx, healthState.history.map(item => item.heartRate), '#ff6b6b')
    }

    const initSpO2Chart = () => {
      const ctx = uni.createCanvasContext('spO2Chart')
      if (!ctx) return
      
      chartContexts.spO2 = ctx
      drawMiniChart(ctx, healthState.history.map(item => item.spO2), '#4dabf7')
    }

    const initBodyTempChart = () => {
      const ctx = uni.createCanvasContext('bodyTempChart')
      if (!ctx) return
      
      chartContexts.bodyTemp = ctx
      drawMiniChart(ctx, healthState.history.map(item => item.bodyTemp), '#ffd43b')
    }

    const drawMiniChart = (ctx, data, color) => {
      if (!ctx || !data || data.length < 2) return
      
      const width = 100
      const height = 60
      const recentData = data.slice(-10)
      
      ctx.clearRect(0, 0, width, height)
      
      const max = Math.max(...recentData)
      const min = Math.min(...recentData)
      const range = max - min || 1
      
      ctx.beginPath()
      ctx.setStrokeStyle(color)
      ctx.setLineWidth(2)
      
      recentData.forEach((value, index) => {
        const x = (index / (recentData.length - 1)) * width
        const y = height - ((value - min) / range) * height
        
        if (index === 0) {
          ctx.moveTo(x, y)
        } else {
          ctx.lineTo(x, y)
        }
      })
      
      ctx.stroke()
      ctx.draw()
    }

    const initStepsRing = () => {
      const ctx = uni.createCanvasContext('stepsRing')
      if (!ctx) return
      
      chartContexts.steps = ctx
      drawStepsRing()
    }

    const drawStepsRing = () => {
      if (!chartContexts.steps) return
      
      const ctx = chartContexts.steps
      const width = 120
      const height = 120
      const centerX = width / 2
      const centerY = height / 2
      const radius = 45
      const lineWidth = 8
      
      ctx.clearRect(0, 0, width, height)
      
      // 背景圆环
      ctx.beginPath()
      ctx.setStrokeStyle('rgba(255, 255, 255, 0.1)')
      ctx.setLineWidth(lineWidth)
      ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI)
      ctx.stroke()
      
      // 进度圆环
      const progress = Math.min(healthState.current.steps / 10000, 1)
      const endAngle = -Math.PI / 2 + 2 * Math.PI * progress
      
      ctx.beginPath()
      ctx.setStrokeStyle('#3cc51f')
      ctx.setLineWidth(lineWidth)
      ctx.setLineCap('round')
      ctx.arc(centerX, centerY, radius, -Math.PI / 2, endAngle)
      ctx.stroke()
      
      ctx.draw()
    }

    const drawHealthTrendChart = () => {
      const ctx = uni.createCanvasContext('healthTrendChart')
      if (!ctx) return
      
      const width = 300
      const height = 200
      const padding = 30
      
      ctx.clearRect(0, 0, width, height)
      
      // 获取数据
      let timeFilter = 24 * 60 * 60 * 1000 // 24小时
      switch (trendPeriod.value) {
        case '7d': timeFilter = 7 * 24 * 60 * 60 * 1000; break
        case '30d': timeFilter = 30 * 24 * 60 * 60 * 1000; break
      }
      
      const now = Date.now()
      const filteredData = healthState.history.filter(item => 
        (now - item.timestamp) <= timeFilter
      ).slice(-20)
      
      if (filteredData.length < 2) return
      
      // 绘制网格
      ctx.setStrokeStyle('rgba(255, 255, 255, 0.1)')
      ctx.setLineWidth(1)
      for (let i = 1; i < 5; i++) {
        const y = (height - padding * 2) / 4 * i + padding
        ctx.beginPath()
        ctx.moveTo(padding, y)
        ctx.lineTo(width - padding, y)
        ctx.stroke()
      }
      
      // 绘制心率曲线
      const heartRates = filteredData.map(item => item.heartRate)
      const maxHR = Math.max(...heartRates)
      const minHR = Math.min(...heartRates)
      const hrRange = maxHR - minHR || 1
      
      ctx.beginPath()
      ctx.setStrokeStyle('#ff6b6b')
      ctx.setLineWidth(2)
      
      filteredData.forEach((item, index) => {
        const x = (index / (filteredData.length - 1)) * (width - padding * 2) + padding
        const y = height - padding - ((item.heartRate - minHR) / hrRange) * (height - padding * 2)
        
        if (index === 0) {
          ctx.moveTo(x, y)
        } else {
          ctx.lineTo(x, y)
        }
      })
      ctx.stroke()
      
      ctx.draw()
    }

    const updateHealthCharts = () => {
      if (chartContexts.heartRate) drawMiniChart(chartContexts.heartRate, healthState.history.map(item => item.heartRate), '#ff6b6b')
      if (chartContexts.spO2) drawMiniChart(chartContexts.spO2, healthState.history.map(item => item.spO2), '#4dabf7')
      if (chartContexts.bodyTemp) drawMiniChart(chartContexts.bodyTemp, healthState.history.map(item => item.bodyTemp), '#ffd43b')
      drawStepsRing()
      if (activeAnalysisTab.value === 'trends') {
        drawHealthTrendChart()
      }
    }

    // 生命周期
    onMounted(() => {
      setTimeout(() => {
        initHealthCharts()
      }, 500)

      // 模拟健康数据更新
      const healthDataInterval = setInterval(() => {
        const mockHealthData = {
          heartRate: 70 + Math.round(Math.random() * 20 - 10),
          spO2: 96 + Math.round(Math.random() * 4),
          bodyTemp: 36.5 + (Math.random() * 1.0 - 0.5),
          steps: healthState.current.steps + Math.round(Math.random() * 50)
        }
        
        dataStore.updateHealthData(mockHealthData)
        updateHealthCharts()
      }, 10000) // 每10秒更新一次

      onUnmounted(() => {
        clearInterval(healthDataInterval)
      })
    })

    return {
      activeAnalysisTab,
      trendPeriod,
      healthState,
      healthScore,
      healthStatusClass,
      healthStatusIcon,
      healthStatusText,
      heartRateStatus,
      spO2Status,
      bodyTempStatus,
      trendPeriodText,
      dailyRating,
      switchAnalysisTab,
      setTrendPeriod,
      formatDate,
      syncHealthData,
      showHealthSettings
    }
  }
}
</script>

<style scoped>
.health-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #0f0f1c 0%, #1a1a2e 100%);
  padding: 20rpx;
}

/* 健康状态总览 */
.health-overview {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20px);
  border-radius: 30rpx;
  padding: 40rpx;
  margin-bottom: 30rpx;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.overview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25rpx;
}

.overview-title {
  font-size: 36rpx;
  color: #ffffff;
  font-weight: 700;
}

.health-score {
  text-align: center;
}

.score-value {
  display: block;
  font-size: 48rpx;
  color: #3cc51f;
  font-weight: 700;
  line-height: 1;
}

.score-label {
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.6);
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 15rpx;
  padding: 20rpx 30rpx;
  border-radius: 25rpx;
  transition: all 0.3s ease;
}

.status-indicator.excellent {
  background: rgba(60, 197, 31, 0.1);
  border: 1px solid rgba(60, 197, 31, 0.3);
}

.status-indicator.good {
  background: rgba(33, 150, 243, 0.1);
  border: 1px solid rgba(33, 150, 243, 0.3);
}

.status-indicator.fair {
  background: rgba(255, 193, 7, 0.1);
  border: 1px solid rgba(255, 193, 7, 0.3);
}

.status-indicator.poor {
  background: rgba(220, 53, 69, 0.1);
  border: 1px solid rgba(220, 53, 69, 0.3);
}

.status-icon {
  font-size: 32rpx;
}

.status-text {
  font-size: 28rpx;
  color: #ffffff;
  font-weight: 600;
}

/* 健康指标卡片 */
.health-cards {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.health-card {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20px);
  border-radius: 20rpx;
  padding: 25rpx;
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.health-card:active {
  transform: scale(0.98);
  background: rgba(255, 255, 255, 0.08);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.card-icon {
  font-size: 28rpx;
}

.card-title {
  flex: 1;
  font-size: 24rpx;
  color: #ffffff;
  font-weight: 600;
  margin-left: 10rpx;
}

.card-status {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  position: relative;
}

.card-status.normal {
  background: #3cc51f;
}

.card-status.optimal {
  background: #00bcd4;
  animation: pulse 2s infinite;
}

.card-status.high,
.card-status.elevated {
  background: #ffc107;
}

.card-status.low {
  background: #ff9800;
}

.card-status.critical {
  background: #dc3545;
  animation: urgent 1s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

@keyframes urgent {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.3; }
}

.status-dot {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
}

.card-main {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15rpx;
}

.metric-display {
  display: flex;
  align-items: baseline;
  gap: 5rpx;
}

.metric-value {
  font-size: 36rpx;
  color: #ffffff;
  font-weight: 700;
  line-height: 1;
}

.metric-unit {
  font-size: 18rpx;
  color: rgba(255, 255, 255, 0.6);
}

.metric-chart {
  width: 100rpx;
  height: 60rpx;
}

.mini-chart {
  width: 100%;
  height: 100%;
}

.card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.metric-label {
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.8);
}

.metric-range {
  font-size: 18rpx;
  color: rgba(255, 255, 255, 0.5);
}

/* 步数特殊卡片 */
.health-card.steps .card-main {
  flex-direction: column;
  align-items: center;
  gap: 15rpx;
}

.steps-progress {
  background: rgba(60, 197, 31, 0.1);
  border: 1px solid rgba(60, 197, 31, 0.3);
  border-radius: 15rpx;
  padding: 5rpx 15rpx;
}

.progress-text {
  font-size: 18rpx;
  color: #3cc51f;
  font-weight: 600;
}

.steps-ring {
  position: relative;
  width: 120rpx;
  height: 120rpx;
}

.ring-chart {
  width: 100%;
  height: 100%;
}

.ring-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
}

.ring-goal {
  display: block;
  font-size: 20rpx;
  color: #ffffff;
  font-weight: 600;
  line-height: 1;
}

.ring-label {
  font-size: 16rpx;
  color: rgba(255, 255, 255, 0.6);
}

/* 分析选项卡 */
.analysis-tabs {
  display: flex;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 15rpx;
  padding: 8rpx;
  margin-bottom: 30rpx;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 20rpx;
  border-radius: 10rpx;
  transition: all 0.3s ease;
}

.tab-item.active {
  background: rgba(60, 197, 31, 0.2);
  border: 1px solid rgba(60, 197, 31, 0.3);
}

.tab-text {
  font-size: 24rpx;
  color: #ffffff;
  font-weight: 500;
}

/* 分析内容 */
.analysis-section {
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20rpx); }
  to { opacity: 1; transform: translateY(0); }
}

/* 趋势图表 */
.trend-chart-card {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20px);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.chart-title {
  font-size: 28rpx;
  color: #ffffff;
  font-weight: 600;
}

.chart-controls {
  display: flex;
  gap: 10rpx;
}

.control-btn {
  padding: 10rpx 20rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 15rpx;
  color: rgba(255, 255, 255, 0.7);
  font-size: 20rpx;
  transition: all 0.3s ease;
}

.control-btn.active {
  background: rgba(60, 197, 31, 0.2);
  color: #3cc51f;
  border: 1px solid rgba(60, 197, 31, 0.3);
}

.chart-container {
  height: 400rpx;
  margin-bottom: 20rpx;
}

.trend-chart {
  width: 100%;
  height: 100%;
}

.chart-legend {
  display: flex;
  justify-content: center;
  gap: 30rpx;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 10rpx;
}

.legend-color {
  width: 20rpx;
  height: 20rpx;
  border-radius: 50%;
}

.legend-color.heart {
  background: #ff6b6b;
}

.legend-color.spo2 {
  background: #4dabf7;
}

.legend-color.temp {
  background: #ffd43b;
}

.legend-text {
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.7);
}

/* 健康统计 */
.health-stats {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20px);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.stats-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25rpx;
}

.stats-title {
  font-size: 28rpx;
  color: #ffffff;
  font-weight: 600;
}

.stats-period {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.6);
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
}

.stat-item {
  padding: 25rpx;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 15rpx;
  border: 1px solid rgba(255, 255, 255, 0.05);
}

.stat-label {
  display: block;
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.6);
  margin-bottom: 10rpx;
}

.stat-value {
  display: block;
  font-size: 28rpx;
  color: #ffffff;
  font-weight: 600;
  margin-bottom: 10rpx;
}

.stat-trend {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 5rpx 12rpx;
  border-radius: 12rpx;
  font-size: 18rpx;
}

.stat-trend.up {
  background: rgba(220, 53, 69, 0.1);
  color: #dc3545;
}

.stat-trend.down {
  background: rgba(60, 197, 31, 0.1);
  color: #3cc51f;
}

.stat-trend.stable {
  background: rgba(255, 193, 7, 0.1);
  color: #ffc107;
}

.trend-icon {
  font-size: 16rpx;
}

.trend-text {
  font-size: 18rpx;
  font-weight: 500;
}

/* 每日报告 */
.daily-report {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20px);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.report-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.report-title {
  font-size: 28rpx;
  color: #ffffff;
  font-weight: 600;
}

.report-date {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.6);
}

.report-summary {
  margin-bottom: 30rpx;
}

.summary-card {
  padding: 30rpx;
  border-radius: 20rpx;
  text-align: center;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.summary-card.excellent {
  background: linear-gradient(135deg, rgba(60, 197, 31, 0.1) 0%, rgba(76, 217, 100, 0.1) 100%);
  border-color: rgba(60, 197, 31, 0.3);
}

.summary-card.good {
  background: linear-gradient(135deg, rgba(33, 150, 243, 0.1) 0%, rgba(100, 181, 246, 0.1) 100%);
  border-color: rgba(33, 150, 243, 0.3);
}

.summary-card.fair {
  background: linear-gradient(135deg, rgba(255, 193, 7, 0.1) 0%, rgba(255, 235, 59, 0.1) 100%);
  border-color: rgba(255, 193, 7, 0.3);
}

.summary-card.poor {
  background: linear-gradient(135deg, rgba(220, 53, 69, 0.1) 0%, rgba(244, 67, 54, 0.1) 100%);
  border-color: rgba(220, 53, 69, 0.3);
}

.summary-icon {
  display: block;
  font-size: 48rpx;
  margin-bottom: 15rpx;
}

.summary-title {
  display: block;
  font-size: 32rpx;
  color: #ffffff;
  font-weight: 600;
  margin-bottom: 15rpx;
}

.summary-desc {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.4;
}

/* 报告详情 */
.report-details {
  display: flex;
  flex-direction: column;
  gap: 25rpx;
}

.detail-section {
  padding: 25rpx;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 15rpx;
  border: 1px solid rgba(255, 255, 255, 0.05);
}

.section-title {
  display: block;
  font-size: 26rpx;
  color: #ffffff;
  font-weight: 600;
  margin-bottom: 20rpx;
}

.detail-content {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15rpx 20rpx;
  background: rgba(255, 255, 255, 0.02);
  border-radius: 12rpx;
}

.detail-label {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.7);
}

.detail-value {
  font-size: 24rpx;
  color: #ffffff;
  font-weight: 600;
}

.detail-status {
  padding: 8rpx 15rpx;
  border-radius: 12rpx;
  font-size: 18rpx;
  font-weight: 500;
}

.detail-status.excellent {
  background: rgba(60, 197, 31, 0.1);
  color: #3cc51f;
}

.detail-status.good {
  background: rgba(33, 150, 243, 0.1);
  color: #2196f3;
}

/* 健康建议 */
.health-advice {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20px);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.advice-header {
  text-align: center;
  margin-bottom: 30rpx;
}

.advice-title {
  display: block;
  font-size: 32rpx;
  color: #ffffff;
  font-weight: 600;
  margin-bottom: 10rpx;
}

.advice-subtitle {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.6);
  line-height: 1.4;
}

.advice-categories {
  display: flex;
  flex-direction: column;
  gap: 25rpx;
}

.advice-category {
  padding: 25rpx;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 15rpx;
  border: 1px solid rgba(255, 255, 255, 0.05);
}

.category-header {
  display: flex;
  align-items: center;
  gap: 15rpx;
  margin-bottom: 20rpx;
}

.category-icon {
  font-size: 28rpx;
}

.category-title {
  font-size: 26rpx;
  color: #ffffff;
  font-weight: 600;
}

.advice-list {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.advice-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 15rpx;
  padding: 20rpx;
  background: rgba(255, 255, 255, 0.02);
  border-radius: 12rpx;
}

.advice-text {
  flex: 1;
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.4;
}

.advice-priority {
  padding: 6rpx 12rpx;
  border-radius: 10rpx;
  font-size: 18rpx;
  font-weight: 500;
  white-space: nowrap;
}

.advice-priority.high {
  background: rgba(220, 53, 69, 0.1);
  color: #dc3545;
  border: 1px solid rgba(220, 53, 69, 0.3);
}

.advice-priority.medium {
  background: rgba(255, 193, 7, 0.1);
  color: #ffc107;
  border: 1px solid rgba(255, 193, 7, 0.3);
}

.advice-priority.low {
  background: rgba(33, 150, 243, 0.1);
  color: #2196f3;
  border: 1px solid rgba(33, 150, 243, 0.3);
}

/* 同步按钮 */
.sync-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15rpx;
  padding: 25rpx;
  margin: 30rpx 0;
  background: rgba(60, 197, 31, 0.1);
  border: 1px solid rgba(60, 197, 31, 0.3);
  border-radius: 25rpx;
  transition: all 0.3s ease;
}

.sync-button:active {
  transform: scale(0.98);
  background: rgba(60, 197, 31, 0.2);
}

.sync-icon {
  font-size: 28rpx;
}

.sync-text {
  font-size: 26rpx;
  color: #ffffff;
  font-weight: 600;
}

/* 浮动按钮 */
.fab-health {
  position: fixed;
  bottom: 120rpx;
  right: 40rpx;
  width: 100rpx;
  height: 100rpx;
  background: linear-gradient(45deg, #ff6b6b, #ee5a52);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 10rpx 30rpx rgba(255, 107, 107, 0.3);
  z-index: 100;
  transition: all 0.3s ease;
}

.fab-health:active {
  transform: scale(0.9);
}

.fab-icon {
  font-size: 36rpx;
  color: #ffffff;
}
</style> 