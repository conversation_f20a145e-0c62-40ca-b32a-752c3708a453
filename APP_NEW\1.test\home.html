<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>智能家居控制系统</title>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">
    <script src="key.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Poppins', -apple-system, BlinkMacSystemFont, "SF Pro Text", "Helvetica Neue", Arial, sans-serif;
        }
        
        body {
            background: linear-gradient(120deg, #e3f0ff 0%, #f7f7f7 100%);
            display: flex;
            flex-direction: column;
            min-height: 100vh;
            height: 100vh;
            overflow: hidden;
        }
        
        .container {
            display: flex;
            width: 100vw;
            height: 100vh;
            margin: 0;
            padding: 0;
            flex-wrap: wrap;
            justify-content: center;
            align-items: center;
            background: none;
        }
        
        /* 主页面设计 */
        .home-container {
            width: 100%;
            max-width: 375px;
            height: auto;
            aspect-ratio: 375/812;
            background: linear-gradient(145deg, #f0f0f0, #fafafa);
            border-radius: 50px;
            overflow: hidden;
            box-shadow: 0 20px 60px -10px rgba(30, 136, 229, 0.18);
            position: relative;
            display: flex;
            flex-direction: column;
            max-height: 92vh;
        }
        
        /* 顶部状态栏 */
        .status-bar {
            display: flex;
            justify-content: space-between;
            padding: 16px 20px 10px;
            align-items: center;
            position: relative;
            z-index: 10;
            background-color: #f0f0f0;
        }
        
        .date-info {
            display: flex;
            flex-direction: column;
        }
        
        .date-weather {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 4px;
        }
        
        .date {
            font-size: 12px;
            color: #999;
        }
        
        .weather-icon-small {
            width: 20px;
            height: 20px;
            color: #f9ad3d;
        }
        
        .location {
            font-size: 18px;
            font-weight: 600;
            color: #222;
        }
        
        .notification-icon {
            width: 40px;
            height: 40px;
            background-color: #222;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .notification-icon:hover {
            transform: scale(1.05);
        }
        
        .notification-icon svg {
            width: 20px;
            height: 20px;
            color: white;
        }
        
        /* 天气信息卡片 */
        .weather-card {
            background-color: white;
            border-radius: 16px;
            margin: 0 20px 20px;
            padding: 16px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }
        
        .weather-card-title {
            font-size: 14px;
            font-weight: 600;
            color: #666;
            margin-bottom: 16px;
        }
        
        .weather-info {
            display: flex;
            justify-content: space-between;
        }
        
        .weather-condition {
            display: flex;
            align-items: center;
        }
        
        .weather-icon {
            width: 28px;
            height: 28px;
            margin-right: 10px;
        }
        
        .weather-data {
            display: flex;
            align-items: center;
        }
        
        .weather-temp {
            font-size: 16px;
            font-weight: 600;
            margin: 0 12px;
            color: #333;
        }
        
        .temp-divider {
            font-size: 16px;
            color: #ddd;
        }
        
        /* 设备状态摘要 */
        .device-summary {
            display: flex;
            padding: 16px 20px 20px;
            margin-bottom: 16px;
            background-color: white;
            border-bottom-left-radius: 20px;
            border-bottom-right-radius: 20px;
            box-shadow: 0 4px 6px -6px rgba(0, 0, 0, 0.1);
            justify-content: space-between;
        }
        
        .summary-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 0 10px;
            justify-content: center;
        }
        
        .summary-value {
            font-size: 24px;
            font-weight: 700;
            color: #333;
            margin-bottom: 4px;
        }
        
        .summary-label {
            font-size: 12px;
            color: #999;
            text-align: center;
        }
        
        .summary-item:last-child {
            flex-direction: row;
            gap: 8px;
        }
        
        .summary-circle {
            width: 28px;
            height: 28px;
            border-radius: 50%;
            background-color: #f0f0f0;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 0;
        }
        
        .status-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background-color: #4caf50;
            box-shadow: 0 0 8px rgba(76, 175, 80, 0.6);
        }
        
        .status-dot.warning {
            background-color: #ff9800;
            box-shadow: 0 0 8px rgba(255, 152, 0, 0.6);
        }
        
        .status-dot.error {
            background-color: #f44336;
            box-shadow: 0 0 8px rgba(244, 67, 54, 0.6);
        }
        
        /* 设备卡片区域 */
        .room-cards {
            flex: 1;
            overflow-y: auto;
            padding: 12px 20px;
            padding-bottom: 20px;
        }
        
        .device-cards-container {
            display: flex;
            overflow-x: auto;
            scroll-snap-type: x mandatory;
            gap: 14px;
            padding: 4px 0;
            scrollbar-width: none;  /* Firefox */
            margin-bottom: 20px;
        }
        
        .device-cards-container::-webkit-scrollbar {
            display: none;  /* Chrome, Safari, Opera */
        }
        
        .room-cards::-webkit-scrollbar {
            width: 4px;
        }
        
        .room-cards::-webkit-scrollbar-track {
            background: transparent;
        }
        
        .room-cards::-webkit-scrollbar-thumb {
            background-color: rgba(0, 0, 0, 0.1);
            border-radius: 20px;
        }
        
        .room-card {
            background-color: white;
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
            cursor: pointer;
            min-width: 280px;
            scroll-snap-align: start;
        }
        
        .device-card {
            background-color: white;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
            cursor: pointer;
            min-width: 160px;
            width: 160px;
            height: 160px;
            scroll-snap-align: start;
            flex-shrink: 0;
            display: flex;
            flex-direction: column;
            position: relative;
            border: 1px solid rgba(0, 0, 0, 0.05);
        }
        
        .device-card.running {
            border-left: 4px solid #4caf50;
        }
        
        .device-card.stopped {
            border-left: 4px solid #f44336;
        }
        
        .device-card.sensor {
            border-left: 4px solid #2196f3;
        }
        
        .device-card:hover, .room-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
        }
        
        .mode-cards-container {
            display: flex;
            overflow-x: auto;
            scroll-snap-type: x mandatory;
            gap: 16px;
            padding: 4px 0;
            scrollbar-width: none;
            margin-bottom: 16px;
        }
        
        .mode-cards-container::-webkit-scrollbar {
            display: none;
        }
        
        .mode-card {
            background-color: white;
            border-radius: 16px;
            padding: 16px;
            min-width: 120px;
            height: 120px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            cursor: pointer;
            transition: all 0.3s ease;
            scroll-snap-align: start;
            flex-shrink: 0;
            border: 1px solid rgba(0, 0, 0, 0.05);
        }
        
        .mode-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
        }
        
        .mode-card.active {
            background: linear-gradient(145deg, #f0f9ff, #e3f2fd);
            border: 1px solid #bbdefb;
        }
        
        .mode-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            background-color: #f5f5f5;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 12px;
        }
        
        .mode-icon svg {
            width: 28px;
            height: 28px;
            color: #444;
        }
        
        .mode-name {
            font-size: 14px;
            font-weight: 600;
            color: #333;
            text-align: center;
        }
        
        .room-image {
            width: 100%;
            height: 120px;
            object-fit: cover;
        }
        
        .room-details {
            padding: 12px;
        }
        
        .device-details {
            padding: 16px;
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            height: 100%;
        }
        
        .device-icon {
            width: 60px;
            height: 60px;
            border-radius: 16px;
            background: linear-gradient(145deg, #f0f0f0, #fafafa);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
        }
        
        .device-icon svg {
            width: 28px;
            height: 28px;
            color: #444;
        }
        
        .device-icon.gear svg {
            color: #ff9800;
        }
        
        .device-icon.settings svg {
            color: #2196f3;
        }
        
        .device-icon.plus svg {
            color: #4caf50;
        }
        
        .device-icon.star svg {
            color: #9c27b0;
        }
        
        .device-icon.minus svg {
            color: #f44336;
        }
        
        .device-icon.refresh svg {
            color: #00bcd4;
        }
        
        .device-icon.info svg {
            color: #607d8b;
        }
        
        .device-icon.more svg {
            color: #3f51b5;
        }
        
        .room-name {
            font-size: 18px;
            font-weight: 700;
            margin-bottom: 6px;
            color: #222;
        }
        
        .device-name {
            font-size: 15px;
            font-weight: 600;
            margin-bottom: 8px;
            color: #333;
        }
        
        .device-status {
            font-size: 12px;
            font-weight: 500;
            padding: 4px 10px;
            border-radius: 12px;
            background-color: #f0f0f0;
            display: inline-block;
            width: fit-content;
            text-align: center;
            margin-bottom: 4px;
        }
        
        .device-status.running {
            color: #4caf50;
            background-color: rgba(76, 175, 80, 0.1);
            border: 1px solid rgba(76, 175, 80, 0.2);
        }
        
        .device-status.stopped {
            color: #f44336;
            background-color: rgba(244, 67, 54, 0.1);
            border: 1px solid rgba(244, 67, 54, 0.2);
        }
        
        .device-status.sensor-value {
            color: #2196f3;
            background-color: rgba(33, 150, 243, 0.1);
            border: 1px solid rgba(33, 150, 243, 0.2);
        }
        
        .device-count {
            font-size: 12px;
            color: #999;
            display: flex;
            align-items: center;
        }
        
        .device-count .active-indicator {
            display: inline-block;
            width: 6px;
            height: 6px;
            background-color: #4caf50;
            border-radius: 50%;
            margin-left: 8px;
        }
        
        .section-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 12px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .section-subtitle {
            color: #999;
            font-size: 12px;
            font-weight: normal;
        }
        
        /* 底部导航栏 */
        .bottom-nav {
            display: flex;
            justify-content: space-around;
            padding: 12px 20px 8px;
            background-color: white;
            border-top-left-radius: 16px;
            border-top-right-radius: 16px;
            box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
        }
        
        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .nav-icon {
            width: 22px;
            height: 22px;
            margin-bottom: 4px;
            opacity: 0.5;
            transition: all 0.3s ease;
            color: #aaa;
        }
        
        .nav-text {
            font-size: 10px;
            color: #aaa;
            transition: all 0.3s ease;
        }
        
        .nav-item.active .nav-icon {
            opacity: 1;
            color: #333;
        }
        
        .nav-item.active .nav-text {
            color: #333;
            font-weight: 600;
        }
        
        /* 设计说明部分 */
        /* 删除设计说明相关样式 */
        /* 删除 .design-notes、.design-note 及相关样式 */

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes slideUp {
            from { transform: translateY(10px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }

        .animate-fadeIn {
            animation: fadeIn 0.5s ease-in-out;
        }

        .animate-slideUp {
            animation: slideUp 0.5s ease-in-out;
        }

        @media (max-width: 1000px) {
            .container {
                flex-direction: column;
                align-items: center;
                padding: 0;
            }
            .home-container {
                max-width: 100vw;
                max-height: 90vh;
                border-radius: 30px;
            }
        }
        @media (max-width: 480px) {
            .home-container {
                border-radius: 18px;
                max-height: 98vh;
            }
        }
        
        /* 天气图标样式 */
        .weather-icon svg {
            width: 30px;
            height: 30px;
        }
        
        .sunny {
            color: #ff9800;
        }
        
        .cloudy {
            color: #90a4ae;
        }
        
        .rainy {
            color: #64b5f6;
        }
        
        .snowy {
            color: #b0bec5;
        }

        /* 动画效果 */
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        /* 注意：已移除加载样式，现在使用统一的toast样式 */

        /* 提示消息样式 - 参考注册页面 */
        .toast-container {
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 9999;
            width: 320px;
            max-width: 90%;
            pointer-events: none;
        }
        
        .toast-message {
            padding: 16px;
            margin-bottom: 12px;
            border-radius: 16px;
            background-color: white;
            color: #333;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
            display: flex;
            align-items: center;
            transform: translateY(-20px);
            opacity: 0;
            transition: all 0.3s ease;
            overflow: hidden;
        }
        
        .toast-message.show {
            transform: translateY(0);
            opacity: 1;
        }
        
        .toast-message.success {
            border-left: 4px solid #4caf50;
        }
        
        .toast-message.error {
            border-left: 4px solid #f44336;
        }
        
        .toast-message.info {
            border-left: 4px solid #2196f3;
        }
        
        .toast-message .toast-icon {
            margin-right: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .toast-message .toast-content {
            flex: 1;
            font-size: 14px;
            font-weight: 500;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 主页面部分 -->
        <div class="home-container">
            <!-- 顶部状态栏 -->
            <div class="status-bar">
                <div class="date-info">
                    <div class="date-weather">
                        <span class="date" id="currentDate">9 August</span>
                        <div class="weather-icon-small">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" stroke="none">
                                <path d="M12 2a1 1 0 0 1 1 1v1a1 1 0 1 1-2 0V3a1 1 0 0 1 1-1zm9 9h-1a1 1 0 1 1 0-2h1a1 1 0 1 1 0 2zm-9 9a1 1 0 0 1-1-1v-1a1 1 0 1 1 2 0v1a1 1 0 0 1-1 1zM3 11h1a1 1 0 1 1 0 2H3a1 1 0 1 1 0-2zm15.7-6.3a1 1 0 1 1-1.4 1.4l-.7-.7a1 1 0 1 1 1.4-1.4l.7.7zM6.3 18.7a1 1 0 0 1-1.4-1.4l.7-.7a1 1 0 1 1 1.4 1.4l-.7.7zm12.4 0-.7-.7a1 1 0 1 1 1.4-1.4l.7.7a1 1 0 1 1-1.4 1.4zM6.3 5.3l-.7-.7a1 1 0 0 1 1.4-1.4l.7.7a1 1 0 1 1-1.4 1.4z"/>
                                <circle cx="12" cy="12" r="4"/>
                            </svg>
                        </div>
                    </div>
                    <span class="location">多云</span>
                </div>
                <div class="notification-icon">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9"></path>
                        <path d="M13.73 21a2 2 0 0 1-3.46 0"></path>
                    </svg>
                </div>
            </div>
            
            <!-- 天气信息卡片 -->
            <div class="weather-card animate-fadeIn">
                <div class="weather-card-title">一般信息</div>
                <div class="weather-info">
                    <div class="weather-condition" id="weatherCondition">
                        <div class="weather-icon" id="weatherIcon">
                            <!-- 天气图标将根据当前天气动态插入 -->
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M13 3a9 9 0 1 0 9 9"></path>
                                <path d="M18 5v4h-4"></path>
                            </svg>
                        </div>
                        <span>多雨</span>
                    </div>
                    <div class="weather-data">
                        <span class="weather-temp">室内 25°C</span>
                        <span class="temp-divider">|</span>
                        <span class="weather-temp">室外 20°C</span>
                    </div>
                </div>
            </div>
            
            <!-- 设备状态摘要 -->
            <div class="device-summary">
                <div class="summary-item">
                    <div class="summary-value" id="onlineDevices">0</div>
                    <div class="summary-label">设备在线</div>
                </div>
                <div class="summary-item">
                    <div class="summary-value" id="runningDevices">0</div>
                    <div class="summary-label">运行中</div>
                </div>
                <div class="summary-item">
                    <div class="summary-value" id="totalDevices">8</div>
                    <div class="summary-label">总设备</div>
                </div>
                <div class="summary-item">
                    <div class="summary-circle" id="systemStatus">
                        <div class="status-dot"></div>
                    </div>
                    <div class="summary-label">系统状态</div>
                </div>
            </div>
            
            <!-- 设备卡片区域 -->
            <div class="room-cards" id="roomCards">
                <!-- 所有设备 -->
                <div class="section-title">
                    所有设备
                    <span class="section-subtitle">
                        <span id="runningDevicesCount">0</span>/<span id="totalDevicesCount">8</span> 运行中
                    </span>
                </div>
                <div style="margin-bottom: 10px; font-size: 12px; color: #666;">
                    点击传感器或按住Ctrl点击设备可查看历史数据
                </div>
                <div class="device-cards-container" id="allDevices">
                    <!-- 设备卡片将通过JS动态生成 -->
                </div>
                
                <!-- 操作模式 -->
                <div class="section-title">
                    操作模式
                    <span class="section-subtitle">选择运行模式</span>
                </div>
                <div class="mode-cards-container" id="operationModes">
                    <div class="mode-card">
                        <div class="mode-icon">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                                <polyline points="22 4 12 14.01 9 11.01"></polyline>
                            </svg>
                        </div>
                        <div class="mode-name">自动模式</div>
                    </div>
                    <div class="mode-card">
                        <div class="mode-icon">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                                <line x1="9" y1="9" x2="15" y2="15"></line>
                                <line x1="15" y1="9" x2="9" y2="15"></line>
                            </svg>
                        </div>
                        <div class="mode-name">停止模式</div>
                    </div>
                    <div class="mode-card">
                        <div class="mode-icon">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <circle cx="12" cy="12" r="10"></circle>
                                <path d="M12 16v-4"></path>
                                <path d="M12 8h.01"></path>
                            </svg>
                        </div>
                        <div class="mode-name">诊断模式</div>
                    </div>
                    <div class="mode-card">
                        <div class="mode-icon">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M12 20V10"></path>
                                <path d="M18 20V4"></path>
                                <path d="M6 20v-6"></path>
                            </svg>
                        </div>
                        <div class="mode-name">高级设置</div>
                    </div>
                </div>
            </div>
            
            <!-- 底部导航栏 -->
            <div class="bottom-nav">
                <div class="nav-item active">
                    <svg class="nav-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" stroke="none">
                        <path d="M20 10.76a1 1 0 0 0-.35-.76l-7.42-6.52a1 1 0 0 0-1.46 0L3.35 10a1 1 0 0 0-.35.76V20a1 1 0 0 0 1 1h16a1 1 0 0 0 1-1v-9.24z"/>
                    </svg>
                    <span class="nav-text">首页</span>
                </div>
                <div class="nav-item">
                    <svg class="nav-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M13 3a9 9 0 1 0 9 9"></path>
                        <path d="M18 5v4h-4"></path>
                    </svg>
                    <span class="nav-text">历史</span>
                </div>
                <div class="nav-item">
                    <svg class="nav-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9"></path>
                        <path d="M13.73 21a2 2 0 0 1-3.46 0"></path>
                    </svg>
                    <span class="nav-text">通知</span>
                </div>
                <div class="nav-item">
                    <svg class="nav-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                        <circle cx="12" cy="7" r="4"></circle>
                    </svg>
                    <span class="nav-text">我的</span>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 简化的createCommonToken函数获取逻辑
        function initializeFunctions() {
            // 直接从window对象获取函数（通过script标签加载）
            if (typeof window.createCommonToken === 'function' && 
                typeof window.simulateAPIData === 'function') {
                console.log('成功从全局获取key.js中的函数');
            } else {
                console.warn('无法获取key.js中的函数，将使用默认实现');
                
                // 提供默认实现（作为备份）
                window.createCommonToken = function(params) {
                    console.log('使用默认token生成函数');
                    return 'default-token-for-testing';
                };
                
                window.simulateAPIData = function() {
                    return {
                        data: [
                            { value: "7.0" },     // pH
                            { value: "true" },    // Pump1
                            { value: "true" },    // Pump2
                            { value: "true" },    // Pump3
                            { value: "false" },   // Pump4
                            { value: "false" },   // Pump5
                            { value: "true" },    // Pump6
                            { value: "0.5" },     // 其他数值
                            { value: "0.3" },     // 其他数值
                            { value: "0.8" },     // 其他数值
                            { value: "75%" }      // 水位
                        ]
                    };
                };
            }
        }
        
        // 初始化函数
        initializeFunctions();

        // 模拟的设备数据（实际应用中从API获取）
        let deviceData = {
            ph: null,
            Water: null,
            Pump1: null,
            Pump2: null,
            Pump3: null,
            Pump4: null,
            Pump5: null,
            Pump6: null,
            
            // 设备状态
            subDevices: [],
            lastUpdateTime: null,
            connectionStatus: false // 连接状态
        };
        
        // 设备图标映射
        const deviceIcons = {
            'gear': `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="3"></circle><path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"></path></svg>`,
            'settings': `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M12 20V10"></path><path d="M18 20V4"></path><path d="M6 20v-6"></path></svg>`,
            'plus': `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><line x1="12" y1="8" x2="12" y2="16"></line><line x1="8" y1="12" x2="16" y2="12"></line></svg>`,
            'star': `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"></polygon></svg>`,
            'minus': `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><line x1="8" y1="12" x2="16" y2="12"></line></svg>`,
            'refresh': `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M23 4v6h-6"></path><path d="M1 20v-6h6"></path><path d="M3.51 9a9 9 0 0 1 14.85-3.36L23 10M1 14l4.64 4.36A9 9 0 0 0 20.49 15"></path></svg>`,
            'info': `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><line x1="12" y1="16" x2="12" y2="12"></line><line x1="12" y1="8" x2="12.01" y2="8"></line></svg>`,
            'more': `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M12 3v18"></path><path d="M3 12h18"></path></svg>`
        };
        
        // 更新当前日期
        function updateDate() {
            const now = new Date();
            const options = { day: 'numeric', month: 'long' };
            // 中文日期格式
            const formattedDate = now.toLocaleDateString('zh-CN', options);
            document.getElementById('currentDate').textContent = formattedDate;
        }
        
        // 设置天气图标
        function setWeatherIcon(condition) {
            const weatherIcon = document.getElementById('weatherIcon');
            let iconHTML = '';
            
            switch(condition) {
                case 'sunny':
                    iconHTML = `<svg class="sunny" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <circle cx="12" cy="12" r="5"></circle>
                                    <line x1="12" y1="1" x2="12" y2="3"></line>
                                    <line x1="12" y1="21" x2="12" y2="23"></line>
                                    <line x1="4.22" y1="4.22" x2="5.64" y2="5.64"></line>
                                    <line x1="18.36" y1="18.36" x2="19.78" y2="19.78"></line>
                                    <line x1="1" y1="12" x2="3" y2="12"></line>
                                    <line x1="21" y1="12" x2="23" y2="12"></line>
                                    <line x1="4.22" y1="19.78" x2="5.64" y2="18.36"></line>
                                    <line x1="18.36" y1="5.64" x2="19.78" y2="4.22"></line>
                                </svg>`;
                    document.getElementById('weatherCondition').querySelector('span').textContent = '晴天';
                    break;
                case 'cloudy':
                    iconHTML = `<svg class="cloudy" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M18 10h-1.26A8 8 0 1 0 9 20h9a5 5 0 0 0 0-10z"></path>
                                </svg>`;
                    document.getElementById('weatherCondition').querySelector('span').textContent = '多云';
                    break;
                case 'rainy':
                    iconHTML = `<svg class="rainy" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <line x1="16" y1="13" x2="16" y2="21"></line>
                                    <line x1="8" y1="13" x2="8" y2="21"></line>
                                    <line x1="12" y1="15" x2="12" y2="23"></line>
                                    <path d="M20 16.58A5 5 0 0 0 18 7h-1.26A8 8 0 1 0 4 15.25"></path>
                                </svg>`;
                    document.getElementById('weatherCondition').querySelector('span').textContent = '下雨';
                    break;
                case 'snowy':
                    iconHTML = `<svg class="snowy" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M20 17.58A5 5 0 0 0 18 8h-1.26A8 8 0 1 0 4 16.25"></path>
                                    <line x1="8" y1="16" x2="8.01" y2="16"></line>
                                    <line x1="8" y1="20" x2="8.01" y2="20"></line>
                                    <line x1="12" y1="18" x2="12.01" y2="18"></line>
                                    <line x1="12" y1="22" x2="12.01" y2="22"></line>
                                    <line x1="16" y1="16" x2="16.01" y2="16"></line>
                                    <line x1="16" y1="20" x2="16.01" y2="20"></line>
                                </svg>`;
                    document.getElementById('weatherCondition').querySelector('span').textContent = '下雪';
                    break;
            }
            
            weatherIcon.innerHTML = iconHTML;
        }
        
        // 获取天气信息
        async function fetchChengduWeather() {
            try {
                // 在实际应用中，这里应该调用真实的天气API
                // 这里使用模拟数据演示不同天气状态
                const weatherConditions = ['sunny', 'cloudy', 'rainy', 'snowy'];
                const randomIndex = Math.floor(Math.random() * weatherConditions.length);
                const currentCondition = weatherConditions[randomIndex];
                
                // 设置天气图标和文本
                setWeatherIcon(currentCondition);
                
                // 模拟室内外温度
                const outdoorTemp = Math.floor(Math.random() * 15) + 15; // 15-30°C
                const indoorTemp = Math.floor(Math.random() * 5) + outdoorTemp - 2; // 室内温度略低或高于室外
                
                document.querySelector('.weather-data').innerHTML = `
                    <span class="weather-temp">室内 ${indoorTemp}°C</span>
                    <span class="temp-divider">|</span>
                    <span class="weather-temp">室外 ${outdoorTemp}°C</span>
                `;
            } catch (error) {
                console.error('获取天气信息失败:', error);
                setWeatherIcon('cloudy'); // 默认为多云
            }
        }
        
        // 创建设备卡片
        function createDeviceCard(device) {
            const card = document.createElement('div');
            
            // 设置卡片类型
            let cardClass = 'device-card animate-slideUp';
            if (device.type === 'sensor') {
                cardClass += ' sensor';
            } else {
                cardClass += device.running ? ' running' : ' stopped';
            }
            
            card.className = cardClass;
            card.id = `device-${device.id || device.name.replace(/\s+/g, '-').toLowerCase()}`;
            
            // 设置延时动画
            const delay = Math.random() * 0.3;
            card.style.animationDelay = `${delay}s`;
            
            const iconSvg = deviceIcons[device.icon] || deviceIcons['gear'];
            
            // 设置状态文本和样式
            let statusClass = '';
            let statusText = '';
            
            if (device.type === 'sensor') {
                statusClass = 'sensor-value';
                statusText = device.value;
            } else {
                statusClass = device.running ? 'running' : 'stopped';
                statusText = device.running ? '正在运行' : '已停止';
            }
            
            const lastUpdated = deviceData.lastUpdateTime ? 
                `<div style="font-size: 10px; color: #999; margin-top: 4px;">
                    ${new Date(deviceData.lastUpdateTime).toLocaleTimeString()}
                </div>` : '';
            
            card.innerHTML = `
                <div class="device-details">
                    <div>
                        <div class="device-icon ${device.icon}">${iconSvg}</div>
                        <div class="device-name">${device.name}</div>
                    </div>
                    <div>
                        <div class="device-status ${statusClass}">${statusText}</div>
                        ${lastUpdated}
                    </div>
                </div>
            `;
            
            // 添加点击事件
            card.addEventListener('click', function(event) {
                // 根据设备类型处理点击事件
                if (device.type === 'sensor') {
                    // 传感器设备点击时跳转到历史数据页面
                    const deviceId = device.name === 'PH传感器' ? 'ph' : 'water';
                    window.location.href = `history.html?device=${deviceId}`;
                } else if (event.ctrlKey || event.metaKey) {
                    // Ctrl/Cmd+点击跳转到历史页面
                    let deviceId = '';
                    if (device.name === '捕收剂阳') deviceId = 'pump1';
                    if (device.name === '捕收剂阴') deviceId = 'pump2';
                    if (device.name === '起泡剂') deviceId = 'pump3';
                    if (device.name === '活化剂') deviceId = 'pump4';
                    if (device.name === '抑制剂') deviceId = 'pump5';
                    if (device.name === '调整剂') deviceId = 'pump6';
                    
                    window.location.href = `history.html?device=${deviceId}`;
                } else {
                    // 普通点击切换设备状态并发送更新请求
                    toggleDeviceStatus(device);
                }
            });
            
            return card;
        }
        
        // 切换设备状态并发送更新
        function toggleDeviceStatus(device) {
            const newStatus = !device.running;
            const deviceId = getDeviceIdFromName(device.name);
            
            // 更新本地状态
            device.running = newStatus;
            if (device.name === '捕收剂阳') deviceData.Pump1 = newStatus;
            if (device.name === '捕收剂阴') deviceData.Pump2 = newStatus;
            if (device.name === '起泡剂') deviceData.Pump3 = newStatus;
            if (device.name === '活化剂') deviceData.Pump4 = newStatus;
            if (device.name === '抑制剂') deviceData.Pump5 = newStatus;
            if (device.name === '调整剂') deviceData.Pump6 = newStatus;
            
            // 更新UI
            updateDeviceList();
            updateDeviceSummary();
            
            // 给用户反馈
            showToast(device.name + (newStatus ? ' 已启动' : ' 已停止'), newStatus ? 'success' : 'info');
            
            // 发送更新到API (仅展示逻辑，实际API可能需要修改)
            sendDeviceUpdate(deviceId, newStatus);
        }
        
        // 获取设备ID
        function getDeviceIdFromName(name) {
            const mapping = {
                '捕收剂阳': 'Pump1',
                '捕收剂阴': 'Pump2',
                '起泡剂': 'Pump3',
                '活化剂': 'Pump4',
                '抑制剂': 'Pump5',
                '调整剂': 'Pump6',
                'PH传感器': 'ph',
                '水位传感器': 'Water'
            };
            
            return mapping[name] || '';
        }
        
        // 发送设备状态更新请求
        function sendDeviceUpdate(deviceId, status) {
            // 这里应该实现向API发送更新请求的逻辑
            console.log(`发送设备状态更新: ${deviceId} = ${status}`);
            // 实际项目中，这里应该调用真实的API
            // 模拟API请求成功
            setTimeout(() => {
                console.log(`设备${deviceId}状态已更新到${status}`);
            }, 500);
        }
        
        // 显示提示消息 - 使用新样式
        function showToast(message, type = 'info') {
            // 确保存在toast容器
            let toastContainer = document.querySelector('.toast-container');
            if (!toastContainer) {
                toastContainer = document.createElement('div');
                toastContainer.className = 'toast-container';
                document.body.appendChild(toastContainer);
            }
            
            // 创建toast消息
            const toast = document.createElement('div');
            toast.className = `toast-message ${type}`;
            
            // 根据类型添加图标
            let iconSvg = '';
            if (type === 'success') {
                iconSvg = `<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="#4caf50" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path><polyline points="22 4 12 14.01 9 11.01"></polyline></svg>`;
            } else if (type === 'error') {
                iconSvg = `<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="#f44336" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><line x1="15" y1="9" x2="9" y2="15"></line><line x1="9" y1="9" x2="15" y2="15"></line></svg>`;
            } else {
                iconSvg = `<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="#2196f3" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><line x1="12" y1="16" x2="12" y2="12"></line><line x1="12" y1="8" x2="12" y2="8"></line></svg>`;
            }
            
            // 设置内容
            toast.innerHTML = `
                <div class="toast-icon">${iconSvg}</div>
                <div class="toast-content">${message}</div>
            `;
            
            // 添加到容器
            toastContainer.appendChild(toast);
            
            // 显示动画
            setTimeout(() => { 
                toast.classList.add('show');
            }, 10);
            
            // 定时消失
            setTimeout(() => { 
                toast.classList.remove('show');
                setTimeout(() => {
                    if (toast.parentNode) {
                        toast.parentNode.removeChild(toast);
                    }
                    
                    // 如果没有更多toast，移除容器
                    if (toastContainer.children.length === 0) {
                        toastContainer.remove();
                    }
                }, 300);
            }, 3000);
        }
        
        // 更新设备摘要信息
        function updateDeviceSummary() {
            const onlineCount = deviceData.subDevices.filter(device => device.online).length;
            const runningCount = deviceData.subDevices.filter(device => device.running).length;
            const totalCount = deviceData.subDevices.length;
            
            document.getElementById('onlineDevices').textContent = onlineCount;
            document.getElementById('runningDevices').textContent = runningCount;
            document.getElementById('totalDevices').textContent = totalCount;
            document.getElementById('runningDevicesCount').textContent = runningCount;
            document.getElementById('totalDevicesCount').textContent = totalCount;
            
            // 设置系统状态指示器
            const systemStatus = document.getElementById('systemStatus');
            const statusDot = systemStatus.querySelector('.status-dot');
            
            if (runningCount === 0) {
                statusDot.classList.remove('warning');
                statusDot.classList.add('error');
            } else if (runningCount < totalCount / 2) {
                statusDot.classList.remove('error');
                statusDot.classList.add('warning');
            } else {
                statusDot.classList.remove('warning', 'error');
            }
        }
        
        // 更新设备列表
        function updateDeviceList() {
            // 如果连接失败，显示错误信息
            if (!deviceData.connectionStatus) {
                updateDeviceListWithError('无法连接到设备');
                return;
            }
            
            deviceData.subDevices = [
                { 
                    name: '捕收剂阳', 
                    id: 'pump1',
                    icon: 'gear', 
                    status: deviceData.Pump1 ? '运行' : '停止', 
                    online: deviceData.Pump1 !== null, 
                    type: 'pump', 
                    running: deviceData.Pump1,
                    active: false
                },
                { 
                    name: '捕收剂阴', 
                    id: 'pump2',
                    icon: 'settings', 
                    status: deviceData.Pump2 ? '运行' : '停止', 
                    online: deviceData.Pump2 !== null, 
                    type: 'pump', 
                    running: deviceData.Pump2,
                    active: false
                },
                { 
                    name: '起泡剂', 
                    id: 'pump3',
                    icon: 'plus', 
                    status: deviceData.Pump3 ? '运行' : '停止', 
                    online: deviceData.Pump3 !== null, 
                    type: 'pump', 
                    running: deviceData.Pump3,
                    active: false
                },
                { 
                    name: '活化剂', 
                    id: 'pump4',
                    icon: 'star', 
                    status: deviceData.Pump4 ? '运行' : '停止', 
                    online: deviceData.Pump4 !== null, 
                    type: 'pump', 
                    running: deviceData.Pump4,
                    active: false
                },
                { 
                    name: '抑制剂', 
                    id: 'pump5',
                    icon: 'minus', 
                    status: deviceData.Pump5 ? '运行' : '停止', 
                    online: deviceData.Pump5 !== null, 
                    type: 'pump', 
                    running: deviceData.Pump5,
                    active: false
                },
                { 
                    name: '调整剂', 
                    id: 'pump6',
                    icon: 'refresh', 
                    status: deviceData.Pump6 ? '运行' : '停止', 
                    online: deviceData.Pump6 !== null, 
                    type: 'pump', 
                    running: deviceData.Pump6,
                    active: false
                },
                { 
                    name: 'PH传感器', 
                    id: 'ph',
                    icon: 'info', 
                    status: deviceData.ph, 
                    online: deviceData.ph !== null, 
                    type: 'sensor', 
                    value: deviceData.ph,
                    running: deviceData.ph !== null // 传感器有值即为运行状态
                },
                { 
                    name: '水位传感器', 
                    id: 'water',
                    icon: 'more', 
                    status: deviceData.Water, 
                    online: deviceData.Water !== null, 
                    type: 'sensor', 
                    value: deviceData.Water,
                    running: deviceData.Water !== null // 传感器有值即为运行状态
                }
            ];
            
            // 更新所有设备
            const allDevices = document.getElementById('allDevices');
            allDevices.innerHTML = '';
            
            deviceData.subDevices.forEach(device => {
                allDevices.appendChild(createDeviceCard(device));
            });
            
            // 更新设备摘要
            updateDeviceSummary();
            
            // 更新最后刷新时间
            const currentTime = new Date();
            const formattedTime = currentTime.toLocaleTimeString();
            
            // 如果存在更新时间显示元素则更新
            if (document.getElementById('lastUpdateTime')) {
                document.getElementById('lastUpdateTime').textContent = `最后更新: ${formattedTime}`;
            } else {
                // 添加最后更新时间显示
                const updateTimeElement = document.createElement('div');
                updateTimeElement.id = 'lastUpdateTime';
                updateTimeElement.style.fontSize = '12px';
                updateTimeElement.style.color = '#999';
                updateTimeElement.style.textAlign = 'right';
                updateTimeElement.style.padding = '8px 20px 0';
                updateTimeElement.textContent = `最后更新: ${formattedTime}`;
                
                const roomCards = document.getElementById('roomCards');
                roomCards.insertBefore(updateTimeElement, roomCards.firstChild);
            }
        }
        
        // 设置操作模式
        function setupOperationModes() {
            const modes = document.querySelectorAll('.mode-card');
            
            modes.forEach((mode, index) => {
                mode.addEventListener('click', () => {
                    // 移除所有活动状态
                    modes.forEach(m => m.classList.remove('active'));
                    
                    // 设置当前模式为活动状态
                    mode.classList.add('active');
                    
                    // 根据选择的模式更新设备状态
                    switch(index) {
                        case 0: // 自动模式
                            deviceData.Pump1 = true;
                            deviceData.Pump2 = true;
                            deviceData.Pump3 = true;
                            deviceData.Pump4 = false;
                            deviceData.Pump5 = false;
                            deviceData.Pump6 = true;
                            break;
                        case 1: // 停止模式
                            deviceData.Pump1 = false;
                            deviceData.Pump2 = false;
                            deviceData.Pump3 = false;
                            deviceData.Pump4 = false;
                            deviceData.Pump5 = false;
                            deviceData.Pump6 = false;
                            break;
                        case 2: // 诊断模式
                            deviceData.Pump1 = true;
                            deviceData.Pump2 = false;
                            deviceData.Pump3 = true;
                            deviceData.Pump4 = false;
                            deviceData.Pump5 = true;
                            deviceData.Pump6 = false;
                            break;
                        case 3: // 高级设置
                            // 不改变设备状态
                            break;
                    }
                    
                    // 更新设备列表
                    updateDeviceList();
                });
            });
            
            // 默认选择自动模式
            modes[0].classList.add('active');
        }
        
        // 从OneNET获取设备数据
        function fetchDeviceData() {
            showLoadingState(true);
            
            try {
                // 使用实际OneNET数据
                const accessKey = '****************************************************************';
                
                // 由于Web Crypto API在浏览器中的限制，我们使用一个两步策略：
                // 1. 尝试使用异步API获取token并调用OneNET API
                // 2. 如果失败，则使用硬编码的数据作为备份
                
                // 先尝试使用异步方法调用API
                if (window.createCommonTokenAsync) {
                    createCommonTokenAsync({
                        author_key: accessKey,
                        version: '2022-05-01',
                        user_id: '420568',
                    }).then(token => {
                        console.log("成功生成token");
                        
                        // 使用fetch API调用OneNET
                        return fetch('https://iot-api.heclouds.com/thingmodel/query-device-property?product_id=HTJ98Pjh4a&device_name=flotation', {
                            method: 'GET',
                            headers: {
                                'authorization': token,
                                'Content-Type': 'application/json'
                            }
                        });
                    })
                    .then(response => {
                        if (!response.ok) {
                            throw new Error(`HTTP error! status: ${response.status}`);
                        }
                        return response.json();
                    })
                    .then(data => {
                        processDeviceData(data);
                    })
                    .catch(error => {
                        console.error('API调用失败，使用备份数据:', error);
                        // 如果API调用失败，使用备份的静态数据
                        useBackupData();
                    });
                } else {
                    // 如果异步API不可用，直接使用备份数据
                    console.warn('异步token生成不可用，使用备份数据');
                    useBackupData();
                }
            } catch (error) {
                showLoadingState(false);
                console.error('数据处理错误:', error);
                deviceData.connectionStatus = false;
                updateDeviceListWithError(`处理数据时出错: ${error.message}`);
                showApiStatus(false);
            }
        }
        
        // 处理从API获取的设备数据
        function processDeviceData(data) {
            // 首先调用showLoadingState(false)来隐藏加载提示
            showLoadingState(false);
            
            if (data && data.data) {
                console.log('成功获取设备数据:', data);
                
                // 根据返回的数据结构，设置设备状态
                const deviceMap = {};
                
                // 将API返回的数据映射到各个设备
                // 注意：API返回的标识符可能与我们的字段名称大小写不同
                data.data.forEach(item => {
                    if (item.identifier) {
                        // 将标识符转为小写存储，便于后续不区分大小写匹配
                        deviceMap[item.identifier.toLowerCase()] = item.value;
                    }
                });
                
                console.log('处理后的设备数据映射:', deviceMap);
                
                // 更新设备数据 - 使用正确的标识符名称
                deviceData.ph = deviceMap.ph || deviceMap.ph?.toString() || "7.1";  // PH传感器
                deviceData.Water = deviceMap.water || deviceMap.water?.toString() || "51.5"; // 水位传感器
                
                // 泵设备状态更新
                deviceData.Pump1 = deviceMap.pump1 === "true"; // 泵1
                deviceData.Pump2 = deviceMap.pump2 === "true"; // 泵2
                deviceData.Pump3 = deviceMap.pump3 === "true"; // 泵3
                deviceData.Pump4 = deviceMap.pump4 === "true"; // 泵4
                deviceData.Pump5 = deviceMap.pump5 === "true"; // 泵5
                deviceData.Pump6 = deviceMap.pump6 === "true"; // 泵6
                
                // 检查API返回的实际字段名称
                if (data.data.find(item => item.identifier === "PH")) {
                    deviceData.ph = data.data.find(item => item.identifier === "PH").value;
                }
                
                if (data.data.find(item => item.identifier === "Water")) {
                    deviceData.Water = data.data.find(item => item.identifier === "Water").value;
                }
                
                if (data.data.find(item => item.identifier === "Pump1")) {
                    deviceData.Pump1 = data.data.find(item => item.identifier === "Pump1").value === "true";
                }
                
                if (data.data.find(item => item.identifier === "Pump2")) {
                    deviceData.Pump2 = data.data.find(item => item.identifier === "Pump2").value === "true";
                }
                
                if (data.data.find(item => item.identifier === "Pump3")) {
                    deviceData.Pump3 = data.data.find(item => item.identifier === "Pump3").value === "true";
                }
                
                if (data.data.find(item => item.identifier === "Pump4")) {
                    deviceData.Pump4 = data.data.find(item => item.identifier === "Pump4").value === "true";
                }
                
                if (data.data.find(item => item.identifier === "Pump5")) {
                    deviceData.Pump5 = data.data.find(item => item.identifier === "Pump5").value === "true";
                }
                
                if (data.data.find(item => item.identifier === "Pump6")) {
                    deviceData.Pump6 = data.data.find(item => item.identifier === "Pump6").value === "true";
                }
                
                console.log('更新后的设备数据状态:', {
                    ph: deviceData.ph,
                    Water: deviceData.Water,
                    Pump1: deviceData.Pump1,
                    Pump2: deviceData.Pump2,
                    Pump3: deviceData.Pump3,
                    Pump4: deviceData.Pump4,
                    Pump5: deviceData.Pump5,
                    Pump6: deviceData.Pump6
                });
                
                // 更新设备列表和摘要
                updateDeviceList();
                updateDeviceSummary();
                
                // 连接状态设为true
                deviceData.connectionStatus = true;
                deviceData.lastUpdateTime = new Date();
                
                // 显示API状态
                showApiStatus(true);
            } else {
                console.error('API返回数据格式不正确');
                useBackupData();
            }
        }
        
        // 使用备份数据（基于您提供的图片中的实际数据）
        function useBackupData() {
            showLoadingState(false);
            
            // 使用您提供的图片中显示的数据
            deviceData.ph = "7.1";       // PH值
            deviceData.Water = "51.5";   // 水位
            deviceData.Pump1 = false;    // 泵1
            deviceData.Pump2 = false;    // 泵2
            deviceData.Pump3 = false;    // 泵3
            deviceData.Pump4 = false;    // 泵4
            deviceData.Pump5 = true;     // 泵5
            deviceData.Pump6 = true;     // 泵6
            
            // 更新界面
            updateDeviceList();
            updateDeviceSummary();
            
            // 连接状态设为true（虽然是使用备份数据）
            deviceData.connectionStatus = true;
            deviceData.lastUpdateTime = new Date();
            
            // 显示API状态为降级模式
            showApiStatus(true, true); // 第二个参数表示使用备份数据
            
            console.log('使用备份数据更新界面');
        }
        
        // 显示连接错误信息
        function showConnectionError(message) {
            // 更新设备列表，显示未连接状态
            updateDeviceListWithError(message);
            
            // 显示错误提示
            showToast(message, 'error');
        }
        
        // 在连接失败时更新设备列表
        function updateDeviceListWithError(errorMessage) {
            const allDevices = document.getElementById('allDevices');
            
            // 清空现有设备卡片
            allDevices.innerHTML = '';
            
            // 创建错误提示卡片
            const errorCard = document.createElement('div');
            errorCard.className = 'device-card error animate-slideUp';
            errorCard.style.width = '100%';
            errorCard.style.minWidth = '100%';
            errorCard.style.backgroundColor = 'rgba(244, 67, 54, 0.1)';
            errorCard.style.border = '1px solid rgba(244, 67, 54, 0.3)';
            
            errorCard.innerHTML = `
                <div class="device-details" style="justify-content: center; align-items: center; text-align: center;">
                    <div>
                        <div style="margin-bottom: 10px;">
                            <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 24 24" fill="none" stroke="#f44336" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <circle cx="12" cy="12" r="10"></circle>
                                <line x1="12" y1="8" x2="12" y2="12"></line>
                                <line x1="12" y1="16" x2="12.01" y2="16"></line>
                            </svg>
                        </div>
                        <div class="device-name" style="color: #f44336;">连接失败</div>
                        <div style="font-size: 12px; color: #666; margin-top: 8px;">${errorMessage}</div>
                        <div style="margin-top: 15px;">
                            <button id="retryButton" style="background: #2196f3; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;">
                                重试连接
                            </button>
                        </div>
                    </div>
                </div>
            `;
            
            allDevices.appendChild(errorCard);
            
            // 添加重试按钮事件
            document.getElementById('retryButton').addEventListener('click', () => {
                fetchDeviceData();
            });
            
            // 更新设备摘要
            document.getElementById('onlineDevices').textContent = '0';
            document.getElementById('runningDevices').textContent = '0';
            document.getElementById('totalDevices').textContent = '8';
            document.getElementById('runningDevicesCount').textContent = '0';
            document.getElementById('totalDevicesCount').textContent = '8';
            
            // 设置系统状态指示器为错误
            const systemStatus = document.getElementById('systemStatus');
            const statusDot = systemStatus.querySelector('.status-dot');
            statusDot.classList.remove('warning');
            statusDot.classList.add('error');
        }
        
        // 显示API状态指示器 - 使用新样式的Toast
        function showApiStatus(isSuccess, isBackupData = false) {
            if (isSuccess) {
                if (isBackupData) {
                    // 使用备份数据 - 显示警告提示
                    showToast('⚠ 使用备份数据显示', 'info');
                } else {
                    // API连接成功 - 显示成功提示
                    showToast('✓ API连接正常', 'success');
                }
            } else {
                // API连接失败 - 显示错误提示
                showToast('✗ API连接失败', 'error');
            }
        }
        
        // 显示或隐藏加载状态 - 使用Toast系统来保持一致性
        function showLoadingState(isLoading) {
            // 使用具有ID的toast，便于后续识别和移除
            const loadingToastId = 'loadingStateToast';
            
            // 移除任何现有的加载提示
            const existingToast = document.getElementById(loadingToastId);
            if (existingToast) {
                existingToast.classList.remove('show');
                setTimeout(() => {
                    if (existingToast.parentNode) {
                        existingToast.parentNode.removeChild(existingToast);
                    }
                }, 300);
            }
            
            if (isLoading) {
                // 确保存在toast容器
                let toastContainer = document.querySelector('.toast-container');
                if (!toastContainer) {
                    toastContainer = document.createElement('div');
                    toastContainer.className = 'toast-container';
                    document.body.appendChild(toastContainer);
                }
                
                // 创建加载提示toast
                const loadingToast = document.createElement('div');
                loadingToast.className = 'toast-message info';
                loadingToast.id = loadingToastId;
                
                // 创建加载图标
                const spinnerSvg = `
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="#2196f3" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="loading-svg-icon">
                    <circle cx="12" cy="12" r="10"></circle>
                    <path d="M12 6v2"></path>
                </svg>
                <style>
                    @keyframes loading-rotate {
                        0% { transform: rotate(0deg); }
                        100% { transform: rotate(360deg); }
                    }
                    .loading-svg-icon {
                        animation: loading-rotate 1s linear infinite;
                    }
                </style>
                `;
                
                // 设置内容
                loadingToast.innerHTML = `
                    <div class="toast-icon">${spinnerSvg}</div>
                    <div class="toast-content">正在更新设备数据...</div>
                `;
                
                // 添加到容器
                toastContainer.appendChild(loadingToast);
                
                // 显示动画
                setTimeout(() => {
                    loadingToast.classList.add('show');
                }, 10);
                
                // 不设置自动消失时间，在完成加载后手动移除
            }
        }
        
        // 手动刷新数据
        function manualRefresh() {
            fetchDeviceData();
        }
        
        // 底部导航交互
        function setupBottomNav() {
            const navItems = document.querySelectorAll('.nav-item');
            navItems.forEach((item, index) => {
                item.addEventListener('click', () => {
                    navItems.forEach(i => i.classList.remove('active'));
                    item.classList.add('active');
                    
                    // 如果点击的是"场景"按钮，跳转到历史数据页面
                    if (index === 1) {
                        window.location.href = 'history.html?device=pump1';
                    }
                    // 如果点击的是"通知"按钮，跳转到通知中心页面
                    else if (index === 2) {
                        window.location.href = 'notification.html';
                    }
                    // 如果点击的是"我的"按钮，跳转到个人中心页面
                    else if (index === 3) {
                        window.location.href = 'my.html';
                    }
                });
            });
        }
        
        // 添加刷新按钮
        function addRefreshButton() {
            const section = document.querySelector('.section-title');
            const refreshButton = document.createElement('div');
            refreshButton.style.cursor = 'pointer';
            refreshButton.style.display = 'flex';
            refreshButton.style.alignItems = 'center';
            refreshButton.style.fontSize = '14px';
            refreshButton.style.color = '#2196f3';
            refreshButton.innerHTML = `
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" style="margin-right: 4px;">
                    <path d="M23 4v6h-6"></path>
                    <path d="M1 20v-6h6"></path>
                    <path d="M3.51 9a9 9 0 0 1 14.85-3.36L23 10"></path>
                    <path d="M1 14l4.64 4.36A9 9 0 0 0 20.49 15"></path>
                </svg>
                刷新
            `;
            refreshButton.addEventListener('click', manualRefresh);
            
            // 添加到section标题右侧
            section.appendChild(refreshButton);
        }
        
        // 页面加载时执行
        document.addEventListener('DOMContentLoaded', () => {
            updateDate();
            fetchChengduWeather();
            fetchDeviceData();
            setupOperationModes();
            setupBottomNav();
            updateDeviceSummary();
            addRefreshButton();
            
            // 定时刷新数据
            setInterval(() => {
                fetchDeviceData();
            }, 10000); // 每10秒刷新一次设备数据
            
            setInterval(() => {
                fetchChengduWeather();
            }, 60000); // 每分钟刷新一次天气数据
        });
    </script>
</body>
</html> 