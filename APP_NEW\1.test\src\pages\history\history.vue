<template>
  <view class="container">
    <!-- 顶部导航栏 -->
    <view class="top-nav">
      <view class="back-button" @click="goBack">
        <text class="iconfont icon-back">
          <view class="back-icon"></view>
        </text>
      </view>
      <view class="page-title">设备历史数据</view>
      <view class="share-button" @click="shareData">
        <text class="iconfont icon-share"></text>
      </view>
    </view>
    
    <!-- 设备选择滚轮 -->
    <view class="device-selector-container">
      <scroll-view class="device-selector" scroll-x="true" show-scrollbar="false">
        <view 
          v-for="(device, index) in allDevices" 
          :key="device.id"
          :class="['device-option', { active: deviceData.id === device.id }]"
          @click="changeDevice(device.id)"
        >
          <view class="device-option-icon">
            <text class="iconfont" :class="'icon-' + device.icon"></text>
          </view>
          <text>{{ device.name }}</text>
        </view>
      </scroll-view>
      <view class="scroll-indicator">
        <view 
          v-for="(_, index) in Math.ceil(allDevices.length / 3)" 
          :key="index"
          :class="['indicator-dot', { active: currentIndicatorIndex === index }]"
        ></view>
      </view>
    </view>
    
    <!-- 设备信息卡片 -->
    <view class="device-info-card">
      <view class="device-icon-large">
        <text class="iconfont" :class="'icon-' + deviceData.icon"></text>
      </view>
      <view class="device-details-large">
        <view class="device-name-large">{{ deviceData.name }}</view>
        <view 
          :class="['device-status-large', 
            deviceData.type === 'sensor' ? 'sensor-value' : 
            deviceData.status === '运行中' ? 'running' : 'stopped']"
        >
          {{ deviceData.status }}
        </view>
      </view>
    </view>
    
    <!-- 日期范围选择器 -->
    <view class="date-range-selector">
      <view class="date-range-input">
        <picker 
          mode="date" 
          :value="dateRange.start" 
          @change="onStartDateChange"
          class="date-picker"
        >
          <view class="picker-text">{{ dateRange.start }}</view>
        </picker>
        <text class="date-separator">至</text>
        <picker 
          mode="date" 
          :value="dateRange.end" 
          @change="onEndDateChange"
          class="date-picker"
        >
          <view class="picker-text">{{ dateRange.end }}</view>
        </picker>
        <button class="search-btn" @click="searchData">查询</button>
      </view>
      <view class="quick-date-options">
        <view 
          v-for="(option, index) in quickOptions" 
          :key="index"
          :class="['quick-option', { active: currentPeriod === option.value }]"
          @click="selectQuickOption(option.value)"
        >
          {{ option.label }}
        </view>
      </view>
    </view>
    
    <!-- 历史内容区域 -->
    <scroll-view class="history-content" scroll-y="true">
      <!-- 统计卡片 -->
      <view class="data-section">
        <view class="section-header">
          <view class="section-title">数据统计</view>
          <view @click="refreshStats" class="refresh-stats">
            <text class="iconfont icon-refresh"></text>
            <text>刷新</text>
          </view>
        </view>
        
        <view class="stat-cards">
          <view class="stat-card up">
            <view class="stat-info">
              <view class="stat-label">{{ statCards[0].label }}</view>
              <view class="stat-value">{{ statCards[0].value }}</view>
            </view>
            <view class="stat-icon">
              <text class="iconfont icon-trending-up"></text>
            </view>
          </view>
          
          <view class="stat-card down">
            <view class="stat-info">
              <view class="stat-label">{{ statCards[1].label }}</view>
              <view class="stat-value">{{ statCards[1].value }}</view>
            </view>
            <view class="stat-icon">
              <text class="iconfont icon-trending-down"></text>
            </view>
          </view>
          
          <view class="stat-card neutral">
            <view class="stat-info">
              <view class="stat-label">{{ statCards[2].label }}</view>
              <view class="stat-value">{{ statCards[2].value }}</view>
            </view>
            <view class="stat-icon">
              <text class="iconfont icon-chart"></text>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 布尔型设备状态历史 -->
      <view id="booleanHistorySection" class="data-section">
        <view class="section-header">
          <view class="section-title">{{ deviceData.name }}{{ deviceData.type === 'pump' ? '运行状态历史' : '测量值状态历史' }}</view>
          <view class="time-info">{{ periodText }}</view>
        </view>
        
        <view :class="['boolean-matrix', deviceData.type === 'pump' ? 'device-pump' : 'device-sensor']" id="statusMatrix">
          <!-- 状态矩阵将通过JS动态生成 -->
          <view 
            v-for="(cell, index) in matrixCells" 
            :key="index"
            :class="['matrix-cell', { 
              active: cell.value === true, 
              inactive: cell.value === false,
              'no-data': cell.value === null
            }]"
            @click="showCellDetails(cell)"
          >
            {{ cell.label }}
          </view>
        </view>
        
        <!-- 运行状态图例 -->
        <view class="matrix-legend">
          <view class="legend-item">
            <view class="legend-color active"></view>
            <view class="legend-text">{{ deviceData.type === 'pump' ? '运行' : '正常' }}</view>
          </view>
          <view class="legend-item">
            <view class="legend-color inactive"></view>
            <view class="legend-text">{{ deviceData.type === 'pump' ? '停止' : '异常' }}</view>
          </view>
        </view>
      </view>
      
      <!-- 数值型设备数据历史 -->
      <view v-if="deviceData.type === 'sensor'" id="numericHistorySection" class="data-section">
        <view class="section-header">
          <view class="section-title">数据历史记录</view>
          <view class="view-toggle">
            <view 
              v-for="(view, index) in chartViews" 
              :key="index"
              :class="['toggle-option', { active: currentView === view.value }]"
              @click="changeView(view.value)"
            >
              {{ view.label }}
            </view>
          </view>
        </view>
        
        <view class="chart-container">
          <canvas canvas-id="dataChart" id="dataChart" class="chart-canvas"></canvas>
        </view>
        
        <!-- 数据分析概要 -->
        <view class="data-summary">
          <view class="summary-row">
            <view class="summary-label">平均值:</view>
            <view class="summary-value">{{ statistics.avg }}</view>
          </view>
          <view class="summary-row">
            <view class="summary-label">最大值:</view>
            <view class="summary-value">{{ statistics.max }}</view>
          </view>
          <view class="summary-row">
            <view class="summary-label">最小值:</view>
            <view class="summary-value">{{ statistics.min }}</view>
          </view>
          <view class="summary-row">
            <view class="summary-label">标准差:</view>
            <view class="summary-value">{{ statistics.std }}</view>
          </view>
        </view>
      </view>
      
      <!-- 预测趋势 -->
      <view v-if="deviceData.type === 'sensor'" id="predictionSection" class="data-section">
        <view class="section-header">
          <view class="section-title">预测分析</view>
          <view class="prediction-badge">AI分析</view>
        </view>
        
        <view class="prediction-section">
          <view class="prediction-header">
            <view class="prediction-title">
              <view class="prediction-icon">
                <text class="iconfont icon-prediction"></text>
              </view>
              未来7天趋势预测
            </view>
            <view class="prediction-accuracy">准确度: <text>{{ predictionAccuracy }}</text></view>
          </view>
          
          <view class="chart-container">
            <canvas canvas-id="predictionChart" id="predictionChart" class="chart-canvas"></canvas>
          </view>
          
          <!-- 预测分析结果 -->
          <view class="prediction-insight">
            <view class="insight-icon">
              <text class="iconfont icon-info"></text>
            </view>
            <view class="insight-text" id="predictionInsight">
              {{ predictionInsight }}
            </view>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script>
import { createCommonToken } from '@/key.js';
import uCharts from '@/components/u-charts/u-charts.js'

var uChartsInstance = {};

export default {
  data() {
    return {
      deviceData: {
        id: 'pump1',
        name: '捕收剂阳',
        status: '运行中',
        icon: 'gear',
        type: 'pump'
      },
      currentPeriod: '24h',
      currentView: 'line',
      currentIndicatorIndex: 0,
      allDevices: [
        {
          id: 'pump1',
          name: '捕收剂阳',
          status: '运行中',
          icon: 'gear',
          type: 'pump'
        },
        {
          id: 'pump2',
          name: '捕收剂阴',
          status: '已停止',
          icon: 'settings',
          type: 'pump'
        },
        {
          id: 'pump3',
          name: '起泡剂',
          status: '运行中',
          icon: 'plus',
          type: 'pump'
        },
        {
          id: 'pump4',
          name: '活化剂',
          status: '已停止',
          icon: 'star',
          type: 'pump'
        },
        {
          id: 'pump5',
          name: '抑制剂',
          status: '运行中',
          icon: 'minus',
          type: 'pump'
        },
        {
          id: 'pump6',
          name: '调整剂',
          status: '已停止',
          icon: 'refresh',
          type: 'pump'
        },
        {
          id: 'ph',
          name: 'PH传感器',
          status: '7.2',
          icon: 'info',
          type: 'sensor'
        },
        {
          id: 'water',
          name: '水位传感器',
          status: '80%',
          icon: 'more',
          type: 'sensor'
        }
      ],
      quickOptions: [
        { label: '最近1小时', value: '1h' },
        { label: '最近24小时', value: '24h' },
        { label: '最近7天', value: '7d' }
      ],
      chartViews: [
        { label: '折线图', value: 'line' },
        { label: '柱状图', value: 'bar' }
      ],
      dateRange: {
        start: this.formatDate(new Date(Date.now() - 24 * 60 * 60 * 1000)),
        end: this.formatDate(new Date())
      },
      statistics: {
        avg: '-',
        max: '-',
        min: '-',
        std: '-'
      },
      statCards: [
        { label: '平均运行时间', value: '16.4小时/天' },
        { label: '故障频率', value: '2.3次/月' },
        { label: '能效评分', value: '93/100' }
      ],
      matrixCells: [],
      historicalData: [],
      predictionAccuracy: '95%',
      predictionInsight: '根据历史数据分析，预计该设备在未来7天内将保持稳定运行状态，趋势略有上升。建议定期检查，确保设备性能最优。',
      token: '',
      chartInstance: null,
      predictionChartInstance: null,
      animationEnabled: true
    }
  },
  computed: {
    periodText() {
      switch(this.currentPeriod) {
        case '1h': return '最近1小时';
        case '3d': return '最近3天';
        case '5d': return '最近5天';
        case '7d': return '最近7天';
        case '15d': return '最近15天';
        case '30d': return '最近30天';
        default: return '最近24小时'; // 24h
      }
    }
  },
  onLoad(options) {
    // 从URL参数获取设备ID
    if (options.device) {
      this.changeDevice(options.device);
    }
    
    // 创建token
    const params = {
      author_key: 'xaVmoFXwf9oB4QpVN8Vt8sL4hqhLoIyRp31g2j0gQKEt0VG5XEFbpYGvQst14YPX',
      version: '2022-05-01',
      user_id: '420568',
    };
    this.token = createCommonToken(params);
    
    // 初始化状态矩阵
    this.initStatusMatrix();
    
    // 如果是传感器设备，加载图表
    if (this.deviceData.type === 'sensor') {
      this.initDataChart();
      this.initPredictionChart();
    }
    
    // 更新设备统计数据
    this.updateDeviceStatistics();
  },
  onReady() {
    // 图表初始化需要在页面加载完成后进行
    if (this.deviceData.type === 'sensor') {
      this.initDataChart();
      this.initPredictionChart();
    }
    
    // 添加滚动监听以实现动画效果
    const query = uni.createSelectorQuery().in(this);
    query.selectViewport().scrollOffset((res) => {
      this.handleScroll(res.scrollTop);
    });
  },
  methods: {
    goBack() {
      if (getCurrentPages().length > 1) {
        uni.navigateBack();
      } else {
        uni.switchTab({ url: '/pages/home/<USER>' });
      }
    },
    shareData() {
      uni.showToast({
        title: '分享功能开发中',
        icon: 'none'
      });
    },
    changeDevice(deviceId) {
      // 查找设备数据
      const device = this.allDevices.find(d => d.id === deviceId);
      if (!device) return;
      
      this.deviceData = { ...device };
      
      // 更新状态矩阵
      this.initStatusMatrix();
      
      // 如果是传感器设备，更新图表
      if (this.deviceData.type === 'sensor') {
        this.initDataChart();
        this.initPredictionChart();
      }
      
      // 更新设备统计数据
      this.updateDeviceStatistics();
    },
    selectQuickOption(period) {
      this.currentPeriod = period;
      
      // 更新日期范围
      const now = new Date();
      let start;
      
      switch(period) {
        case '1h':
          start = new Date(now.getTime() - 1 * 60 * 60 * 1000);
          break;
        case '7d':
          start = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
          break;
        default: // 24h
          start = new Date(now.getTime() - 24 * 60 * 60 * 1000);
      }
      
      this.dateRange = {
        start: this.formatDate(start),
        end: this.formatDate(now)
      };
      
      // 更新数据
      this.initStatusMatrix();
      if (this.deviceData.type === 'sensor') {
        this.initDataChart();
      }
    },
    searchData() {
      uni.showLoading({ title: '查询数据中...' });
      
      // 更新状态矩阵和图表
      this.initStatusMatrix();
      if (this.deviceData.type === 'sensor') {
        this.initDataChart();
      }
      
      setTimeout(() => {
        uni.hideLoading();
        uni.showToast({ 
          title: '数据更新完成', 
          icon: 'success' 
        });
      }, 1000);
    },
    changeView(view) {
      this.currentView = view;
      this.initDataChart();
    },
    formatDate(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    },
    onStartDateChange(e) {
      this.dateRange.start = e.detail.value;
    },
    onEndDateChange(e) {
      this.dateRange.end = e.detail.value;
    },
    refreshStats() {
      uni.showLoading({ title: '刷新数据中...' });
      
      // 更新设备统计数据
      this.updateDeviceStatistics();
      
      setTimeout(() => {
        uni.hideLoading();
        uni.showToast({ 
          title: '统计数据已更新', 
          icon: 'success' 
        });
      }, 800);
    },
    showCellDetails(cell) {
      if (cell.value === null) return;
      
      uni.showToast({
        title: `${cell.timeLabel}: ${cell.value ? '运行中' : '已停止'}`,
        icon: 'none'
      });
    },
    updateDeviceStatistics() {
      // 根据设备类型设置不同的统计数据
      const isPumpDevice = !['ph', 'water'].includes(this.deviceData.id);
      
      if (isPumpDevice) {
        // 泵设备的统计数据
        const runHours = (Math.random() * 10 + 10).toFixed(1);
        const faultFreq = (Math.random() * 4).toFixed(1);
        const effScore = Math.floor(Math.random() * 20 + 80);
        
        this.statCards = [
          { label: '平均运行时间', value: `${runHours}小时/天` },
          { label: '故障频率', value: `${faultFreq}次/月` },
          { label: '能效评分', value: `${effScore}/100` }
        ];
      } else if (this.deviceData.id === 'ph') {
        // PH传感器的统计数据
        const avgPh = (Math.random() * 1.5 + 6.5).toFixed(1);
        const stability = (Math.random() * 30 + 70).toFixed(0);
        const alarmRate = (Math.random() * 10).toFixed(1);
        
        this.statCards = [
          { label: '平均PH值', value: avgPh },
          { label: '数据稳定性', value: `${stability}%` },
          { label: '异常率', value: `${alarmRate}%` }
        ];
      } else if (this.deviceData.id === 'water') {
        // 水位传感器的统计数据
        const avgLevel = (Math.random() * 20 + 70).toFixed(1);
        const fluctuation = (Math.random() * 20 + 5).toFixed(1);
        const warningCount = Math.floor(Math.random() * 10);
        
        this.statCards = [
          { label: '平均水位', value: `${avgLevel}%` },
          { label: '波动范围', value: `±${fluctuation}%` },
          { label: '警告次数', value: `${warningCount}次` }
        ];
      }
    },
    initStatusMatrix() {
      const totalCells = this.currentPeriod === '24h' ? 24 : 
                         this.currentPeriod === '7d' ? 28 : 24;
      
      // 生成模拟数据
      this.matrixCells = [];
      
      for (let i = 0; i < totalCells; i++) {
        const now = new Date();
        let cellTime;
        
        if (this.currentPeriod === '24h') {
          // 每小时一个单元格
          cellTime = new Date(now.getTime() - (totalCells - i - 1) * 60 * 60 * 1000);
        } else if (this.currentPeriod === '7d') {
          // 每6小时一个单元格
          cellTime = new Date(now.getTime() - (totalCells - i - 1) * 6 * 60 * 60 * 1000);
        }
        
        // 生成随机值
        let value;
        if (['pump1', 'pump2', 'pump3', 'pump4', 'pump5', 'pump6'].includes(this.deviceData.id)) {
          // 泵设备，70%概率为运行状态
          value = Math.random() < 0.7;
        } else if (this.deviceData.id === 'ph') {
          // PH传感器，80%概率为正常范围
          value = Math.random() < 0.8;
        } else if (this.deviceData.id === 'water') {
          // 水位传感器，90%概率为正常范围
          value = Math.random() < 0.9;
        } else {
          value = Math.random() < 0.5;
        }
        
        // 5%概率为无数据
        if (Math.random() < 0.05) {
          value = null;
        }
        
        // 时间标签
        const hour = cellTime.getHours();
        const timeLabel = this.currentPeriod === '24h' ? 
          `${hour}:00` : 
          `${this.formatDate(cellTime)} ${hour}:00`;
        
        this.matrixCells.push({
          value,
          label: this.currentPeriod === '24h' ? `${hour}` : `${hour}:00`,
          timeLabel
        });
      }
      
      // 在实际应用中，这里应该调用API获取真实历史数据
      this.fetchHistoricalData();
    },
    fetchHistoricalData() {
      // 在实际应用中，这里应该调用API获取真实历史数据
      // 为示例，这里只使用模拟数据
      
      // 可以添加真实API调用
      const now = new Date();
      let startTime;
      
      switch(this.currentPeriod) {
        case '1h':
          startTime = new Date(now.getTime() - 1 * 60 * 60 * 1000);
          break;
        case '7d':
          startTime = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
          break;
        default: // 24h
          startTime = new Date(now.getTime() - 24 * 60 * 60 * 1000);
      }
      
      // 这里可以添加实际的API调用
      // 示例：
      /*
      uni.request({
        url: 'https://iot-api.heclouds.com/datapoint/history-datapoints',
        method: 'GET',
        data: {
          product_id: 'HTJ98Pjh4a',
          device_name: 'flotation',
          datastream_id: this.deviceData.id,
          start: this.formatDate(startTime),
          end: this.formatDate(now),
          limit: '1000'
        },
        header: { authorization: this.token },
        success: (res) => {
          // 处理返回数据
          if (res.data && res.data.code === 0) {
            // 更新矩阵和图表
          }
        }
      });
      */
    },
    initDataChart() {
      // 在这里集成图表库，uCharts或ECharts
      const cWidth = uni.upx2px(690);
      const cHeight = uni.upx2px(500);
      this.chartData = this.generateMockChartData();
      
      uChartsInstance.data = uCharts.showchart({
        $this: this,
        canvasId: 'dataChart',
        type: this.currentView,
        dataIndex: 0,
        opts: {
          animation: true,
          timing: 'easeOut',
          duration: 1000,
          width: cWidth,
          height: cHeight,
          background: '#FFFFFF',
          padding: [15, 15, 15, 15],
          enableScroll: false,
          legend: {},
          xAxis: {
            disableGrid: true
          },
          yAxis: {
            data: [
              {
                position: 'left',
                title: this.deviceData.id === 'ph' ? 'pH值' : '水位(%)'
              }
            ]
          },
          extra: {
            line: {
              type: 'curve',
              width: 2
            }
          }
        },
        chartData: this.chartData
      });
    },
    
    // 初始化预测图表
    initPredictionChart() {
      // 在这里集成预测图表
      const cWidth = uni.upx2px(690);
      const cHeight = uni.upx2px(500);
      this.predictionData = this.generateMockPredictionData();
      
      uChartsInstance.prediction = uCharts.showchart({
        $this: this,
        canvasId: 'predictionChart',
        type: 'line',
        dataIndex: 1,
        opts: {
          animation: true,
          timing: 'easeOut',
          duration: 1000,
          width: cWidth,
          height: cHeight,
          background: '#FFFFFF',
          padding: [15, 15, 15, 15],
          enableScroll: false,
          legend: {},
          xAxis: {
            disableGrid: true
          },
          yAxis: {
            data: [
              {
                position: 'left',
                title: this.deviceData.id === 'ph' ? 'pH值' : '水位(%)'
              }
            ]
          },
          extra: {
            line: {
              type: 'curve',
              width: 2
            }
          }
        },
        chartData: this.predictionData
      });
    },
    
    // 生成模拟图表数据
    generateMockChartData() {
      const categories = [];
      const series = [];
      
      // 生成模拟数据点
      const now = new Date();
      const dataPoints = 24; // 24小时
      const values = [];
      
      for (let i = dataPoints - 1; i >= 0; i--) {
        const time = new Date(now.getTime() - i * 60 * 60 * 1000);
        categories.push(`${time.getHours()}:00`);
        
        // 生成随机数据
        let value;
        if (this.deviceData.id === 'ph') {
          value = (6.8 + Math.random() * 0.8).toFixed(1); // pH值在6.8-7.6之间
        } else {
          value = Math.floor(70 + Math.random() * 20); // 水位在70%-90%之间
        }
        values.push(value);
      }
      
      series.push({
        name: this.deviceData.name,
        data: values,
        color: '#1976D2'
      });
      
      return {
        categories: categories,
        series: series
      };
    },
    
    // 生成模拟预测数据
    generateMockPredictionData() {
      const categories = [];
      const series = [];
      
      // 生成历史+预测数据
      const now = new Date();
      const historyDays = 7; // 过去7天
      const futureDays = 7; // 未来7天
      const historyValues = [];
      const futureValues = [];
      
      // 历史数据
      for (let i = historyDays - 1; i >= 0; i--) {
        const date = new Date(now.getTime() - i * 24 * 60 * 60 * 1000);
        categories.push(`${date.getMonth()+1}/${date.getDate()}`);
        
        // 生成随机数据
        let value;
        if (this.deviceData.id === 'ph') {
          value = (6.8 + Math.random() * 0.8).toFixed(1);
        } else {
          value = Math.floor(70 + Math.random() * 20);
        }
        historyValues.push(value);
      }
      
      // 预测数据 - 假设有轻微上升趋势
      let lastValue = historyValues[historyValues.length - 1];
      for (let i = 1; i <= futureDays; i++) {
        const date = new Date(now.getTime() + i * 24 * 60 * 60 * 1000);
        categories.push(`${date.getMonth()+1}/${date.getDate()}`);
        
        // 预测值有轻微上升趋势
        if (this.deviceData.id === 'ph') {
          lastValue = (parseFloat(lastValue) + (Math.random() * 0.2 - 0.05)).toFixed(1);
          if (lastValue > 8) lastValue = 8;
          if (lastValue < 6.5) lastValue = 6.5;
        } else {
          lastValue = parseInt(lastValue) + (Math.random() * 4 - 1);
          if (lastValue > 95) lastValue = 95;
          if (lastValue < 65) lastValue = 65;
        }
        futureValues.push(lastValue);
      }
      
      series.push({
        name: '历史数据',
        data: historyValues,
        color: '#2196f3'
      });
      
      series.push({
        name: '预测趋势',
        data: [...Array(historyDays).fill(null), ...futureValues],
        color: '#ff9800',
        lineType: 'dash'
      });
      
      return {
        categories: categories,
        series: series
      };
    },
    
    // 处理页面滚动，触发动画
    handleScroll(scrollTop) {
      if (!this.animationEnabled) return;
      
      // 添加滚动时的动画效果
      // 比如可以检测各部分是否进入视口，然后触发动画
      // 这里简单示例
      if (scrollTop > 300 && !this.statCardsAnimated) {
        this.statCardsAnimated = true;
        // 可以触发统计卡片的动画
      }
    }
  }
}
</script>

<style>
.container {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100vh;
  background: linear-gradient(120deg, #e0f7fa 0%, #f5f5f5 100%);
}

/* 顶部导航 */
.top-nav {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background-color: white;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
  z-index: 10;
}

.back-button {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #f5f5f5;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}

.page-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.share-button {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #f5f5f5;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}

/* 设备选择器 */
.device-selector-container {
  padding: 10px 0;
  background-color: white;
  border-bottom: 1px solid #f0f0f0;
  position: relative;
  z-index: 5;
}

.device-selector {
  display: flex;
  white-space: nowrap;
  overflow-x: auto;
  padding: 0 15px;
  scroll-snap-type: x mandatory;
  -webkit-overflow-scrolling: touch;
}

.device-selector::-webkit-scrollbar {
  display: none;
}

.device-option {
  display: inline-flex;
  flex-direction: column;
  align-items: center;
  padding: 12px 18px;
  margin: 0 5px;
  border-radius: 12px;
  scroll-snap-align: start;
  cursor: pointer;
  transition: all 0.2s ease;
}

.device-option.active {
  background-color: #e3f2fd;
  color: #1976d2;
}

.device-option-icon {
  margin-bottom: 8px;
  font-size: 20px;
}

.scroll-indicator {
  display: flex;
  justify-content: center;
  margin-top: 10px;
}

.indicator-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: #ddd;
  margin: 0 4px;
}

.indicator-dot.active {
  background-color: #1976d2;
}

/* 设备信息卡片 */
.device-info-card {
  margin: 15px;
  padding: 20px;
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
  display: flex;
  align-items: center;
}

.device-icon-large {
  width: 60px;
  height: 60px;
  border-radius: 16px;
  background-color: #f5f5f5;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 15px;
  font-size: 30px;
}

.device-details-large {
  flex: 1;
}

.device-name-large {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 8px;
}

.device-status-large {
  display: inline-block;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 14px;
}

.device-status-large.running {
  background-color: rgba(76, 175, 80, 0.1);
  color: #4CAF50;
  border: 1px solid rgba(76, 175, 80, 0.2);
}

.device-status-large.stopped {
  background-color: rgba(244, 67, 54, 0.1);
  color: #F44336;
  border: 1px solid rgba(244, 67, 54, 0.2);
}

.device-status-large.sensor-value {
  background-color: rgba(33, 150, 243, 0.1);
  color: #2196F3;
  border: 1px solid rgba(33, 150, 243, 0.2);
}

/* 日期范围选择器 */
.date-range-selector {
  margin: 15px;
  padding: 15px;
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.date-range-input {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  margin-bottom: 15px;
}

.date-picker {
  flex: 1;
  min-width: 0; /* 防止flex子项溢出 */
  height: 40px;
  background-color: #f5f5f5;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 10px;
  margin: 0 5px;
}

.picker-text {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  text-align: center;
  width: 100%;
  font-size: 14px;
}

.date-separator {
  margin: 0 5px;
  color: #999;
  font-size: 14px;
}

.search-btn {
  height: 40px;
  background-color: #1976d2;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 10px;
  width: 100%;
}

.quick-date-options {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-around;
  gap: 8px;
  margin-top: 10px;
}

.quick-option {
  flex: 1;
  padding: 8px 5px;
  background-color: #f5f5f5;
  border-radius: 20px;
  font-size: 13px;
  color: #666;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: center;
  min-width: 80px;
}

.quick-option.active {
  background-color: #e3f2fd;
  color: #1976d2;
}

/* 历史内容区域 */
.history-content {
  flex: 1;
  overflow-y: auto;
  padding: 0 15px 20px;
}

.history-content::-webkit-scrollbar {
  display: none;
}

/* 数据部分 */
.data-section {
  background-color: white;
  border-radius: 12px;
  padding: 15px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  flex-wrap: wrap;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 5px;
}

.time-info {
  font-size: 12px;
  color: #999;
}

.refresh-stats {
  display: flex;
  align-items: center;
  font-size: 13px;
  color: #1976d2;
  cursor: pointer;
}

.refresh-stats .iconfont {
  margin-right: 5px;
}

/* 统计卡片 */
.stat-cards {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.stat-card {
  flex: 1 1 100%;
  min-width: calc(33% - 10px);
  background-color: #f5f5f5;
  border-radius: 12px;
  padding: 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.stat-card.up {
  background-color: rgba(76, 175, 80, 0.1);
}

.stat-card.down {
  background-color: rgba(244, 67, 54, 0.1);
}

.stat-card.neutral {
  background-color: rgba(33, 150, 243, 0.1);
}

.stat-info {
  display: flex;
  flex-direction: column;
}

.stat-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 5px;
}

.stat-value {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.stat-icon {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  background-color: white;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 20px;
}

.stat-card.up .stat-icon {
  color: #4CAF50;
}

.stat-card.down .stat-icon {
  color: #F44336;
}

.stat-card.neutral .stat-icon {
  color: #2196F3;
}

/* 布尔型历史 */
.boolean-matrix {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(28px, 1fr));
  gap: 5px;
  margin-bottom: 15px;
}

.matrix-cell {
  width: 28px;
  height: 28px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  color: #fff;
  cursor: pointer;
}

.matrix-cell.active {
  background-color: #4CAF50;
}

.matrix-cell.inactive {
  background-color: #F44336;
}

.matrix-cell.no-data {
  background-color: #ddd;
  color: #999;
}

.matrix-legend {
  display: flex;
  gap: 15px;
}

.legend-item {
  display: flex;
  align-items: center;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 4px;
  margin-right: 5px;
}

.legend-color.active {
  background-color: #4CAF50;
}

.legend-color.inactive {
  background-color: #F44336;
}

.legend-text {
  font-size: 12px;
  color: #666;
}

/* 图表容器 */
.chart-container {
  height: 200px;
  margin-bottom: 15px;
  width: 100%;
}

.chart-canvas {
  width: 100%;
  height: 100%;
}

/* 视图切换 */
.view-toggle {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.toggle-option {
  padding: 4px 10px;
  background-color: #f5f5f5;
  border-radius: 12px;
  font-size: 12px;
  color: #666;
  cursor: pointer;
  margin-top: 5px;
}

.toggle-option.active {
  background-color: #e3f2fd;
  color: #1976d2;
}

/* 数据分析概要 */
.data-summary {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
  gap: 10px;
}

.summary-row {
  background-color: #f5f5f5;
  border-radius: 8px;
  padding: 10px;
  display: flex;
  justify-content: space-between;
}

.summary-label {
  font-size: 13px;
  color: #666;
}

.summary-value {
  font-size: 13px;
  font-weight: 600;
  color: #333;
}

/* 预测分析 */
.prediction-section {
  padding: 10px 0;
}

.prediction-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  flex-wrap: wrap;
}

.prediction-title {
  display: flex;
  align-items: center;
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.prediction-icon {
  margin-right: 5px;
  color: #673AB7;
}

.prediction-badge {
  padding: 4px 8px;
  background-color: #673AB7;
  color: white;
  font-size: 11px;
  border-radius: 12px;
}

.prediction-accuracy {
  font-size: 12px;
  color: #666;
  margin-top: 5px;
}

.prediction-accuracy text {
  color: #4CAF50;
  font-weight: 600;
}

.prediction-insight {
  background-color: #f5f5f5;
  border-radius: 8px;
  padding: 15px;
  display: flex;
  margin-top: 15px;
}

.insight-icon {
  margin-right: 10px;
  color: #2196F3;
  font-size: 18px;
  flex-shrink: 0;
}

.insight-text {
  font-size: 13px;
  color: #666;
  line-height: 1.5;
}

/* 移动端适配 */
@media screen and (min-width: 500px) {
  .date-range-input {
    flex-wrap: nowrap;
  }
  
  .search-btn {
    width: auto;
    margin-left: 10px;
    margin-top: 0;
    min-width: 80px;
  }
  
  .stat-card {
    flex: 1 1 calc(33.33% - 10px);
  }
}
</style>