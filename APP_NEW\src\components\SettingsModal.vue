<template>
  <view v-if="visible" class="modal-overlay" @tap="closeModal">
    <view class="modal-container" @tap.stop :class="animationClass">
      <view class="modal-header">
        <view class="modal-icon" :class="iconClass">
          <text class="icon-text">{{ iconText }}</text>
        </view>
        <text class="modal-title">{{ title }}</text>
        <view class="modal-close" @tap="closeModal">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <line x1="18" y1="6" x2="6" y2="18"></line>
            <line x1="6" y1="6" x2="18" y2="18"></line>
          </svg>
        </view>
      </view>
      
      <view class="modal-content">
        <slot></slot>
      </view>
      
      <view class="modal-footer" v-if="showFooter">
        <view class="modal-button cancel" @tap="onCancel" v-if="showCancel">
          <text class="button-text">{{ cancelText }}</text>
        </view>
        <view class="modal-button confirm" @tap="onConfirm">
          <text class="button-text">{{ confirmText }}</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { ref, watch } from 'vue'

export default {
  name: 'SettingsModal',
  props: {
    modelValue: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: '设置'
    },
    iconText: {
      type: String,
      default: '⚙️'
    },
    iconClass: {
      type: String,
      default: 'default'
    },
    showFooter: {
      type: Boolean,
      default: true
    },
    showCancel: {
      type: Boolean,
      default: true
    },
    cancelText: {
      type: String,
      default: '取消'
    },
    confirmText: {
      type: String,
      default: '确定'
    }
  },
  emits: ['update:modelValue', 'confirm', 'cancel', 'close'],
  setup(props, { emit }) {
    const visible = ref(false)
    const animationClass = ref('')
    
    watch(() => props.modelValue, (newVal) => {
      if (newVal) {
        show()
      } else {
        hide()
      }
    }, { immediate: true })
    
    const show = () => {
      visible.value = true
      animationClass.value = 'modal-enter'
      // 触发震动反馈
      uni.vibrateShort()
    }
    
    const hide = () => {
      animationClass.value = 'modal-leave'
      setTimeout(() => {
        visible.value = false
        animationClass.value = ''
      }, 300)
    }
    
    const closeModal = () => {
      emit('update:modelValue', false)
      emit('close')
    }
    
    const onConfirm = () => {
      emit('confirm')
      closeModal()
    }
    
    const onCancel = () => {
      emit('cancel')
      closeModal()
    }
    
    return {
      visible,
      animationClass,
      closeModal,
      onConfirm,
      onCancel
    }
  }
}
</script>

<style scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  z-index: 10001;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  animation: fadeIn 0.3s ease;
  box-sizing: border-box;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.modal-container {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.9) 100%);
  border-radius: 24px;
  max-width: 400px;
  width: 100%;
  max-height: 80vh;
  overflow: hidden;
  box-shadow:
    0 20px 60px rgba(0, 0, 0, 0.15),
    0 8px 32px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  position: relative;
  margin: auto;
}

.modal-enter {
  animation: slideInUp 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.modal-leave {
  animation: slideOutDown 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes slideOutDown {
  from {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
  to {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
}

.modal-header {
  display: flex;
  align-items: center;
  padding: 24px 24px 16px;
  border-bottom: 1px solid rgba(226, 232, 240, 0.6);
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.8) 0%, rgba(248, 250, 252, 0.6) 100%);
}

.modal-icon {
  width: 40px;
  height: 40px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
}

.modal-icon.warning {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

.modal-icon.success {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.modal-icon.error {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
}

.icon-text {
  font-size: 20px;
  color: white;
}

.modal-title {
  flex: 1;
  font-size: 18px;
  font-weight: 700;
  color: #0f172a;
}

.modal-close {
  width: 32px;
  height: 32px;
  border-radius: 16px;
  background: rgba(248, 250, 252, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  border: 1px solid rgba(226, 232, 240, 0.6);
}

.modal-close:active {
  background: rgba(226, 232, 240, 0.8);
  transform: scale(0.95);
}

.modal-close svg {
  width: 16px;
  height: 16px;
  color: #64748b;
}

.modal-content {
  padding: 24px;
  max-height: 60vh;
  overflow-y: auto;
}

.modal-footer {
  display: flex;
  gap: 12px;
  padding: 16px 24px 24px;
  background: linear-gradient(135deg, rgba(248, 250, 252, 0.8) 0%, rgba(255, 255, 255, 0.6) 100%);
}

.modal-button {
  flex: 1;
  padding: 14px 20px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  font-weight: 600;
  border: 1px solid transparent;
}

.modal-button.cancel {
  background: rgba(248, 250, 252, 0.8);
  border-color: rgba(226, 232, 240, 0.6);
}

.modal-button.cancel .button-text {
  color: #64748b;
}

.modal-button.confirm {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  box-shadow: 0 4px 16px rgba(59, 130, 246, 0.3);
}

.modal-button.confirm .button-text {
  color: white;
}

.modal-button:active {
  transform: scale(0.98);
}

.modal-button.confirm:active {
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.4);
}

.button-text {
  font-size: 16px;
  font-weight: 600;
}
</style>
