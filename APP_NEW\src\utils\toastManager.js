import { ref } from 'vue'

// Toast状态管理
const toastList = ref([])
let toastId = 0

// Toast管理器
class ToastManager {
  // 显示Toast
  show(options) {
    const id = ++toastId
    const toast = {
      id,
      message: options.message || '',
      type: options.type || 'info',
      duration: options.duration || 3000,
      showCountdown: options.showCountdown !== false
    }
    
    toastList.value.push(toast)
    
    // 自动移除
    setTimeout(() => {
      this.remove(id)
    }, toast.duration + 500) // 额外500ms用于动画
    
    return id
  }
  
  // 移除Toast
  remove(id) {
    const index = toastList.value.findIndex(toast => toast.id === id)
    if (index > -1) {
      toastList.value.splice(index, 1)
    }
  }
  
  // 清空所有Toast
  clear() {
    toastList.value = []
  }
  
  // 成功提示
  success(message, options = {}) {
    return this.show({
      message,
      type: 'success',
      ...options
    })
  }
  
  // 错误提示
  error(message, options = {}) {
    return this.show({
      message,
      type: 'error',
      duration: 4000, // 错误信息显示时间稍长
      ...options
    })
  }
  
  // 警告提示
  warning(message, options = {}) {
    return this.show({
      message,
      type: 'warning',
      ...options
    })
  }
  
  // 信息提示
  info(message, options = {}) {
    return this.show({
      message,
      type: 'info',
      ...options
    })
  }
}

// 创建单例
const toastManager = new ToastManager()

// 导出
export { toastList, toastManager }
export default toastManager
