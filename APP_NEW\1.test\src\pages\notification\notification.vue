<template>
  <view class="container">
    <!-- 顶部状态栏 -->
    <view class="status-bar">
      <view class="title-section">
        <text class="page-title">通知中心</text>
        <text class="subtitle">设备实时数据通知</text>
      </view>
      <view class="back-button" @click="goBack">
        <text class="back-icon">&#xe601;</text>
      </view>
    </view>
    
    <!-- 通知设置栏 -->
    <view class="settings-bar">
      <view class="period-selector">
        <text class="period-label">通知周期:</text>
        <picker mode="selector" :range="periodOptions" :value="periodIndex" @change="changePeriod">
          <view class="period-dropdown">
            <text>{{ periodOptions[periodIndex] }}</text>
          </view>
        </picker>
      </view>
      <view class="settings-actions">
        <button class="refresh-button" @click="manualRefresh">
          <text class="refresh-icon">&#xe786;</text>
          <text>刷新</text>
        </button>
        <button class="clear-button" @click="clearNotifications">清空通知</button>
      </view>
    </view>
    
    <!-- 通知列表 -->
    <scroll-view class="notification-list" scroll-y>
      <view v-if="notifications.length === 0" class="empty-state">
        <view class="empty-icon">
          <text class="iconfont">&#xe604;</text>
        </view>
        <text class="empty-title">暂无通知</text>
        <text class="empty-subtitle">设备数据将每隔 {{ periodValues[periodIndex]/1000 }} 秒更新一次</text>
      </view>
      
      <block v-else>
        <view 
          v-for="notification in sortedNotifications" 
          :key="notification.id" 
          class="notification-item"
          :class="notification.type"
        >
          <view class="notification-header">
            <text class="time-stamp">{{ notification.time }}</text>
            <text class="notification-badge" :class="notification.type">{{ getTypeText(notification.type) }}</text>
          </view>
          <rich-text class="notification-content" :nodes="notification.content"></rich-text>
        </view>
      </block>
    </scroll-view>
    
    <!-- 底部导航栏 -->
    <view class="bottom-nav">
      <navigator url="/pages/index/index" class="nav-item" open-type="switchTab">
        <text class="nav-icon iconfont">&#xe62b;</text>
        <text class="nav-text">首页</text>
      </navigator>
      <navigator url="/pages/history/history" class="nav-item" open-type="switchTab">
        <text class="nav-icon iconfont">&#xe636;</text>
        <text class="nav-text">历史</text>
      </navigator>
      <navigator url="/pages/notification/notification" class="nav-item active" open-type="switchTab">
        <text class="nav-icon iconfont">&#xe604;</text>
        <text class="nav-text">通知</text>
      </navigator>
      <navigator url="/pages/user/user" class="nav-item" open-type="switchTab">
        <text class="nav-icon iconfont">&#xe635;</text>
        <text class="nav-text">我的</text>
      </navigator>
    </view>
  </view>
</template>

<script>
import NotificationService from '@/utils/notificationService.js';

export default {
  data() {
    return {
      notifications: [],
      periodOptions: ['5秒', '10秒', '30秒', '1分钟'],
      periodValues: [5000, 10000, 30000, 60000],
      periodIndex: 0,
      refreshing: false
    };
  },
  computed: {
    sortedNotifications() {
      // 按时间降序排列
      return [...this.notifications].sort((a, b) => b.id - a.id);
    }
  },
  onLoad() {
    // 从存储中获取保存的通知周期设置
    const savedPeriod = uni.getStorageSync('notificationPeriod');
    if (savedPeriod) {
      const idx = this.periodValues.indexOf(parseInt(savedPeriod));
      if (idx !== -1) {
        this.periodIndex = idx;
      }
    }
    
    // 注册通知更新事件
    uni.$on('notificationsUpdated', this.syncNotifications);
    
    // 同步通知列表
    this.syncNotifications();
    
    // 启动通知服务
    NotificationService.start();
    
    // 如果没有通知，添加欢迎通知
    if (this.notifications.length === 0) {
      setTimeout(() => {
        NotificationService.addNotification(`欢迎使用通知中心！系统将每隔${this.periodValues[this.periodIndex]/1000}秒自动从OneNET云平台获取最新数据。`, 'normal');
      }, 1000);
    }
  },
  onShow() {
    // 页面显示时重新同步一次，确保数据最新
    this.syncNotifications();
  },
  onUnload() {
    // 解除事件监听
    uni.$off('notificationsUpdated', this.syncNotifications);
  },
  methods: {
    syncNotifications() {
      this.notifications = NotificationService.getNotifications();
    },
    getTypeText(type) {
      switch(type) {
        case 'normal': return '普通';
        case 'changed': return '数据变化';
        case 'error': return '异常';
        default: return '信息';
      }
    },
    changePeriod(e) {
      this.periodIndex = Number(e.detail.value);
      NotificationService.setPeriod(this.periodValues[this.periodIndex]);
      uni.showToast({
        title: `通知周期已更改为 ${this.periodOptions[this.periodIndex]}`,
        icon: 'none'
      });
    },
    clearNotifications() {
      uni.showModal({
        title: '确认清空',
        content: '确定要清空所有通知记录吗？',
        success: (res) => {
          if (res.confirm) {
            NotificationService.clearNotifications();
            this.syncNotifications();
            uni.showToast({
              title: '通知记录已清空',
              icon: 'success'
            });
          }
        }
      });
    },
    manualRefresh() {
      if (this.refreshing) return;
      
      this.refreshing = true;
      NotificationService.fetchData();
      
      uni.showToast({
        title: '正在刷新数据...',
        icon: 'loading'
      });
      
      setTimeout(() => {
        this.refreshing = false;
      }, 1000);
    },
    goBack() {
      uni.switchTab({
        url: '/pages/index/index'
      });
    }
  }
};
</script>

<style>
.container {
  display: flex;
  width: 100%;
  height: 100vh;
  flex-direction: column;
  background: linear-gradient(120deg, #e0f7fa 0%, #f5f5f5 100%);
}

/* 顶部状态栏 */
.status-bar {
  display: flex;
  justify-content: space-between;
  padding: 16px 20px;
  align-items: center;
  background-color: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  z-index: 10;
}

.title-section {
  display: flex;
  flex-direction: column;
}

.page-title {
  font-size: 22px;
  font-weight: 700;
  color: #222;
}

.subtitle {
  font-size: 12px;
  color: #999;
}

.back-button {
  width: 40px;
  height: 40px;
  background-color: #222;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}

.back-icon {
  font-family: "iconfont";
  color: white;
  font-size: 20px;
}

/* 通知设置栏 */
.settings-bar {
  padding: 15px 20px;
  background-color: white;
  margin: 15px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.period-selector {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.period-label {
  font-size: 14px;
  color: #666;
  margin-right: 10px;
  white-space: nowrap;
}

.period-dropdown {
  background-color: #f5f5f5;
  padding: 8px 12px;
  border-radius: 8px;
  flex: 1;
  font-size: 14px;
  color: #333;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.settings-actions {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.refresh-button {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #2196f3;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 0 15px;
  height: 40px;
  font-size: 14px;
  flex: 1;
  min-width: 100px;
}

.refresh-icon {
  font-family: "iconfont";
  margin-right: 5px;
}

.clear-button {
  background-color: #f5f5f5;
  color: #666;
  border: none;
  border-radius: 8px;
  padding: 0 15px;
  height: 40px;
  font-size: 14px;
  flex: 1;
  min-width: 100px;
}

/* 通知列表 */
.notification-list {
  flex: 1;
  padding: 0 15px;
  margin-bottom: 70px;
  overflow-y: auto;
}

.notification-list::-webkit-scrollbar {
  display: none;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 50px 0;
}

.empty-icon {
  font-size: 50px;
  color: #ccc;
  margin-bottom: 20px;
}

.empty-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 5px;
}

.empty-subtitle {
  font-size: 14px;
  color: #999;
  text-align: center;
}

.notification-item {
  margin-bottom: 15px;
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  position: relative;
  display: flex;
  flex-direction: column;
  min-height: 80px;
}

.notification-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background-color: #ddd;
}

.notification-item.error::before {
  background-color: #f44336;
}

.notification-item.changed::before {
  background-color: #ff9800;
}

.notification-item.normal::before {
  background-color: #2196f3;
}

.notification-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 15px 8px;
  border-bottom: 1px solid #f0f0f0;
}

.time-stamp {
  font-size: 12px;
  color: #999;
}

.notification-badge {
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 10px;
  font-weight: 500;
}

.notification-badge.error {
  background-color: rgba(244, 67, 54, 0.1);
  color: #f44336;
}

.notification-badge.changed {
  background-color: rgba(255, 152, 0, 0.1);
  color: #ff9800;
}

.notification-badge.normal {
  background-color: rgba(33, 150, 243, 0.1);
  color: #2196f3;
}

.notification-content {
  padding: 12px 15px;
  font-size: 14px;
  line-height: 1.5;
  color: #333;
  flex: 1;
}

/* 底部导航栏 */
.bottom-nav {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  justify-content: space-around;
  padding: 12px 10px 8px;
  background-color: white;
  border-top-left-radius: 16px;
  border-top-right-radius: 16px;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
  z-index: 100;
}

.nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  transition: all 0.3s ease;
  flex: 1;
}

.nav-icon {
  margin-bottom: 4px;
  font-size: 20px;
  color: #aaa;
}

.nav-text {
  font-size: 12px;
  color: #aaa;
}

.nav-item.active .nav-icon,
.nav-item.active .nav-text {
  color: #2196f3;
  font-weight: 600;
}

/* 移动端适配 */
@media screen and (min-width: 500px) {
  .settings-actions {
    flex-wrap: nowrap;
  }
}
</style>