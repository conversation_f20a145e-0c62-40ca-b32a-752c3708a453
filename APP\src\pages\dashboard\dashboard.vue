<template>
  <view class="dashboard-container">
    <!-- 状态栏 -->
    <view class="status-bar">
      <view class="device-status">
        <view class="status-item" :class="{ connected: deviceState.connection.mqtt }">
          <text class="status-icon">📡</text>
          <text class="status-text">{{ deviceState.connection.mqtt ? '已连接' : '未连接' }}</text>
        </view>
        <view class="battery-status">
          <text class="battery-icon">🔋</text>
          <text class="battery-text">{{ deviceState.battery.level }}%</text>
        </view>
      </view>
      <text class="current-time">{{ currentTime }}</text>
    </view>

    <!-- 主要辐射数据卡片 -->
    <view class="main-data-card" :class="radiationLevelClass">
      <view class="radiation-header">
        <view class="status-indicator" :class="radiationLevelClass">
          <text class="indicator-icon">{{ radiationIcon }}</text>
        </view>
        <text class="radiation-title">实时辐射监测</text>
      </view>
      
      <view class="radiation-main">
        <view class="dose-rate-display">
          <text class="dose-value">{{ formatDoseRate(radiationState.currentData.doseRate) }}</text>
          <text class="dose-unit">μSv/h</text>
        </view>
        <view class="radiation-status">
          <text class="status-text" :class="radiationLevelClass">{{ radiationStatusText }}</text>
        </view>
      </view>

      <view class="additional-data">
        <view class="data-item">
          <text class="data-label">计数率</text>
          <text class="data-value">{{ radiationState.currentData.cps.toFixed(1) }} CPS</text>
        </view>
        <view class="data-item">
          <text class="data-label">累积剂量</text>
          <text class="data-value">{{ radiationState.currentData.doseSum.toFixed(3) }} μSv</text>
        </view>
        <view class="data-item">
          <text class="data-label">环境温度</text>
          <text class="data-value">{{ radiationState.currentData.temperature.toFixed(1) }}°C</text>
        </view>
      </view>
    </view>

    <!-- 快速数据概览 -->
    <view class="quick-overview">
      <view class="overview-card">
        <view class="card-header">
          <text class="card-icon">📊</text>
          <text class="card-title">今日统计</text>
        </view>
        <view class="card-content">
          <view class="stat-item">
            <text class="stat-label">平均剂量率</text>
            <text class="stat-value">{{ todayAvgDoseRate.toFixed(3) }} μSv/h</text>
          </view>
          <view class="stat-item">
            <text class="stat-label">最高剂量率</text>
            <text class="stat-value">{{ todayMaxDoseRate.toFixed(3) }} μSv/h</text>
          </view>
        </view>
      </view>

      <view class="overview-card">
        <view class="card-header">
          <text class="card-icon">⚠️</text>
          <text class="card-title">报警状态</text>
        </view>
        <view class="card-content">
          <view class="alert-count">
            <text class="alert-label">今日报警</text>
            <text class="alert-value" :class="{ 'has-alerts': todayAlerts > 0 }">{{ todayAlerts }}</text>
          </view>
          <view class="last-alert" v-if="lastAlert">
            <text class="alert-time">{{ formatTime(lastAlert.timestamp) }}</text>
            <text class="alert-msg">{{ lastAlert.message }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 实时图表预览 -->
    <view class="chart-preview">
      <view class="chart-header">
        <text class="chart-title">实时趋势</text>
        <text class="chart-subtitle" @tap="goToCharts">查看详情 →</text>
      </view>
      <view class="mini-chart">
        <canvas canvas-id="miniChart" class="chart-canvas"></canvas>
      </view>
    </view>

    <!-- 快速操作按钮 -->
    <view class="quick-actions">
      <view class="action-button" @tap="toggleCollection">
        <text class="action-icon">{{ isCollecting ? '⏸️' : '▶️' }}</text>
        <text class="action-text">{{ isCollecting ? '停止采集' : '开始采集' }}</text>
      </view>
      <view class="action-button" @tap="exportData">
        <text class="action-icon">📤</text>
        <text class="action-text">导出数据</text>
      </view>
      <view class="action-button" @tap="showSettings">
        <text class="action-icon">⚙️</text>
        <text class="action-text">设置</text>
      </view>
    </view>

    <!-- 连接状态浮窗 -->
    <view class="connection-overlay" v-if="showConnectionStatus">
      <view class="overlay-content">
        <text class="overlay-title">连接状态</text>
        <view class="connection-items">
          <view class="connection-item" :class="{ connected: deviceState.connection.mqtt }">
            <text class="conn-icon">📡</text>
            <text class="conn-label">MQTT服务器</text>
            <text class="conn-status">{{ deviceState.connection.mqtt ? '已连接' : '连接中...' }}</text>
          </view>
          <view class="connection-item" :class="{ connected: deviceState.connection.bluetooth }">
            <text class="conn-icon">📱</text>
            <text class="conn-label">蓝牙设备</text>
            <text class="conn-status">{{ deviceState.connection.bluetooth ? '已连接' : '未连接' }}</text>
          </view>
          <view class="connection-item" :class="{ connected: deviceState.connection.gps }">
            <text class="conn-icon">🛰️</text>
            <text class="conn-label">GPS定位</text>
            <text class="conn-status">{{ deviceState.connection.gps ? '已定位' : '定位中...' }}</text>
          </view>
        </view>
        <text class="overlay-close" @tap="hideConnectionStatus">关闭</text>
      </view>
    </view>
  </view>
</template>

<script>
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { radiationState, deviceState, locationState } from '../../utils/dataStore.js'
import mqttService from '../../utils/mqttService.js'
import dataStore from '../../utils/dataStore.js'

export default {
  name: 'Dashboard',
  setup() {
    const currentTime = ref('')
    const isCollecting = ref(true)
    const showConnectionStatus = ref(false)
    const timeInterval = ref(null)
    const chartContext = ref(null)

    // 计算属性
    const radiationLevel = computed(() => {
      const doseRate = radiationState.currentData.doseRate
      const { maxDoseRate, minDoseRate } = radiationState.settings
      
      if (doseRate > maxDoseRate) return 'danger'
      if (doseRate < minDoseRate) return 'warning'
      return 'safe'
    })

    const radiationLevelClass = computed(() => {
      return `level-${radiationLevel.value}`
    })

    const radiationIcon = computed(() => {
      switch (radiationLevel.value) {
        case 'danger': return '🚨'
        case 'warning': return '⚠️'
        default: return '✅'
      }
    })

    const radiationStatusText = computed(() => {
      switch (radiationLevel.value) {
        case 'danger': return '危险水平'
        case 'warning': return '需要注意'
        default: return '安全水平'
      }
    })

    // 今日统计数据
    const todayAvgDoseRate = computed(() => {
      const today = new Date().toDateString()
      const todayData = radiationState.history.filter(item => 
        new Date(item.timestamp).toDateString() === today
      )
      
      if (todayData.length === 0) return 0
      
      const total = todayData.reduce((sum, item) => sum + item.doseRate, 0)
      return total / todayData.length
    })

    const todayMaxDoseRate = computed(() => {
      const today = new Date().toDateString()
      const todayData = radiationState.history.filter(item => 
        new Date(item.timestamp).toDateString() === today
      )
      
      if (todayData.length === 0) return 0
      
      return Math.max(...todayData.map(item => item.doseRate))
    })

    const todayAlerts = computed(() => {
      const today = new Date().toDateString()
      return radiationState.alerts.filter(alert => 
        new Date(alert.timestamp).toDateString() === today
      ).length
    })

    const lastAlert = computed(() => {
      return radiationState.alerts.length > 0 ? radiationState.alerts[0] : null
    })

    // 方法
    const updateTime = () => {
      const now = new Date()
      currentTime.value = now.toLocaleTimeString('zh-CN', {
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })
    }

    const formatDoseRate = (value) => {
      if (value < 0.001) return value.toExponential(2)
      if (value < 1) return value.toFixed(3)
      return value.toFixed(2)
    }

    const formatTime = (timestamp) => {
      return new Date(timestamp).toLocaleTimeString('zh-CN', {
        hour: '2-digit',
        minute: '2-digit'
      })
    }

    const toggleCollection = () => {
      isCollecting.value = !isCollecting.value
      uni.showToast({
        title: isCollecting.value ? '开始采集数据' : '停止采集数据',
        icon: 'success'
      })
    }

    const exportData = () => {
      try {
        const data = dataStore.exportData()
        const jsonStr = JSON.stringify(data, null, 2)
        
        uni.showModal({
          title: '导出数据',
          content: '数据已准备就绪，您可以将其保存到文件',
          confirmText: '确定',
          success: (res) => {
            if (res.confirm) {
              uni.showToast({
                title: '导出成功',
                icon: 'success'
              })
            }
          }
        })
      } catch (error) {
        uni.showToast({
          title: '导出失败',
          icon: 'error'
        })
      }
    }

    const showSettings = () => {
      uni.navigateTo({
        url: '/pages/settings/settings'
      })
    }

    const goToCharts = () => {
      uni.navigateTo({
        url: '/pages/charts/charts'
      })
    }

    const hideConnectionStatus = () => {
      showConnectionStatus.value = false
    }

    const initMiniChart = () => {
      // 初始化迷你图表
      const ctx = uni.createCanvasContext('miniChart')
      if (!ctx) return

      chartContext.value = ctx
      drawMiniChart()
    }

    const drawMiniChart = () => {
      if (!chartContext.value) return

      const ctx = chartContext.value
      const width = 300
      const height = 80
      
      // 清空画布
      ctx.clearRect(0, 0, width, height)
      
      // 获取最近的数据点
      const recentData = radiationState.history.slice(0, 20).reverse()
      
      if (recentData.length < 2) return

      // 找出最大值和最小值用于缩放
      const values = recentData.map(item => item.doseRate)
      const maxValue = Math.max(...values)
      const minValue = Math.min(...values)
      const range = maxValue - minValue || 1

      // 绘制线条
      ctx.beginPath()
      ctx.setStrokeStyle('#3cc51f')
      ctx.setLineWidth(2)
      
      recentData.forEach((item, index) => {
        const x = (index / (recentData.length - 1)) * width
        const y = height - ((item.doseRate - minValue) / range) * height
        
        if (index === 0) {
          ctx.moveTo(x, y)
        } else {
          ctx.lineTo(x, y)
        }
      })
      
      ctx.stroke()
      ctx.draw()
    }

    // 生命周期
    onMounted(() => {
      updateTime()
      timeInterval.value = setInterval(updateTime, 1000)

      // 初始化MQTT连接
      mqttService.connect()
      
      // 注册MQTT事件监听
      mqttService.onMessage('radiationData', (data) => {
        dataStore.updateRadiationData(data)
        drawMiniChart()
      })
      
      mqttService.onMessage('deviceStatus', (data) => {
        dataStore.updateDeviceStatus(data)
      })
      
      mqttService.onMessage('connected', () => {
        deviceState.connection.mqtt = true
      })
      
      mqttService.onMessage('disconnected', () => {
        deviceState.connection.mqtt = false
      })

      // 初始化图表
      setTimeout(() => {
        initMiniChart()
      }, 500)

      // 模拟数据更新（仅用于演示）
      const simulateData = () => {
        const mockData = {
          doseRate: 0.1 + Math.random() * 0.05,
          cps: 50 + Math.random() * 20,
          doseSum: radiationState.currentData.doseSum + Math.random() * 0.001,
          alarmStatus: Math.random() > 0.95 ? 2 : 0,
          temperature: 25 + Math.random() * 5
        }
        dataStore.updateRadiationData(mockData)
        drawMiniChart()
      }

      // 每5秒更新一次模拟数据
      const dataInterval = setInterval(simulateData, 5000)
      
      onUnmounted(() => {
        clearInterval(dataInterval)
      })
    })

    onUnmounted(() => {
      if (timeInterval.value) {
        clearInterval(timeInterval.value)
      }
      mqttService.disconnect()
    })

    return {
      currentTime,
      isCollecting,
      showConnectionStatus,
      radiationState,
      deviceState,
      locationState,
      radiationLevelClass,
      radiationIcon,
      radiationStatusText,
      todayAvgDoseRate,
      todayMaxDoseRate,
      todayAlerts,
      lastAlert,
      formatDoseRate,
      formatTime,
      toggleCollection,
      exportData,
      showSettings,
      goToCharts,
      hideConnectionStatus
    }
  }
}
</script>

<style scoped>
.dashboard-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #0f0f1c 0%, #1a1a2e 100%);
  padding: 20rpx;
  position: relative;
}

/* 状态栏 */
.status-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  margin-bottom: 30rpx;
}

.device-status {
  display: flex;
  gap: 30rpx;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 10rpx;
  padding: 10rpx 20rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20rpx;
  transition: all 0.3s ease;
}

.status-item.connected {
  background: rgba(60, 197, 31, 0.2);
  border: 1px solid rgba(60, 197, 31, 0.3);
}

.status-icon, .battery-icon {
  font-size: 24rpx;
}

.status-text, .battery-text {
  font-size: 24rpx;
  color: #ffffff;
}

.current-time {
  font-size: 28rpx;
  color: #ffffff;
  font-weight: 500;
}

.battery-status {
  display: flex;
  align-items: center;
  gap: 10rpx;
  padding: 10rpx 20rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20rpx;
}

/* 主要数据卡片 */
.main-data-card {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20px);
  border-radius: 30rpx;
  padding: 40rpx;
  margin-bottom: 30rpx;
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.main-data-card.level-safe {
  border-color: rgba(60, 197, 31, 0.3);
  box-shadow: 0 0 30rpx rgba(60, 197, 31, 0.1);
}

.main-data-card.level-warning {
  border-color: rgba(255, 193, 7, 0.3);
  box-shadow: 0 0 30rpx rgba(255, 193, 7, 0.1);
}

.main-data-card.level-danger {
  border-color: rgba(220, 53, 69, 0.3);
  box-shadow: 0 0 30rpx rgba(220, 53, 69, 0.1);
}

.radiation-header {
  display: flex;
  align-items: center;
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.status-indicator {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: pulse 2s infinite;
}

.status-indicator.level-safe {
  background: rgba(60, 197, 31, 0.2);
}

.status-indicator.level-warning {
  background: rgba(255, 193, 7, 0.2);
}

.status-indicator.level-danger {
  background: rgba(220, 53, 69, 0.2);
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

.indicator-icon {
  font-size: 32rpx;
}

.radiation-title {
  font-size: 32rpx;
  color: #ffffff;
  font-weight: 600;
}

.radiation-main {
  text-align: center;
  margin-bottom: 40rpx;
}

.dose-rate-display {
  display: flex;
  align-items: baseline;
  justify-content: center;
  gap: 10rpx;
  margin-bottom: 20rpx;
}

.dose-value {
  font-size: 80rpx;
  font-weight: 700;
  color: #ffffff;
  line-height: 1;
}

.dose-unit {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.7);
  font-weight: 500;
}

.radiation-status {
  margin-bottom: 20rpx;
}

.radiation-status .status-text {
  font-size: 28rpx;
  font-weight: 600;
  padding: 10rpx 30rpx;
  border-radius: 20rpx;
}

.radiation-status .status-text.level-safe {
  color: #3cc51f;
  background: rgba(60, 197, 31, 0.1);
}

.radiation-status .status-text.level-warning {
  color: #ffc107;
  background: rgba(255, 193, 7, 0.1);
}

.radiation-status .status-text.level-danger {
  color: #dc3545;
  background: rgba(220, 53, 69, 0.1);
}

.additional-data {
  display: flex;
  justify-content: space-around;
  gap: 20rpx;
}

.data-item {
  text-align: center;
  flex: 1;
}

.data-label {
  display: block;
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.6);
  margin-bottom: 10rpx;
}

.data-value {
  display: block;
  font-size: 28rpx;
  color: #ffffff;
  font-weight: 600;
}

/* 快速概览 */
.quick-overview {
  display: flex;
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.overview-card {
  flex: 1;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20px);
  border-radius: 20rpx;
  padding: 30rpx;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.card-header {
  display: flex;
  align-items: center;
  gap: 15rpx;
  margin-bottom: 20rpx;
}

.card-icon {
  font-size: 28rpx;
}

.card-title {
  font-size: 28rpx;
  color: #ffffff;
  font-weight: 600;
}

.stat-item {
  margin-bottom: 15rpx;
}

.stat-label {
  display: block;
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.6);
  margin-bottom: 5rpx;
}

.stat-value {
  display: block;
  font-size: 26rpx;
  color: #ffffff;
  font-weight: 600;
}

.alert-count {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15rpx;
}

.alert-label {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.6);
}

.alert-value {
  font-size: 32rpx;
  color: #3cc51f;
  font-weight: 700;
}

.alert-value.has-alerts {
  color: #dc3545;
}

.last-alert {
  padding-top: 15rpx;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.alert-time {
  display: block;
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.5);
  margin-bottom: 5rpx;
}

.alert-msg {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.4;
}

/* 图表预览 */
.chart-preview {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20px);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.chart-title {
  font-size: 28rpx;
  color: #ffffff;
  font-weight: 600;
}

.chart-subtitle {
  font-size: 24rpx;
  color: #3cc51f;
}

.mini-chart {
  height: 150rpx;
}

.chart-canvas {
  width: 100%;
  height: 100%;
}

/* 快速操作 */
.quick-actions {
  display: flex;
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.action-button {
  flex: 1;
  background: rgba(60, 197, 31, 0.1);
  border: 1px solid rgba(60, 197, 31, 0.3);
  border-radius: 20rpx;
  padding: 30rpx 20rpx;
  text-align: center;
  transition: all 0.3s ease;
}

.action-button:active {
  transform: scale(0.95);
  background: rgba(60, 197, 31, 0.2);
}

.action-icon {
  display: block;
  font-size: 32rpx;
  margin-bottom: 10rpx;
}

.action-text {
  font-size: 24rpx;
  color: #ffffff;
  font-weight: 500;
}

/* 连接状态浮窗 */
.connection-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.overlay-content {
  background: rgba(26, 26, 46, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 30rpx;
  padding: 40rpx;
  width: 80%;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.overlay-title {
  font-size: 32rpx;
  color: #ffffff;
  font-weight: 600;
  text-align: center;
  margin-bottom: 30rpx;
}

.connection-items {
  margin-bottom: 40rpx;
}

.connection-item {
  display: flex;
  align-items: center;
  gap: 20rpx;
  padding: 20rpx;
  margin-bottom: 15rpx;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 15rpx;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.connection-item.connected {
  border-color: rgba(60, 197, 31, 0.3);
  background: rgba(60, 197, 31, 0.1);
}

.conn-icon {
  font-size: 28rpx;
}

.conn-label {
  flex: 1;
  font-size: 26rpx;
  color: #ffffff;
}

.conn-status {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.7);
}

.connection-item.connected .conn-status {
  color: #3cc51f;
}

.overlay-close {
  display: block;
  text-align: center;
  padding: 20rpx;
  background: rgba(60, 197, 31, 0.2);
  border: 1px solid rgba(60, 197, 31, 0.3);
  border-radius: 15rpx;
  color: #ffffff;
  font-size: 26rpx;
}
</style> 