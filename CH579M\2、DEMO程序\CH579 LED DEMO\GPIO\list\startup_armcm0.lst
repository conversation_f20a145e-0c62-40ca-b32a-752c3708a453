


ARM Macro Assembler    Page 1 


    1 00000000         ;/******************************************************
                       ********************//**
    2 00000000         ; * @file     startup_ARMCM0.s
    3 00000000         ; * @brief    CMSIS Core Device Startup File for
    4 00000000         ; *           ARMCM0 Device Series
    5 00000000         ; * @version  V1.08
    6 00000000         ; * @date     23. November 2012
    7 00000000         ; *
    8 00000000         ; * @note
    9 00000000         ; *
   10 00000000         ; ******************************************************
                       ************************/
   11 00000000         ;/* Copyright (c) 2011 - 2012 ARM LIMITED
   12 00000000         ;
   13 00000000         ;   All rights reserved.
   14 00000000         ;   Redistribution and use in source and binary forms, w
                       ith or without
   15 00000000         ;   modification, are permitted provided that the follow
                       ing conditions are met:
   16 00000000         ;   - Redistributions of source code must retain the abo
                       ve copyright
   17 00000000         ;     notice, this list of conditions and the following 
                       disclaimer.
   18 00000000         ;   - Redistributions in binary form must reproduce the 
                       above copyright
   19 00000000         ;     notice, this list of conditions and the following 
                       disclaimer in the
   20 00000000         ;     documentation and/or other materials provided with
                        the distribution.
   21 00000000         ;   - Neither the name of ARM nor the names of its contr
                       ibutors may be used
   22 00000000         ;     to endorse or promote products derived from this s
                       oftware without
   23 00000000         ;     specific prior written permission.
   24 00000000         ;   *
   25 00000000         ;   THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS A
                       ND CONTRIBUTORS "AS IS"
   26 00000000         ;   AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BU
                       T NOT LIMITED TO, THE
   27 00000000         ;   IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FO
                       R A PARTICULAR PURPOSE
   28 00000000         ;   ARE DISCLAIMED. IN NO EVENT SHALL COPYRIGHT HOLDERS 
                       AND CONTRIBUTORS BE
   29 00000000         ;   LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL
                       , EXEMPLARY, OR
   30 00000000         ;   CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO
                       , PROCUREMENT OF
   31 00000000         ;   SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR 
                       PROFITS; OR BUSINESS
   32 00000000         ;   INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LI
                       ABILITY, WHETHER IN
   33 00000000         ;   CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLI
                       GENCE OR OTHERWISE)
   34 00000000         ;   ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, 
                       EVEN IF ADVISED OF THE
   35 00000000         ;   POSSIBILITY OF SUCH DAMAGE.
   36 00000000         ;   ----------------------------------------------------
                       -----------------------*/
   37 00000000         ;/*



ARM Macro Assembler    Page 2 


   38 00000000         ;//-------- <<< Use Configuration Wizard in Context Menu
                        >>> ------------------
   39 00000000         ;*/
   40 00000000         
   41 00000000         
   42 00000000         ; <h> Stack Configuration
   43 00000000         ;   <o> Stack Size (in Bytes) <0x0-0xFFFFFFFF:8>
   44 00000000         ; </h>
   45 00000000         
   46 00000000 00000400 
                       Stack_Size
                               EQU              0x00000400
   47 00000000         
   48 00000000                 AREA             STACK, NOINIT, READWRITE, ALIGN
=3
   49 00000000         Stack_Mem
                               SPACE            Stack_Size
   50 00000400 20008000 
                       __initial_sp
                               EQU              0x20008000
   51 00000400         
   52 00000400         
   53 00000400         ; <h> Heap Configuration
   54 00000400         ;   <o>  Heap Size (in Bytes) <0x0-0xFFFFFFFF:8>
   55 00000400         ; </h>
   56 00000400         
   57 00000400 00000400 
                       Heap_Size
                               EQU              0x00000400
   58 00000400         
   59 00000400                 AREA             HEAP, NOINIT, READWRITE, ALIGN=
3
   60 00000000         __heap_base
   61 00000000         Heap_Mem
                               SPACE            Heap_Size
   62 00000400         __heap_limit
   63 00000400         
   64 00000400         
   65 00000400                 PRESERVE8
   66 00000400                 THUMB
   67 00000400         
   68 00000400         
   69 00000400         ; Vector Table Mapped to Address 0 at Reset
   70 00000400         
   71 00000400                 AREA             RESET, DATA, READONLY
   72 00000000                 EXPORT           __Vectors
   73 00000000                 EXPORT           __Vectors_End
   74 00000000                 EXPORT           __Vectors_Size
   75 00000000         
   76 00000000 20008000 
                       __Vectors
                               DCD              __initial_sp ; Top of Stack
   77 00000004 00000000        DCD              Reset_Handler ; Reset Handler
   78 00000008 00000000        DCD              NMI_Handler ; NMI Handler
   79 0000000C 00000000        DCD              HardFault_Handler ; Hard Fault 
                                                            Handler
   80 00000010 00000000        DCD              0           ; Reserved
   81 00000014 00000000        DCD              0           ; Reserved
   82 00000018 00000000        DCD              0           ; Reserved



ARM Macro Assembler    Page 3 


   83 0000001C 00000000        DCD              0           ; Reserved
   84 00000020 00000000        DCD              0           ; Reserved
   85 00000024 00000000        DCD              0           ; Reserved
   86 00000028 00000000        DCD              0           ; Reserved
   87 0000002C 00000000        DCD              SVC_Handler ; SVCall Handler
   88 00000030 00000000        DCD              0           ; Reserved
   89 00000034 00000000        DCD              0           ; Reserved
   90 00000038 00000000        DCD              PendSV_Handler ; PendSV Handler
                                                            
   91 0000003C 00000000        DCD              SysTick_Handler 
                                                            ; SysTick Handler
   92 00000040         
   93 00000040         ; External Interrupts
   94 00000040 00000000        DCD              TMR0_IRQHandler ;  0:  TMR0
   95 00000044 00000000        DCD              GPIO_IRQHandler ;  1:  GPIO
   96 00000048 00000000        DCD              SLAVE_IRQHandler ;  2:  SLAVE
   97 0000004C 00000000        DCD              SPI0_IRQHandler ;  3:  SPI0
   98 00000050 00000000        DCD              BB_IRQHandler ;  4:  BB
   99 00000054 00000000        DCD              LLE_IRQHandler ;  5:  LLE
  100 00000058 00000000        DCD              USB_IRQHandler ;  6:  USB
  101 0000005C 00000000        DCD              ETH_IRQHandler ;  7:  ETH
  102 00000060 00000000        DCD              TMR1_IRQHandler ;  8:  TMR1
  103 00000064 00000000        DCD              TMR2_IRQHandler ;  9:  TMR2
  104 00000068 00000000        DCD              UART0_IRQHandler ; 10:  UART0
  105 0000006C 00000000        DCD              UART1_IRQHandler ; 11:  UART1
  106 00000070 00000000        DCD              RTC_IRQHandler ; 12:  RTC
  107 00000074 00000000        DCD              ADC_IRQHandler ; 13:  ADC
  108 00000078 00000000        DCD              SPI1_IRQHandler ; 14:  SPI1
  109 0000007C 00000000        DCD              LED_IRQHandler ; 15:  LED
  110 00000080 00000000        DCD              TMR3_IRQHandler ; 16:  TMR3 
  111 00000084 00000000        DCD              UART2_IRQHandler ; 17:  UART2
  112 00000088 00000000        DCD              UART3_IRQHandler ; 18:  UART3
  113 0000008C 00000000        DCD              WDT_IRQHandler ; 19:  WDT
  114 00000090         __Vectors_End
  115 00000090         
  116 00000090 00000090 
                       __Vectors_Size
                               EQU              __Vectors_End - __Vectors
  117 00000090         
  118 00000090                 AREA             |.text|, CODE, READONLY
  119 00000000         
  120 00000000         
  121 00000000         ; Reset Handler
  122 00000000         
  123 00000000         Reset_Handler
                               PROC
  124 00000000                 EXPORT           Reset_Handler             [WEAK
]
  125 00000000                 IMPORT           SystemInit
  126 00000000                 IMPORT           __main
  127 00000000         ;LDR     R0, =0x1007058 
  128 00000000         ;MOV     SP, R0
  129 00000000 4804            LDR              R0, =SystemInit
  130 00000002 4780            BLX              R0
  131 00000004 4804            LDR              R0, =__main
  132 00000006 4700            BX               R0
  133 00000008                 ENDP
  134 00000008         
  135 00000008         



ARM Macro Assembler    Page 4 


  136 00000008         ; Dummy Exception Handlers (infinite loops which can be 
                       modified)
  137 00000008         
  138 00000008         NMI_Handler
                               PROC
  139 00000008                 EXPORT           NMI_Handler               [WEAK
]
  140 00000008 E7FE            B                .
  141 0000000A                 ENDP
  143 0000000A         HardFault_Handler
                               PROC
  144 0000000A                 EXPORT           HardFault_Handler         [WEAK
]
  145 0000000A         ;                B       .
  146 0000000A                 ENDP
  147 0000000A         SVC_Handler
                               PROC
  148 0000000A                 EXPORT           SVC_Handler               [WEAK
]
  149 0000000A E7FE            B                .
  150 0000000C                 ENDP
  151 0000000C         PendSV_Handler
                               PROC
  152 0000000C                 EXPORT           PendSV_Handler            [WEAK
]
  153 0000000C E7FE            B                .
  154 0000000E                 ENDP
  155 0000000E         SysTick_Handler
                               PROC
  156 0000000E                 EXPORT           SysTick_Handler           [WEAK
]
  157 0000000E E7FE            B                .
  158 00000010                 ENDP
  159 00000010         
  160 00000010         Default_Handler
                               PROC
  161 00000010         
  162 00000010                 EXPORT           TMR0_IRQHandler           [WEAK
] 
                                                            ;  0:  TMR0
  163 00000010                 EXPORT           GPIO_IRQHandler           [WEAK
] 
                                                            ;  1:  GPIO
  164 00000010                 EXPORT           SLAVE_IRQHandler          [WEAK
] 
                                                            ;  2:  SLAVE
  165 00000010                 EXPORT           SPI0_IRQHandler           [WEAK
] 
                                                            ;  3:  SPI0
  166 00000010                 EXPORT           BB_IRQHandler             [WEAK
] 
                                                            ;  4:  BB
  167 00000010                 EXPORT           LLE_IRQHandler            [WEAK
] 
                                                            ;  5:  LLE
  168 00000010                 EXPORT           USB_IRQHandler            [WEAK
] 
                                                            ;  6:  USB
  169 00000010                 EXPORT           ETH_IRQHandler            [WEAK



ARM Macro Assembler    Page 5 


] 
                                                            ;  7:  ETH
  170 00000010                 EXPORT           TMR1_IRQHandler           [WEAK
] 
                                                            ;  8:  TMR1
  171 00000010                 EXPORT           TMR2_IRQHandler           [WEAK
] 
                                                            ;  9:  TMR2
  172 00000010                 EXPORT           UART0_IRQHandler          [WEAK
] 
                                                            ; 10:  UART0
  173 00000010                 EXPORT           UART1_IRQHandler          [WEAK
] 
                                                            ; 11:  UART1
  174 00000010                 EXPORT           RTC_IRQHandler            [WEAK
] 
                                                            ; 12:  RTC
  175 00000010                 EXPORT           ADC_IRQHandler            [WEAK
] 
                                                            ; 13:  ADC
  176 00000010                 EXPORT           SPI1_IRQHandler           [WEAK
] 
                                                            ; 14:  SPI1
  177 00000010                 EXPORT           LED_IRQHandler            [WEAK
] 
                                                            ; 15:  LED
  178 00000010                 EXPORT           TMR3_IRQHandler           [WEAK
] 
                                                            ; 16:  TMR3 
  179 00000010                 EXPORT           UART2_IRQHandler          [WEAK
] 
                                                            ; 17:  UART2
  180 00000010                 EXPORT           UART3_IRQHandler          [WEAK
] 
                                                            ; 18:  UART3
  181 00000010                 EXPORT           WDT_IRQHandler            [WEAK
] 
                                                            ; 19:  WDT
  182 00000010         
  183 00000010         TMR0_IRQHandler                      ;  0:  TMR0
  184 00000010         GPIO_IRQHandler                      ;  1:  GPIO
  185 00000010         SLAVE_IRQHandler                     ;  2:  SLAVE
  186 00000010         SPI0_IRQHandler                      ;  3:  SPI0
  187 00000010         BB_IRQHandler                        ;  4:  BB
  188 00000010         LLE_IRQHandler                       ;  5:  LLE
  189 00000010         USB_IRQHandler                       ;  6:  USB
  190 00000010         ETH_IRQHandler                       ;  7:  ETH
  191 00000010         TMR1_IRQHandler                      ;  8:  TMR1
  192 00000010         TMR2_IRQHandler                      ;  9:  TMR2
  193 00000010         UART0_IRQHandler                     ; 10:  UART0
  194 00000010         UART1_IRQHandler                     ; 11:  UART1
  195 00000010         RTC_IRQHandler                       ; 12:  RTC
  196 00000010         ADC_IRQHandler                       ; 13:  ADC
  197 00000010         SPI1_IRQHandler                      ; 14:  SPI1
  198 00000010         LED_IRQHandler                       ; 15:  LED
  199 00000010         TMR3_IRQHandler                      ; 16:  TMR3 
  200 00000010         UART2_IRQHandler                     ; 17:  UART2
  201 00000010         UART3_IRQHandler                     ; 18:  UART3
  202 00000010         WDT_IRQHandler                       ; 19:  WDT



ARM Macro Assembler    Page 6 


  203 00000010 E7FE            B                .
  204 00000012         
  205 00000012                 ENDP
  206 00000012         
  207 00000012         
  208 00000012 00 00           ALIGN
  209 00000014         
  210 00000014         
  211 00000014         ; User Initial Stack & Heap
  212 00000014         
  213 00000014                 IF               :DEF:__MICROLIB
  214 00000014         
  215 00000014                 EXPORT           __initial_sp
  216 00000014                 EXPORT           __heap_base
  217 00000014                 EXPORT           __heap_limit
  218 00000014         
  219 00000014                 ELSE
  234                          ENDIF
  235 00000014         
  236 00000014         
  237 00000014                 END
              00000000 
              00000000 
Command Line: --debug --xref --diag_suppress=9931 --cpu=Cortex-M0 --apcs=interw
ork --depend=.\obj\startup_armcm0.d -o.\obj\startup_armcm0.o -IC:\Users\<USER>\AppData\Local\Arm\Packs\Keil\WCH57x_DFP\1.1.0\Drivers\CMSIS\Device\WCH\C
H57x\Include -IC:\Keil_v5\ARM\CMSIS\Include --predefine="__MICROLIB SETA 1" --p
redefine="__UVISION_VERSION SETA 533" --list=.\list\startup_armcm0.lst ..\SRC\S
tartup\startup_ARMCM0.s



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Relocatable symbols

STACK 00000000

Symbol: STACK
   Definitions
      At line 48 in file ..\SRC\Startup\startup_ARMCM0.s
   Uses
      None
Comment: STACK unused
Stack_Mem 00000000

Symbol: Stack_Mem
   Definitions
      At line 49 in file ..\SRC\Startup\startup_ARMCM0.s
   Uses
      None
Comment: Stack_Mem unused
2 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Relocatable symbols

HEAP 00000000

Symbol: HEAP
   Definitions
      At line 59 in file ..\SRC\Startup\startup_ARMCM0.s
   Uses
      None
Comment: HEAP unused
Heap_Mem 00000000

Symbol: Heap_Mem
   Definitions
      At line 61 in file ..\SRC\Startup\startup_ARMCM0.s
   Uses
      None
Comment: Heap_Mem unused
__heap_base 00000000

Symbol: __heap_base
   Definitions
      At line 60 in file ..\SRC\Startup\startup_ARMCM0.s
   Uses
      At line 216 in file ..\SRC\Startup\startup_ARMCM0.s
Comment: __heap_base used once
__heap_limit 00000400

Symbol: __heap_limit
   Definitions
      At line 62 in file ..\SRC\Startup\startup_ARMCM0.s
   Uses
      At line 217 in file ..\SRC\Startup\startup_ARMCM0.s
Comment: __heap_limit used once
4 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Relocatable symbols

RESET 00000000

Symbol: RESET
   Definitions
      At line 71 in file ..\SRC\Startup\startup_ARMCM0.s
   Uses
      None
Comment: RESET unused
__Vectors 00000000

Symbol: __Vectors
   Definitions
      At line 76 in file ..\SRC\Startup\startup_ARMCM0.s
   Uses
      At line 72 in file ..\SRC\Startup\startup_ARMCM0.s
      At line 116 in file ..\SRC\Startup\startup_ARMCM0.s

__Vectors_End 00000090

Symbol: __Vectors_End
   Definitions
      At line 114 in file ..\SRC\Startup\startup_ARMCM0.s
   Uses
      At line 73 in file ..\SRC\Startup\startup_ARMCM0.s
      At line 116 in file ..\SRC\Startup\startup_ARMCM0.s

3 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Relocatable symbols

.text 00000000

Symbol: .text
   Definitions
      At line 118 in file ..\SRC\Startup\startup_ARMCM0.s
   Uses
      None
Comment: .text unused
ADC_IRQHandler 00000010

Symbol: ADC_IRQHandler
   Definitions
      At line 196 in file ..\SRC\Startup\startup_ARMCM0.s
   Uses
      At line 107 in file ..\SRC\Startup\startup_ARMCM0.s
      At line 175 in file ..\SRC\Startup\startup_ARMCM0.s

BB_IRQHandler 00000010

Symbol: BB_IRQHandler
   Definitions
      At line 187 in file ..\SRC\Startup\startup_ARMCM0.s
   Uses
      At line 98 in file ..\SRC\Startup\startup_ARMCM0.s
      At line 166 in file ..\SRC\Startup\startup_ARMCM0.s

Default_Handler 00000010

Symbol: Default_Handler
   Definitions
      At line 160 in file ..\SRC\Startup\startup_ARMCM0.s
   Uses
      None
Comment: Default_Handler unused
ETH_IRQHandler 00000010

Symbol: ETH_IRQHandler
   Definitions
      At line 190 in file ..\SRC\Startup\startup_ARMCM0.s
   Uses
      At line 101 in file ..\SRC\Startup\startup_ARMCM0.s
      At line 169 in file ..\SRC\Startup\startup_ARMCM0.s

GPIO_IRQHandler 00000010

Symbol: GPIO_IRQHandler
   Definitions
      At line 184 in file ..\SRC\Startup\startup_ARMCM0.s
   Uses
      At line 95 in file ..\SRC\Startup\startup_ARMCM0.s
      At line 163 in file ..\SRC\Startup\startup_ARMCM0.s

HardFault_Handler 0000000A

Symbol: HardFault_Handler
   Definitions
      At line 143 in file ..\SRC\Startup\startup_ARMCM0.s
   Uses
      At line 79 in file ..\SRC\Startup\startup_ARMCM0.s



ARM Macro Assembler    Page 2 Alphabetic symbol ordering
Relocatable symbols

      At line 144 in file ..\SRC\Startup\startup_ARMCM0.s

LED_IRQHandler 00000010

Symbol: LED_IRQHandler
   Definitions
      At line 198 in file ..\SRC\Startup\startup_ARMCM0.s
   Uses
      At line 109 in file ..\SRC\Startup\startup_ARMCM0.s
      At line 177 in file ..\SRC\Startup\startup_ARMCM0.s

LLE_IRQHandler 00000010

Symbol: LLE_IRQHandler
   Definitions
      At line 188 in file ..\SRC\Startup\startup_ARMCM0.s
   Uses
      At line 99 in file ..\SRC\Startup\startup_ARMCM0.s
      At line 167 in file ..\SRC\Startup\startup_ARMCM0.s

NMI_Handler 00000008

Symbol: NMI_Handler
   Definitions
      At line 138 in file ..\SRC\Startup\startup_ARMCM0.s
   Uses
      At line 78 in file ..\SRC\Startup\startup_ARMCM0.s
      At line 139 in file ..\SRC\Startup\startup_ARMCM0.s

PendSV_Handler 0000000C

Symbol: PendSV_Handler
   Definitions
      At line 151 in file ..\SRC\Startup\startup_ARMCM0.s
   Uses
      At line 90 in file ..\SRC\Startup\startup_ARMCM0.s
      At line 152 in file ..\SRC\Startup\startup_ARMCM0.s

RTC_IRQHandler 00000010

Symbol: RTC_IRQHandler
   Definitions
      At line 195 in file ..\SRC\Startup\startup_ARMCM0.s
   Uses
      At line 106 in file ..\SRC\Startup\startup_ARMCM0.s
      At line 174 in file ..\SRC\Startup\startup_ARMCM0.s

Reset_Handler 00000000

Symbol: Reset_Handler
   Definitions
      At line 123 in file ..\SRC\Startup\startup_ARMCM0.s
   Uses
      At line 77 in file ..\SRC\Startup\startup_ARMCM0.s
      At line 124 in file ..\SRC\Startup\startup_ARMCM0.s

SLAVE_IRQHandler 00000010

Symbol: SLAVE_IRQHandler



ARM Macro Assembler    Page 3 Alphabetic symbol ordering
Relocatable symbols

   Definitions
      At line 185 in file ..\SRC\Startup\startup_ARMCM0.s
   Uses
      At line 96 in file ..\SRC\Startup\startup_ARMCM0.s
      At line 164 in file ..\SRC\Startup\startup_ARMCM0.s

SPI0_IRQHandler 00000010

Symbol: SPI0_IRQHandler
   Definitions
      At line 186 in file ..\SRC\Startup\startup_ARMCM0.s
   Uses
      At line 97 in file ..\SRC\Startup\startup_ARMCM0.s
      At line 165 in file ..\SRC\Startup\startup_ARMCM0.s

SPI1_IRQHandler 00000010

Symbol: SPI1_IRQHandler
   Definitions
      At line 197 in file ..\SRC\Startup\startup_ARMCM0.s
   Uses
      At line 108 in file ..\SRC\Startup\startup_ARMCM0.s
      At line 176 in file ..\SRC\Startup\startup_ARMCM0.s

SVC_Handler 0000000A

Symbol: SVC_Handler
   Definitions
      At line 147 in file ..\SRC\Startup\startup_ARMCM0.s
   Uses
      At line 87 in file ..\SRC\Startup\startup_ARMCM0.s
      At line 148 in file ..\SRC\Startup\startup_ARMCM0.s

SysTick_Handler 0000000E

Symbol: SysTick_Handler
   Definitions
      At line 155 in file ..\SRC\Startup\startup_ARMCM0.s
   Uses
      At line 91 in file ..\SRC\Startup\startup_ARMCM0.s
      At line 156 in file ..\SRC\Startup\startup_ARMCM0.s

TMR0_IRQHandler 00000010

Symbol: TMR0_IRQHandler
   Definitions
      At line 183 in file ..\SRC\Startup\startup_ARMCM0.s
   Uses
      At line 94 in file ..\SRC\Startup\startup_ARMCM0.s
      At line 162 in file ..\SRC\Startup\startup_ARMCM0.s

TMR1_IRQHandler 00000010

Symbol: TMR1_IRQHandler
   Definitions
      At line 191 in file ..\SRC\Startup\startup_ARMCM0.s
   Uses
      At line 102 in file ..\SRC\Startup\startup_ARMCM0.s
      At line 170 in file ..\SRC\Startup\startup_ARMCM0.s



ARM Macro Assembler    Page 4 Alphabetic symbol ordering
Relocatable symbols


TMR2_IRQHandler 00000010

Symbol: TMR2_IRQHandler
   Definitions
      At line 192 in file ..\SRC\Startup\startup_ARMCM0.s
   Uses
      At line 103 in file ..\SRC\Startup\startup_ARMCM0.s
      At line 171 in file ..\SRC\Startup\startup_ARMCM0.s

TMR3_IRQHandler 00000010

Symbol: TMR3_IRQHandler
   Definitions
      At line 199 in file ..\SRC\Startup\startup_ARMCM0.s
   Uses
      At line 110 in file ..\SRC\Startup\startup_ARMCM0.s
      At line 178 in file ..\SRC\Startup\startup_ARMCM0.s

UART0_IRQHandler 00000010

Symbol: UART0_IRQHandler
   Definitions
      At line 193 in file ..\SRC\Startup\startup_ARMCM0.s
   Uses
      At line 104 in file ..\SRC\Startup\startup_ARMCM0.s
      At line 172 in file ..\SRC\Startup\startup_ARMCM0.s

UART1_IRQHandler 00000010

Symbol: UART1_IRQHandler
   Definitions
      At line 194 in file ..\SRC\Startup\startup_ARMCM0.s
   Uses
      At line 105 in file ..\SRC\Startup\startup_ARMCM0.s
      At line 173 in file ..\SRC\Startup\startup_ARMCM0.s

UART2_IRQHandler 00000010

Symbol: UART2_IRQHandler
   Definitions
      At line 200 in file ..\SRC\Startup\startup_ARMCM0.s
   Uses
      At line 111 in file ..\SRC\Startup\startup_ARMCM0.s
      At line 179 in file ..\SRC\Startup\startup_ARMCM0.s

UART3_IRQHandler 00000010

Symbol: UART3_IRQHandler
   Definitions
      At line 201 in file ..\SRC\Startup\startup_ARMCM0.s
   Uses
      At line 112 in file ..\SRC\Startup\startup_ARMCM0.s
      At line 180 in file ..\SRC\Startup\startup_ARMCM0.s

USB_IRQHandler 00000010

Symbol: USB_IRQHandler
   Definitions



ARM Macro Assembler    Page 5 Alphabetic symbol ordering
Relocatable symbols

      At line 189 in file ..\SRC\Startup\startup_ARMCM0.s
   Uses
      At line 100 in file ..\SRC\Startup\startup_ARMCM0.s
      At line 168 in file ..\SRC\Startup\startup_ARMCM0.s

WDT_IRQHandler 00000010

Symbol: WDT_IRQHandler
   Definitions
      At line 202 in file ..\SRC\Startup\startup_ARMCM0.s
   Uses
      At line 113 in file ..\SRC\Startup\startup_ARMCM0.s
      At line 181 in file ..\SRC\Startup\startup_ARMCM0.s

28 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Absolute symbols

Heap_Size 00000400

Symbol: Heap_Size
   Definitions
      At line 57 in file ..\SRC\Startup\startup_ARMCM0.s
   Uses
      At line 61 in file ..\SRC\Startup\startup_ARMCM0.s
Comment: Heap_Size used once
Stack_Size 00000400

Symbol: Stack_Size
   Definitions
      At line 46 in file ..\SRC\Startup\startup_ARMCM0.s
   Uses
      At line 49 in file ..\SRC\Startup\startup_ARMCM0.s
Comment: Stack_Size used once
__Vectors_Size 00000090

Symbol: __Vectors_Size
   Definitions
      At line 116 in file ..\SRC\Startup\startup_ARMCM0.s
   Uses
      At line 74 in file ..\SRC\Startup\startup_ARMCM0.s
Comment: __Vectors_Size used once
__initial_sp 20008000

Symbol: __initial_sp
   Definitions
      At line 50 in file ..\SRC\Startup\startup_ARMCM0.s
   Uses
      At line 76 in file ..\SRC\Startup\startup_ARMCM0.s
      At line 215 in file ..\SRC\Startup\startup_ARMCM0.s

4 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
External symbols

SystemInit 00000000

Symbol: SystemInit
   Definitions
      At line 125 in file ..\SRC\Startup\startup_ARMCM0.s
   Uses
      At line 129 in file ..\SRC\Startup\startup_ARMCM0.s
Comment: SystemInit used once
__main 00000000

Symbol: __main
   Definitions
      At line 126 in file ..\SRC\Startup\startup_ARMCM0.s
   Uses
      At line 131 in file ..\SRC\Startup\startup_ARMCM0.s
Comment: __main used once
2 symbols
377 symbols in table
