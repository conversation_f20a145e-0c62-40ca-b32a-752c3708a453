<template>
  <view 
    class="animated-card" 
    :class="[
      cardType, 
      { 'card-hover': allowHover, 'card-pressed': isPressed }
    ]"
    @touchstart="onTouchStart"
    @touchend="onTouchEnd"
    @tap="onCardTap"
  >
    <!-- 卡片背景动画 -->
    <view class="card-background">
      <view class="bg-gradient" :style="backgroundStyle"></view>
      <view class="bg-overlay"></view>
    </view>
    
    <!-- 卡片内容 -->
    <view class="card-content">
      <!-- 卡片头部 -->
      <view class="card-header" v-if="showHeader">
        <view class="header-icon" v-if="iconName">
          <view class="icon-container" :class="iconType">
            <text class="icon-text">{{ iconName }}</text>
          </view>
        </view>
        <view class="header-content">
          <text class="card-title">{{ title }}</text>
          <text class="card-subtitle" v-if="subtitle">{{ subtitle }}</text>
        </view>
        <view class="header-action" v-if="actionIcon">
          <view class="action-btn">
            <text class="action-icon">{{ actionIcon }}</text>
          </view>
        </view>
      </view>
      
      <!-- 主要内容区域 -->
      <view class="card-main">
        <slot></slot>
      </view>
      
      <!-- 卡片底部 -->
      <view class="card-footer" v-if="showFooter">
        <slot name="footer">
          <view class="footer-stats" v-if="stats">
            <view class="stat-item" v-for="(stat, index) in stats" :key="index">
              <text class="stat-value">{{ stat.value }}</text>
              <text class="stat-label">{{ stat.label }}</text>
            </view>
          </view>
        </slot>
      </view>
    </view>
    
    <!-- 装饰性元素 -->
    <view class="card-decorations">
      <view class="decoration-dots">
        <view class="dot" v-for="n in 3" :key="n"></view>
      </view>
      <view class="decoration-lines">
        <view class="line" v-for="n in 2" :key="n"></view>
      </view>
    </view>
    
    <!-- 悬浮加载状态 -->
    <view class="card-loading" v-if="loading">
      <view class="loading-spinner"></view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'

const props = defineProps({
  title: {
    type: String,
    default: ''
  },
  subtitle: {
    type: String,
    default: ''
  },
  cardType: {
    type: String,
    default: 'default', // default, primary, success, warning, danger
    validator: (value) => ['default', 'primary', 'success', 'warning', 'danger'].includes(value)
  },
  iconName: {
    type: String,
    default: ''
  },
  iconType: {
    type: String,
    default: 'default', // default, primary, success, warning, danger
    validator: (value) => ['default', 'primary', 'success', 'warning', 'danger'].includes(value)
  },
  actionIcon: {
    type: String,
    default: ''
  },
  showHeader: {
    type: Boolean,
    default: true
  },
  showFooter: {
    type: Boolean,
    default: false
  },
  allowHover: {
    type: Boolean,
    default: true
  },
  stats: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  },
  gradient: {
    type: Object,
    default: () => ({
      from: '#667eea',
      to: '#764ba2'
    })
  }
})

const emit = defineEmits(['tap', 'action'])

// 响应式数据
const isPressed = ref(false)

// 计算属性
const backgroundStyle = computed(() => {
  return {
    background: `linear-gradient(135deg, ${props.gradient.from} 0%, ${props.gradient.to} 100%)`
  }
})

// 方法
const onTouchStart = () => {
  if (props.allowHover) {
    isPressed.value = true
  }
}

const onTouchEnd = () => {
  setTimeout(() => {
    isPressed.value = false
  }, 150)
}

const onCardTap = () => {
  emit('tap')
}

// 组件挂载时添加入场动画
onMounted(() => {
  // 可以添加一些初始化动画
})
</script>

<style scoped>
.animated-card {
  position: relative;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-radius: 24px;
  overflow: hidden;
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.1),
    0 4px 16px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.8);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
}

.animated-card.card-hover:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow: 
    0 16px 64px rgba(0, 0, 0, 0.15),
    0 8px 32px rgba(0, 0, 0, 0.1);
  border-color: rgba(102, 126, 234, 0.3);
}

.animated-card.card-pressed {
  transform: translateY(2px) scale(0.98);
  box-shadow: 
    0 4px 16px rgba(0, 0, 0, 0.1),
    0 2px 8px rgba(0, 0, 0, 0.05);
}

.card-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0;
  transition: opacity 0.4s ease;
}

.animated-card:hover .card-background {
  opacity: 0.1;
}

.bg-gradient {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.bg-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.3) 0%, transparent 50%);
}

.card-content {
  position: relative;
  z-index: 2;
  padding: 20px;
}

.card-header {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.header-icon {
  margin-right: 12px;
}

.icon-container {
  width: 48px;
  height: 48px;
  border-radius: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  font-size: 20px;
  font-weight: 600;
  box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
}

.icon-container.primary {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  box-shadow: 0 4px 16px rgba(59, 130, 246, 0.3);
}

.icon-container.success {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  box-shadow: 0 4px 16px rgba(16, 185, 129, 0.3);
}

.icon-container.warning {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  box-shadow: 0 4px 16px rgba(245, 158, 11, 0.3);
}

.icon-container.danger {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  box-shadow: 0 4px 16px rgba(239, 68, 68, 0.3);
}

.animated-card:hover .icon-container {
  transform: scale(1.1) rotate(5deg);
}

.header-content {
  flex: 1;
}

.card-title {
  font-size: 18px;
  font-weight: 700;
  color: #1a1a1a;
  margin-bottom: 4px;
  line-height: 1.2;
}

.card-subtitle {
  font-size: 14px;
  color: #666;
  font-weight: 500;
  line-height: 1.3;
}

.header-action {
  margin-left: 12px;
}

.action-btn {
  width: 36px;
  height: 36px;
  border-radius: 18px;
  background: rgba(102, 126, 234, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.action-btn:active {
  transform: scale(0.9);
  background: rgba(102, 126, 234, 0.2);
}

.action-icon {
  font-size: 16px;
  color: #667eea;
}

.card-main {
  margin-bottom: 16px;
}

.card-footer {
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  padding-top: 16px;
}

.footer-stats {
  display: flex;
  gap: 20px;
}

.stat-item {
  flex: 1;
  text-align: center;
}

.stat-value {
  font-size: 20px;
  font-weight: 700;
  color: #1a1a1a;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: #666;
  font-weight: 500;
}

.card-decorations {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 1;
}

.decoration-dots {
  position: absolute;
  top: 20px;
  right: 20px;
  display: flex;
  gap: 4px;
}

.dot {
  width: 4px;
  height: 4px;
  border-radius: 50%;
  background: rgba(102, 126, 234, 0.3);
  animation: dotPulse 2s ease-in-out infinite;
}

.dot:nth-child(2) {
  animation-delay: 0.3s;
}

.dot:nth-child(3) {
  animation-delay: 0.6s;
}

@keyframes dotPulse {
  0%, 100% {
    opacity: 0.3;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.2);
  }
}

.decoration-lines {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  overflow: hidden;
}

.line {
  position: absolute;
  top: 0;
  height: 100%;
  width: 50px;
  background: linear-gradient(90deg, 
    transparent 0%, 
    rgba(102, 126, 234, 0.5) 50%, 
    transparent 100%);
  animation: lineMove 3s ease-in-out infinite;
}

.line:nth-child(2) {
  animation-delay: 1.5s;
}

@keyframes lineMove {
  0% {
    left: -50px;
  }
  100% {
    left: 100%;
  }
}

.card-loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
  backdrop-filter: blur(4px);
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid rgba(102, 126, 234, 0.2);
  border-top: 3px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 卡片类型样式 */
.animated-card.primary {
  border-left: 4px solid #3b82f6;
}

.animated-card.success {
  border-left: 4px solid #10b981;
}

.animated-card.warning {
  border-left: 4px solid #f59e0b;
}

.animated-card.danger {
  border-left: 4px solid #ef4444;
}

/* 响应式设计 */
@media (max-width: 375px) {
  .card-content {
    padding: 16px;
  }
  
  .card-header {
    margin-bottom: 12px;
  }
  
  .icon-container {
    width: 40px;
    height: 40px;
    border-radius: 20px;
    font-size: 18px;
  }
  
  .card-title {
    font-size: 16px;
  }
  
  .footer-stats {
    gap: 16px;
  }
}

/* 入场动画 */
.animated-card {
  animation: cardEnter 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes cardEnter {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}
</style> 