Component: ARM Compiler 5.06 update 7 (build 960) Tool: armlink [4d3601]

==============================================================================

Section Cross References

    main.o(.text) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    main.o(.text) refers to delay.o(.text) for delay_init
    main.o(.text) refers to sys.o(.text) for NVIC_Configuration
    main.o(.text) refers to led.o(.text) for LED_Init
    main.o(.text) refers to usart.o(.text) for uart_init
    main.o(.text) refers to noretval__2printf.o(.text) for __2printf
    main.o(.text) refers to ec800.o(.text) for EC800_Init
    main.o(.text) refers to wdg.o(.text) for IWDG_Feed
    system_stm32f10x.o(.text) refers to stm32f10x_rcc.o(.text) for RCC_DeInit
    system_stm32f10x.o(.text) refers to stm32f10x_flash.o(.text) for FLASH_PrefetchBufferCmd
    system_stm32f10x.o(.text) refers to system_stm32f10x.o(.data) for SystemCoreClock
    led.o(.text) refers to stm32f10x_rcc.o(.text) for RCC_APB2PeriphClockCmd
    led.o(.text) refers to stm32f10x_gpio.o(.text) for GPIO_Init
    wdg.o(.text) refers to stm32f10x_iwdg.o(.text) for IWDG_WriteAccessCmd
    ec800.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    ec800.o(.text) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    ec800.o(.text) refers to _printf_str.o(.text) for _printf_str
    ec800.o(.text) refers to wdg.o(.text) for IWDG_Feed
    ec800.o(.text) refers to noretval__2printf.o(.text) for __2printf
    ec800.o(.text) refers to delay.o(.text) for delay_ms
    ec800.o(.text) refers to strstr.o(.text) for strstr
    ec800.o(.text) refers to stm32f10x_usart.o(.text) for USART_SendData
    ec800.o(.text) refers to usart.o(.bss) for RxBuffer
    ec800.o(.text) refers to usart.o(.data) for RxCounter
    ec800.o(.text) refers to ec800.o(.data) for strx
    dht11.o(.text) refers to delay.o(.text) for delay_ms
    stm32f10x_gpio.o(.text) refers to stm32f10x_rcc.o(.text) for RCC_APB2PeriphResetCmd
    stm32f10x_usart.o(.text) refers to stm32f10x_rcc.o(.text) for RCC_APB2PeriphResetCmd
    stm32f10x_rcc.o(.text) refers to stm32f10x_rcc.o(.data) for APBAHBPrescTable
    startup_stm32f10x_hd.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_hd.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_hd.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_hd.o(RESET) refers to startup_stm32f10x_hd.o(STACK) for __initial_sp
    startup_stm32f10x_hd.o(RESET) refers to startup_stm32f10x_hd.o(.text) for Reset_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(.text) for NMI_Handler
    startup_stm32f10x_hd.o(RESET) refers to usart.o(.text) for USART1_IRQHandler
    startup_stm32f10x_hd.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_hd.o(.text) refers to system_stm32f10x.o(.text) for SystemInit
    startup_stm32f10x_hd.o(.text) refers to __main.o(!!!main) for __main
    startup_stm32f10x_hd.o(.text) refers to startup_stm32f10x_hd.o(HEAP) for Heap_Mem
    startup_stm32f10x_hd.o(.text) refers to startup_stm32f10x_hd.o(STACK) for Stack_Mem
    usart.o(.text) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(.text) refers to stm32f10x_rcc.o(.text) for RCC_APB2PeriphClockCmd
    usart.o(.text) refers to stm32f10x_usart.o(.text) for USART_DeInit
    usart.o(.text) refers to stm32f10x_gpio.o(.text) for GPIO_Init
    usart.o(.text) refers to misc.o(.text) for NVIC_Init
    usart.o(.text) refers to usart.o(.data) for RxCounter
    usart.o(.text) refers to usart.o(.bss) for RxBuffer
    usart.o(.bss) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(.data) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    delay.o(.text) refers to misc.o(.text) for SysTick_CLKSourceConfig
    delay.o(.text) refers to system_stm32f10x.o(.data) for SystemCoreClock
    delay.o(.text) refers to delay.o(.data) for fac_us
    sys.o(.text) refers to misc.o(.text) for NVIC_PriorityGroupConfig
    use_no_semi_2.o(.text) refers (Special) to use_no_semi.o(.text) for __use_no_semihosting_swi
    __2printf.o(.text) refers to _printf_char_file.o(.text) for _printf_char_file
    __2printf.o(.text) refers to usart.o(.data) for __stdout
    noretval__2printf.o(.text) refers to _printf_char_file.o(.text) for _printf_char_file
    noretval__2printf.o(.text) refers to usart.o(.data) for __stdout
    __printf.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    _printf_str.o(.text) refers (Special) to _printf_char.o(.text) for _printf_cs_common
    __printf_flags.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags.o(.text) refers to __printf_flags.o(.constdata) for .constdata
    __printf_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to __printf_flags_ss.o(.constdata) for .constdata
    __printf_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_flags_wp.o(.constdata) for .constdata
    __printf_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_flags_ss_wp.o(.constdata) for .constdata
    _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) refers (Weak) to _printf_char.o(.text) for _printf_string
    _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) refers (Special) to _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017) for _printf_percent_end
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    _printf_char.o(.text) refers (Weak) to _printf_str.o(.text) for _printf_str
    _printf_char_file.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    _printf_char_file.o(.text) refers to ferror.o(.text) for ferror
    _printf_char_file.o(.text) refers to usart.o(.text) for fputc
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to main.o(.text) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    _printf_char_common.o(.text) refers to __printf.o(.text) for __printf
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_stm32f10x_hd.o(.text) for __user_initial_stackheap
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002C) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000002) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000A) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000011) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000033) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_user_alloc_1
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    libinit2.o(.ARM.Collect$$libinit$$00000010) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000026) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000027) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to usart.o(.text) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    argv_veneer.o(.emb_text) refers to no_argv.o(.text) for __ARM_get_argv
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000004) for __rt_lib_shutdown_cpp_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000002) for __rt_lib_shutdown_fini_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000009) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000011) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000012) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000006) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E) for __rt_lib_shutdown_user_alloc_1
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to usart.o(.text) for _sys_exit
    defsig_exit.o(.text) refers to usart.o(.text) for _sys_exit
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    sys_wrch.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display


==============================================================================

Removing Unused input sections from the image.

    Removing ec800.o(.bss), (100 bytes).
    Removing dht11.o(.text), (400 bytes).
    Removing core_cm3.o(.emb_text), (32 bytes).

3 unused section(s) (total 532 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit1.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit2.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit3.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardshut.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_zi.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_copy.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry4.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_raise.o ABSOLUTE
    ../clib/angel/scatter.s                  0x00000000   Number         0  __scatter.o ABSOLUTE
    ../clib/angel/startup.s                  0x00000000   Number         0  __main.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  libspace.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  indicate_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  sys_stackheap_outer.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi_2.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  no_argv.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv_nomalloc.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hrguard.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxi.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit2.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memcpy_w.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_nopercent.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_file.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __2printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_common.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  noretval__2printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_str.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent_end.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_s.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_exit.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_other.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_segv_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_cppl_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_pvfn_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_stak_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  __raise.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_general.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_formal.o ABSOLUTE
    ../clib/signal.s                         0x00000000   Number         0  defsig.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  ferror_locked.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  ferror.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  exit.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  strstr.o ABSOLUTE
    ../fplib/fpinit.s                        0x00000000   Number         0  fpinit.o ABSOLUTE
    ..\CORE\core_cm3.c                       0x00000000   Number         0  core_cm3.o ABSOLUTE
    ..\CORE\startup_stm32f10x_hd.s           0x00000000   Number         0  startup_stm32f10x_hd.o ABSOLUTE
    ..\HARDWARE\DHT11\dht11.c                0x00000000   Number         0  dht11.o ABSOLUTE
    ..\HARDWARE\EC800\EC800.c                0x00000000   Number         0  ec800.o ABSOLUTE
    ..\HARDWARE\LED\led.c                    0x00000000   Number         0  led.o ABSOLUTE
    ..\HARDWARE\WDG\wdg.c                    0x00000000   Number         0  wdg.o ABSOLUTE
    ..\STM32F10x_FWLib\src\misc.c            0x00000000   Number         0  misc.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_flash.c 0x00000000   Number         0  stm32f10x_flash.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_gpio.c  0x00000000   Number         0  stm32f10x_gpio.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_iwdg.c  0x00000000   Number         0  stm32f10x_iwdg.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_rcc.c   0x00000000   Number         0  stm32f10x_rcc.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_usart.c 0x00000000   Number         0  stm32f10x_usart.o ABSOLUTE
    ..\SYSTEM\delay\delay.c                  0x00000000   Number         0  delay.o ABSOLUTE
    ..\SYSTEM\sys\sys.c                      0x00000000   Number         0  sys.o ABSOLUTE
    ..\SYSTEM\usart\usart.c                  0x00000000   Number         0  usart.o ABSOLUTE
    ..\\CORE\\core_cm3.c                     0x00000000   Number         0  core_cm3.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    main.c                                   0x00000000   Number         0  main.o ABSOLUTE
    stm32f10x_it.c                           0x00000000   Number         0  stm32f10x_it.o ABSOLUTE
    system_stm32f10x.c                       0x00000000   Number         0  system_stm32f10x.o ABSOLUTE
    RESET                                    0x08000000   Section      304  startup_stm32f10x_hd.o(RESET)
    !!!main                                  0x08000130   Section        8  __main.o(!!!main)
    !!!scatter                               0x08000138   Section       52  __scatter.o(!!!scatter)
    !!handler_copy                           0x0800016c   Section       26  __scatter_copy.o(!!handler_copy)
    !!handler_zi                             0x08000188   Section       28  __scatter_zi.o(!!handler_zi)
    .ARM.Collect$$_printf_percent$$00000000  0x080001a4   Section        0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    .ARM.Collect$$_printf_percent$$00000014  0x080001a4   Section        6  _printf_s.o(.ARM.Collect$$_printf_percent$$00000014)
    .ARM.Collect$$_printf_percent$$00000017  0x080001aa   Section        4  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    .ARM.Collect$$libinit$$00000000          0x080001ae   Section        2  libinit.o(.ARM.Collect$$libinit$$00000000)
    .ARM.Collect$$libinit$$00000002          0x080001b0   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000002)
    .ARM.Collect$$libinit$$00000004          0x080001b0   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    .ARM.Collect$$libinit$$0000000A          0x080001b0   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    .ARM.Collect$$libinit$$0000000C          0x080001b0   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    .ARM.Collect$$libinit$$0000000E          0x080001b0   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    .ARM.Collect$$libinit$$00000011          0x080001b0   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    .ARM.Collect$$libinit$$00000013          0x080001b0   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    .ARM.Collect$$libinit$$00000015          0x080001b0   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    .ARM.Collect$$libinit$$00000017          0x080001b0   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    .ARM.Collect$$libinit$$00000019          0x080001b0   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    .ARM.Collect$$libinit$$0000001B          0x080001b0   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    .ARM.Collect$$libinit$$0000001D          0x080001b0   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    .ARM.Collect$$libinit$$0000001F          0x080001b0   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    .ARM.Collect$$libinit$$00000021          0x080001b0   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    .ARM.Collect$$libinit$$00000023          0x080001b0   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    .ARM.Collect$$libinit$$00000025          0x080001b0   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    .ARM.Collect$$libinit$$0000002C          0x080001b0   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    .ARM.Collect$$libinit$$0000002E          0x080001b0   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    .ARM.Collect$$libinit$$00000030          0x080001b0   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    .ARM.Collect$$libinit$$00000032          0x080001b0   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    .ARM.Collect$$libinit$$00000033          0x080001b0   Section        2  libinit2.o(.ARM.Collect$$libinit$$00000033)
    .ARM.Collect$$libshutdown$$00000000      0x080001b2   Section        2  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    .ARM.Collect$$libshutdown$$00000002      0x080001b4   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    .ARM.Collect$$libshutdown$$00000004      0x080001b4   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    .ARM.Collect$$libshutdown$$00000006      0x080001b4   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000006)
    .ARM.Collect$$libshutdown$$00000009      0x080001b4   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000009)
    .ARM.Collect$$libshutdown$$0000000C      0x080001b4   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    .ARM.Collect$$libshutdown$$0000000E      0x080001b4   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E)
    .ARM.Collect$$libshutdown$$00000011      0x080001b4   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000011)
    .ARM.Collect$$libshutdown$$00000012      0x080001b4   Section        2  libshutdown2.o(.ARM.Collect$$libshutdown$$00000012)
    .ARM.Collect$$rtentry$$00000000          0x080001b6   Section        0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    .ARM.Collect$$rtentry$$00000002          0x080001b6   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    .ARM.Collect$$rtentry$$00000004          0x080001b6   Section        6  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    .ARM.Collect$$rtentry$$00000009          0x080001bc   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    .ARM.Collect$$rtentry$$0000000A          0x080001bc   Section        4  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    .ARM.Collect$$rtentry$$0000000C          0x080001c0   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    .ARM.Collect$$rtentry$$0000000D          0x080001c0   Section        8  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    .ARM.Collect$$rtexit$$00000000           0x080001c8   Section        2  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    .ARM.Collect$$rtexit$$00000002           0x080001ca   Section        0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    .ARM.Collect$$rtexit$$00000003           0x080001ca   Section        4  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    .ARM.Collect$$rtexit$$00000004           0x080001ce   Section        6  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    .text                                    0x080001d4   Section        0  main.o(.text)
    .text                                    0x08000288   Section        0  stm32f10x_it.o(.text)
    .text                                    0x080002a4   Section        0  system_stm32f10x.o(.text)
    SetSysClockTo72                          0x08000313   Thumb Code   214  system_stm32f10x.o(.text)
    SetSysClock                              0x080003e9   Thumb Code     8  system_stm32f10x.o(.text)
    .text                                    0x080004f0   Section        0  led.o(.text)
    .text                                    0x08000558   Section        0  wdg.o(.text)
    .text                                    0x08000584   Section        0  ec800.o(.text)
    .text                                    0x080008c8   Section        0  stm32f10x_gpio.o(.text)
    .text                                    0x08000c24   Section        0  stm32f10x_iwdg.o(.text)
    .text                                    0x08000c64   Section        0  stm32f10x_usart.o(.text)
    .text                                    0x0800106c   Section        0  stm32f10x_rcc.o(.text)
    .text                                    0x08001410   Section        0  stm32f10x_flash.o(.text)
    .text                                    0x080019cc   Section        0  misc.o(.text)
    .text                                    0x08001aa8   Section       64  startup_stm32f10x_hd.o(.text)
    .text                                    0x08001ae8   Section        0  usart.o(.text)
    .text                                    0x08001c98   Section        0  delay.o(.text)
    .text                                    0x08001d6c   Section        0  sys.o(.text)
    .text                                    0x08001d78   Section        2  use_no_semi_2.o(.text)
    .text                                    0x08001d7c   Section        0  noretval__2printf.o(.text)
    .text                                    0x08001d94   Section        0  __printf.o(.text)
    .text                                    0x08001dfc   Section        0  _printf_str.o(.text)
    .text                                    0x08001e4e   Section        0  strstr.o(.text)
    .text                                    0x08001e72   Section      100  rt_memcpy_w.o(.text)
    .text                                    0x08001ed6   Section        0  heapauxi.o(.text)
    .text                                    0x08001edc   Section        2  use_no_semi.o(.text)
    .text                                    0x08001ede   Section        0  _printf_char.o(.text)
    .text                                    0x08001f0c   Section        0  _printf_char_file.o(.text)
    .text                                    0x08001f30   Section        0  _printf_char_common.o(.text)
    _printf_input_char                       0x08001f31   Thumb Code    10  _printf_char_common.o(.text)
    .text                                    0x08001f60   Section        0  ferror.o(.text)
    .text                                    0x08001f68   Section       74  sys_stackheap_outer.o(.text)
    .text                                    0x08001fb2   Section        0  exit.o(.text)
    .text                                    0x08001fc4   Section        8  libspace.o(.text)
    .data                                    0x20000000   Section       20  system_stm32f10x.o(.data)
    .data                                    0x20000014   Section       16  ec800.o(.data)
    .data                                    0x20000024   Section       20  stm32f10x_rcc.o(.data)
    APBAHBPrescTable                         0x20000024   Data          16  stm32f10x_rcc.o(.data)
    ADCPrescTable                            0x20000034   Data           4  stm32f10x_rcc.o(.data)
    .data                                    0x20000038   Section        8  usart.o(.data)
    .data                                    0x20000040   Section        4  delay.o(.data)
    fac_us                                   0x20000040   Data           1  delay.o(.data)
    fac_ms                                   0x20000042   Data           2  delay.o(.data)
    .bss                                     0x20000044   Section      100  usart.o(.bss)
    .bss                                     0x200000a8   Section       96  libspace.o(.bss)
    HEAP                                     0x20000108   Section      512  startup_stm32f10x_hd.o(HEAP)
    Heap_Mem                                 0x20000108   Data         512  startup_stm32f10x_hd.o(HEAP)
    STACK                                    0x20000308   Section     1024  startup_stm32f10x_hd.o(STACK)
    Stack_Mem                                0x20000308   Data        1024  startup_stm32f10x_hd.o(STACK)
    __initial_sp                             0x20000708   Data           0  startup_stm32f10x_hd.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$P$D$K$B$S$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OSPACE$ROPI$EBA8$UX$STANDARDLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    __ARM_exceptions_init                     - Undefined Weak Reference
    __alloca_initialize                       - Undefined Weak Reference
    __arm_fini_                               - Undefined Weak Reference
    __arm_preinit_                            - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __rt_locale                               - Undefined Weak Reference
    __sigvec_lookup                           - Undefined Weak Reference
    _atexit_init                              - Undefined Weak Reference
    _call_atexit_fns                          - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _fp_trap_init                             - Undefined Weak Reference
    _fp_trap_shutdown                         - Undefined Weak Reference
    _get_lc_collate                           - Undefined Weak Reference
    _get_lc_ctype                             - Undefined Weak Reference
    _get_lc_monetary                          - Undefined Weak Reference
    _get_lc_numeric                           - Undefined Weak Reference
    _get_lc_time                              - Undefined Weak Reference
    _getenv_init                              - Undefined Weak Reference
    _handle_redirection                       - Undefined Weak Reference
    _init_alloc                               - Undefined Weak Reference
    _init_user_alloc                          - Undefined Weak Reference
    _initio                                   - Undefined Weak Reference
    _mutex_acquire                            - Undefined Weak Reference
    _mutex_release                            - Undefined Weak Reference
    _printf_mbtowc                            - Undefined Weak Reference
    _printf_post_padding                      - Undefined Weak Reference
    _printf_pre_padding                       - Undefined Weak Reference
    _rand_init                                - Undefined Weak Reference
    _signal_finish                            - Undefined Weak Reference
    _signal_init                              - Undefined Weak Reference
    _terminate_alloc                          - Undefined Weak Reference
    _terminate_user_alloc                     - Undefined Weak Reference
    _terminateio                              - Undefined Weak Reference
    __Vectors_Size                           0x00000130   Number         0  startup_stm32f10x_hd.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f10x_hd.o(RESET)
    __Vectors_End                            0x08000130   Data           0  startup_stm32f10x_hd.o(RESET)
    __main                                   0x08000131   Thumb Code     8  __main.o(!!!main)
    __scatterload                            0x08000139   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_rt2                        0x08000139   Thumb Code    44  __scatter.o(!!!scatter)
    __scatterload_rt2_thumb_only             0x08000139   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_null                       0x08000147   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_copy                       0x0800016d   Thumb Code    26  __scatter_copy.o(!!handler_copy)
    __scatterload_zeroinit                   0x08000189   Thumb Code    28  __scatter_zi.o(!!handler_zi)
    _printf_percent                          0x080001a5   Thumb Code     0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    _printf_s                                0x080001a5   Thumb Code     0  _printf_s.o(.ARM.Collect$$_printf_percent$$00000014)
    _printf_percent_end                      0x080001ab   Thumb Code     0  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    __rt_lib_init                            0x080001af   Thumb Code     0  libinit.o(.ARM.Collect$$libinit$$00000000)
    __rt_lib_init_alloca_1                   0x080001b1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    __rt_lib_init_argv_1                     0x080001b1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    __rt_lib_init_atexit_1                   0x080001b1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    __rt_lib_init_clock_1                    0x080001b1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    __rt_lib_init_cpp_1                      0x080001b1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    __rt_lib_init_exceptions_1               0x080001b1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    __rt_lib_init_fp_1                       0x080001b1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000002)
    __rt_lib_init_fp_trap_1                  0x080001b1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    __rt_lib_init_getenv_1                   0x080001b1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    __rt_lib_init_heap_1                     0x080001b1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    __rt_lib_init_lc_collate_1               0x080001b1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    __rt_lib_init_lc_ctype_1                 0x080001b1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    __rt_lib_init_lc_monetary_1              0x080001b1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    __rt_lib_init_lc_numeric_1               0x080001b1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    __rt_lib_init_lc_time_1                  0x080001b1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    __rt_lib_init_preinit_1                  0x080001b1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    __rt_lib_init_rand_1                     0x080001b1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    __rt_lib_init_return                     0x080001b1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000033)
    __rt_lib_init_signal_1                   0x080001b1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    __rt_lib_init_stdio_1                    0x080001b1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    __rt_lib_init_user_alloc_1               0x080001b1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    __rt_lib_shutdown                        0x080001b3   Thumb Code     0  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    __rt_lib_shutdown_cpp_1                  0x080001b5   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    __rt_lib_shutdown_fini_1                 0x080001b5   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    __rt_lib_shutdown_fp_trap_1              0x080001b5   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000009)
    __rt_lib_shutdown_heap_1                 0x080001b5   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000011)
    __rt_lib_shutdown_return                 0x080001b5   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000012)
    __rt_lib_shutdown_signal_1               0x080001b5   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    __rt_lib_shutdown_stdio_1                0x080001b5   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000006)
    __rt_lib_shutdown_user_alloc_1           0x080001b5   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E)
    __rt_entry                               0x080001b7   Thumb Code     0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    __rt_entry_presh_1                       0x080001b7   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    __rt_entry_sh                            0x080001b7   Thumb Code     0  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    __rt_entry_li                            0x080001bd   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    __rt_entry_postsh_1                      0x080001bd   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    __rt_entry_main                          0x080001c1   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    __rt_entry_postli_1                      0x080001c1   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    __rt_exit                                0x080001c9   Thumb Code     0  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    __rt_exit_ls                             0x080001cb   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    __rt_exit_prels_1                        0x080001cb   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    __rt_exit_exit                           0x080001cf   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    main                                     0x080001d5   Thumb Code   126  main.o(.text)
    NMI_Handler                              0x08000289   Thumb Code     2  stm32f10x_it.o(.text)
    HardFault_Handler                        0x0800028b   Thumb Code     4  stm32f10x_it.o(.text)
    MemManage_Handler                        0x0800028f   Thumb Code     4  stm32f10x_it.o(.text)
    BusFault_Handler                         0x08000293   Thumb Code     4  stm32f10x_it.o(.text)
    UsageFault_Handler                       0x08000297   Thumb Code     4  stm32f10x_it.o(.text)
    SVC_Handler                              0x0800029b   Thumb Code     2  stm32f10x_it.o(.text)
    DebugMon_Handler                         0x0800029d   Thumb Code     2  stm32f10x_it.o(.text)
    PendSV_Handler                           0x0800029f   Thumb Code     2  stm32f10x_it.o(.text)
    SysTick_Handler                          0x080002a1   Thumb Code     2  stm32f10x_it.o(.text)
    RCC_Configuration                        0x080002a5   Thumb Code   110  system_stm32f10x.o(.text)
    SystemInit                               0x080003f1   Thumb Code    78  system_stm32f10x.o(.text)
    SystemCoreClockUpdate                    0x0800043f   Thumb Code   142  system_stm32f10x.o(.text)
    LED_Init                                 0x080004f1   Thumb Code    90  led.o(.text)
    IWDG_Init                                0x08000559   Thumb Code    36  wdg.o(.text)
    IWDG_Feed                                0x0800057d   Thumb Code     8  wdg.o(.text)
    Uart1_SendStr                            0x08000585   Thumb Code    34  ec800.o(.text)
    Clear_Buffer                             0x080005a7   Thumb Code    42  ec800.o(.text)
    EC800_Init                               0x080005d1   Thumb Code   300  ec800.o(.text)
    EC800Send_StrData                        0x080006fd   Thumb Code   110  ec800.o(.text)
    Send_Text                                0x0800076b   Thumb Code   138  ec800.o(.text)
    GPIO_DeInit                              0x080008c9   Thumb Code   172  stm32f10x_gpio.o(.text)
    GPIO_AFIODeInit                          0x08000975   Thumb Code    20  stm32f10x_gpio.o(.text)
    GPIO_Init                                0x08000989   Thumb Code   278  stm32f10x_gpio.o(.text)
    GPIO_StructInit                          0x08000a9f   Thumb Code    16  stm32f10x_gpio.o(.text)
    GPIO_ReadInputDataBit                    0x08000aaf   Thumb Code    18  stm32f10x_gpio.o(.text)
    GPIO_ReadInputData                       0x08000ac1   Thumb Code     8  stm32f10x_gpio.o(.text)
    GPIO_ReadOutputDataBit                   0x08000ac9   Thumb Code    18  stm32f10x_gpio.o(.text)
    GPIO_ReadOutputData                      0x08000adb   Thumb Code     8  stm32f10x_gpio.o(.text)
    GPIO_SetBits                             0x08000ae3   Thumb Code     4  stm32f10x_gpio.o(.text)
    GPIO_ResetBits                           0x08000ae7   Thumb Code     4  stm32f10x_gpio.o(.text)
    GPIO_WriteBit                            0x08000aeb   Thumb Code    10  stm32f10x_gpio.o(.text)
    GPIO_Write                               0x08000af5   Thumb Code     4  stm32f10x_gpio.o(.text)
    GPIO_PinLockConfig                       0x08000af9   Thumb Code    18  stm32f10x_gpio.o(.text)
    GPIO_EventOutputConfig                   0x08000b0b   Thumb Code    26  stm32f10x_gpio.o(.text)
    GPIO_EventOutputCmd                      0x08000b25   Thumb Code     6  stm32f10x_gpio.o(.text)
    GPIO_PinRemapConfig                      0x08000b2b   Thumb Code   138  stm32f10x_gpio.o(.text)
    GPIO_EXTILineConfig                      0x08000bb5   Thumb Code    66  stm32f10x_gpio.o(.text)
    GPIO_ETH_MediaInterfaceConfig            0x08000bf7   Thumb Code     8  stm32f10x_gpio.o(.text)
    IWDG_WriteAccessCmd                      0x08000c25   Thumb Code     6  stm32f10x_iwdg.o(.text)
    IWDG_SetPrescaler                        0x08000c2b   Thumb Code     6  stm32f10x_iwdg.o(.text)
    IWDG_SetReload                           0x08000c31   Thumb Code     6  stm32f10x_iwdg.o(.text)
    IWDG_ReloadCounter                       0x08000c37   Thumb Code    10  stm32f10x_iwdg.o(.text)
    IWDG_Enable                              0x08000c41   Thumb Code    10  stm32f10x_iwdg.o(.text)
    IWDG_GetFlagStatus                       0x08000c4b   Thumb Code    20  stm32f10x_iwdg.o(.text)
    USART_DeInit                             0x08000c65   Thumb Code   134  stm32f10x_usart.o(.text)
    USART_Init                               0x08000ceb   Thumb Code   210  stm32f10x_usart.o(.text)
    USART_StructInit                         0x08000dbd   Thumb Code    24  stm32f10x_usart.o(.text)
    USART_ClockInit                          0x08000dd5   Thumb Code    34  stm32f10x_usart.o(.text)
    USART_ClockStructInit                    0x08000df7   Thumb Code    12  stm32f10x_usart.o(.text)
    USART_Cmd                                0x08000e03   Thumb Code    24  stm32f10x_usart.o(.text)
    USART_ITConfig                           0x08000e1b   Thumb Code    74  stm32f10x_usart.o(.text)
    USART_DMACmd                             0x08000e65   Thumb Code    18  stm32f10x_usart.o(.text)
    USART_SetAddress                         0x08000e77   Thumb Code    18  stm32f10x_usart.o(.text)
    USART_WakeUpConfig                       0x08000e89   Thumb Code    18  stm32f10x_usart.o(.text)
    USART_ReceiverWakeUpCmd                  0x08000e9b   Thumb Code    24  stm32f10x_usart.o(.text)
    USART_LINBreakDetectLengthConfig         0x08000eb3   Thumb Code    18  stm32f10x_usart.o(.text)
    USART_LINCmd                             0x08000ec5   Thumb Code    24  stm32f10x_usart.o(.text)
    USART_SendData                           0x08000edd   Thumb Code     8  stm32f10x_usart.o(.text)
    USART_ReceiveData                        0x08000ee5   Thumb Code    10  stm32f10x_usart.o(.text)
    USART_SendBreak                          0x08000eef   Thumb Code    10  stm32f10x_usart.o(.text)
    USART_SetGuardTime                       0x08000ef9   Thumb Code    16  stm32f10x_usart.o(.text)
    USART_SetPrescaler                       0x08000f09   Thumb Code    16  stm32f10x_usart.o(.text)
    USART_SmartCardCmd                       0x08000f19   Thumb Code    24  stm32f10x_usart.o(.text)
    USART_SmartCardNACKCmd                   0x08000f31   Thumb Code    24  stm32f10x_usart.o(.text)
    USART_HalfDuplexCmd                      0x08000f49   Thumb Code    24  stm32f10x_usart.o(.text)
    USART_OverSampling8Cmd                   0x08000f61   Thumb Code    22  stm32f10x_usart.o(.text)
    USART_OneBitMethodCmd                    0x08000f77   Thumb Code    24  stm32f10x_usart.o(.text)
    USART_IrDAConfig                         0x08000f8f   Thumb Code    18  stm32f10x_usart.o(.text)
    USART_IrDACmd                            0x08000fa1   Thumb Code    24  stm32f10x_usart.o(.text)
    USART_GetFlagStatus                      0x08000fb9   Thumb Code    26  stm32f10x_usart.o(.text)
    USART_ClearFlag                          0x08000fd3   Thumb Code    18  stm32f10x_usart.o(.text)
    USART_GetITStatus                        0x08000fe5   Thumb Code    84  stm32f10x_usart.o(.text)
    USART_ClearITPendingBit                  0x08001039   Thumb Code    52  stm32f10x_usart.o(.text)
    RCC_DeInit                               0x0800106d   Thumb Code    64  stm32f10x_rcc.o(.text)
    RCC_HSEConfig                            0x080010ad   Thumb Code    70  stm32f10x_rcc.o(.text)
    RCC_GetFlagStatus                        0x080010f3   Thumb Code    56  stm32f10x_rcc.o(.text)
    RCC_WaitForHSEStartUp                    0x0800112b   Thumb Code    56  stm32f10x_rcc.o(.text)
    RCC_AdjustHSICalibrationValue            0x08001163   Thumb Code    20  stm32f10x_rcc.o(.text)
    RCC_HSICmd                               0x08001177   Thumb Code     6  stm32f10x_rcc.o(.text)
    RCC_PLLConfig                            0x0800117d   Thumb Code    24  stm32f10x_rcc.o(.text)
    RCC_PLLCmd                               0x08001195   Thumb Code     6  stm32f10x_rcc.o(.text)
    RCC_SYSCLKConfig                         0x0800119b   Thumb Code    18  stm32f10x_rcc.o(.text)
    RCC_GetSYSCLKSource                      0x080011ad   Thumb Code    10  stm32f10x_rcc.o(.text)
    RCC_HCLKConfig                           0x080011b7   Thumb Code    18  stm32f10x_rcc.o(.text)
    RCC_PCLK1Config                          0x080011c9   Thumb Code    18  stm32f10x_rcc.o(.text)
    RCC_PCLK2Config                          0x080011db   Thumb Code    20  stm32f10x_rcc.o(.text)
    RCC_ITConfig                             0x080011ef   Thumb Code    26  stm32f10x_rcc.o(.text)
    RCC_USBCLKConfig                         0x08001209   Thumb Code     8  stm32f10x_rcc.o(.text)
    RCC_ADCCLKConfig                         0x08001211   Thumb Code    18  stm32f10x_rcc.o(.text)
    RCC_LSEConfig                            0x08001223   Thumb Code    50  stm32f10x_rcc.o(.text)
    RCC_LSICmd                               0x08001255   Thumb Code     6  stm32f10x_rcc.o(.text)
    RCC_RTCCLKConfig                         0x0800125b   Thumb Code    12  stm32f10x_rcc.o(.text)
    RCC_RTCCLKCmd                            0x08001267   Thumb Code     8  stm32f10x_rcc.o(.text)
    RCC_GetClocksFreq                        0x0800126f   Thumb Code   192  stm32f10x_rcc.o(.text)
    RCC_AHBPeriphClockCmd                    0x0800132f   Thumb Code    26  stm32f10x_rcc.o(.text)
    RCC_APB2PeriphClockCmd                   0x08001349   Thumb Code    26  stm32f10x_rcc.o(.text)
    RCC_APB1PeriphClockCmd                   0x08001363   Thumb Code    26  stm32f10x_rcc.o(.text)
    RCC_APB2PeriphResetCmd                   0x0800137d   Thumb Code    26  stm32f10x_rcc.o(.text)
    RCC_APB1PeriphResetCmd                   0x08001397   Thumb Code    26  stm32f10x_rcc.o(.text)
    RCC_BackupResetCmd                       0x080013b1   Thumb Code     8  stm32f10x_rcc.o(.text)
    RCC_ClockSecuritySystemCmd               0x080013b9   Thumb Code     6  stm32f10x_rcc.o(.text)
    RCC_MCOConfig                            0x080013bf   Thumb Code     6  stm32f10x_rcc.o(.text)
    RCC_ClearFlag                            0x080013c5   Thumb Code    14  stm32f10x_rcc.o(.text)
    RCC_GetITStatus                          0x080013d3   Thumb Code    20  stm32f10x_rcc.o(.text)
    RCC_ClearITPendingBit                    0x080013e7   Thumb Code     6  stm32f10x_rcc.o(.text)
    FLASH_SetLatency                         0x08001411   Thumb Code    18  stm32f10x_flash.o(.text)
    FLASH_HalfCycleAccessCmd                 0x08001423   Thumb Code    22  stm32f10x_flash.o(.text)
    FLASH_PrefetchBufferCmd                  0x08001439   Thumb Code    22  stm32f10x_flash.o(.text)
    FLASH_Unlock                             0x0800144f   Thumb Code    12  stm32f10x_flash.o(.text)
    FLASH_UnlockBank1                        0x0800145b   Thumb Code    12  stm32f10x_flash.o(.text)
    FLASH_Lock                               0x08001467   Thumb Code    14  stm32f10x_flash.o(.text)
    FLASH_LockBank1                          0x08001475   Thumb Code    14  stm32f10x_flash.o(.text)
    FLASH_GetBank1Status                     0x08001483   Thumb Code    48  stm32f10x_flash.o(.text)
    FLASH_WaitForLastOperation               0x080014b3   Thumb Code    38  stm32f10x_flash.o(.text)
    FLASH_ErasePage                          0x080014d9   Thumb Code    72  stm32f10x_flash.o(.text)
    FLASH_EraseAllPages                      0x08001521   Thumb Code    68  stm32f10x_flash.o(.text)
    FLASH_WaitForLastBank1Operation          0x08001565   Thumb Code    38  stm32f10x_flash.o(.text)
    FLASH_EraseAllBank1Pages                 0x0800158b   Thumb Code    68  stm32f10x_flash.o(.text)
    FLASH_GetReadOutProtectionStatus         0x080015cf   Thumb Code    20  stm32f10x_flash.o(.text)
    FLASH_EraseOptionBytes                   0x080015e3   Thumb Code   150  stm32f10x_flash.o(.text)
    FLASH_ProgramWord                        0x08001679   Thumb Code   102  stm32f10x_flash.o(.text)
    FLASH_ProgramHalfWord                    0x080016df   Thumb Code    60  stm32f10x_flash.o(.text)
    FLASH_ProgramOptionByteData              0x0800171b   Thumb Code    72  stm32f10x_flash.o(.text)
    FLASH_EnableWriteProtection              0x08001763   Thumb Code   200  stm32f10x_flash.o(.text)
    FLASH_ReadOutProtection                  0x0800182b   Thumb Code   156  stm32f10x_flash.o(.text)
    FLASH_UserOptionByteConfig               0x080018c7   Thumb Code    88  stm32f10x_flash.o(.text)
    FLASH_GetUserOptionByte                  0x0800191f   Thumb Code     8  stm32f10x_flash.o(.text)
    FLASH_GetWriteProtectionOptionByte       0x08001927   Thumb Code     6  stm32f10x_flash.o(.text)
    FLASH_GetPrefetchBufferStatus            0x0800192d   Thumb Code    20  stm32f10x_flash.o(.text)
    FLASH_ITConfig                           0x08001941   Thumb Code    26  stm32f10x_flash.o(.text)
    FLASH_GetFlagStatus                      0x0800195b   Thumb Code    42  stm32f10x_flash.o(.text)
    FLASH_ClearFlag                          0x08001985   Thumb Code     6  stm32f10x_flash.o(.text)
    FLASH_GetStatus                          0x0800198b   Thumb Code    48  stm32f10x_flash.o(.text)
    NVIC_PriorityGroupConfig                 0x080019cd   Thumb Code    10  misc.o(.text)
    NVIC_Init                                0x080019d7   Thumb Code   100  misc.o(.text)
    NVIC_SetVectorTable                      0x08001a3b   Thumb Code    14  misc.o(.text)
    NVIC_SystemLPConfig                      0x08001a49   Thumb Code    34  misc.o(.text)
    SysTick_CLKSourceConfig                  0x08001a6b   Thumb Code    40  misc.o(.text)
    Reset_Handler                            0x08001aa9   Thumb Code     8  startup_stm32f10x_hd.o(.text)
    ADC1_2_IRQHandler                        0x08001ac3   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    ADC3_IRQHandler                          0x08001ac3   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    CAN1_RX1_IRQHandler                      0x08001ac3   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    CAN1_SCE_IRQHandler                      0x08001ac3   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel1_IRQHandler                 0x08001ac3   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel2_IRQHandler                 0x08001ac3   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel3_IRQHandler                 0x08001ac3   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel4_IRQHandler                 0x08001ac3   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel5_IRQHandler                 0x08001ac3   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel6_IRQHandler                 0x08001ac3   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel7_IRQHandler                 0x08001ac3   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel1_IRQHandler                 0x08001ac3   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel2_IRQHandler                 0x08001ac3   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel3_IRQHandler                 0x08001ac3   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel4_5_IRQHandler               0x08001ac3   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI0_IRQHandler                         0x08001ac3   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI15_10_IRQHandler                     0x08001ac3   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI1_IRQHandler                         0x08001ac3   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI2_IRQHandler                         0x08001ac3   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI3_IRQHandler                         0x08001ac3   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI4_IRQHandler                         0x08001ac3   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI9_5_IRQHandler                       0x08001ac3   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    FLASH_IRQHandler                         0x08001ac3   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    FSMC_IRQHandler                          0x08001ac3   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C1_ER_IRQHandler                       0x08001ac3   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C1_EV_IRQHandler                       0x08001ac3   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C2_ER_IRQHandler                       0x08001ac3   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C2_EV_IRQHandler                       0x08001ac3   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    PVD_IRQHandler                           0x08001ac3   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    RCC_IRQHandler                           0x08001ac3   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    RTCAlarm_IRQHandler                      0x08001ac3   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    RTC_IRQHandler                           0x08001ac3   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SDIO_IRQHandler                          0x08001ac3   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SPI1_IRQHandler                          0x08001ac3   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SPI2_IRQHandler                          0x08001ac3   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SPI3_IRQHandler                          0x08001ac3   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TAMPER_IRQHandler                        0x08001ac3   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_BRK_IRQHandler                      0x08001ac3   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_CC_IRQHandler                       0x08001ac3   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_TRG_COM_IRQHandler                  0x08001ac3   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_UP_IRQHandler                       0x08001ac3   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM2_IRQHandler                          0x08001ac3   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM3_IRQHandler                          0x08001ac3   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM4_IRQHandler                          0x08001ac3   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM5_IRQHandler                          0x08001ac3   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM6_IRQHandler                          0x08001ac3   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM7_IRQHandler                          0x08001ac3   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_BRK_IRQHandler                      0x08001ac3   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_CC_IRQHandler                       0x08001ac3   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_TRG_COM_IRQHandler                  0x08001ac3   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_UP_IRQHandler                       0x08001ac3   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    UART4_IRQHandler                         0x08001ac3   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    UART5_IRQHandler                         0x08001ac3   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USART3_IRQHandler                        0x08001ac3   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USBWakeUp_IRQHandler                     0x08001ac3   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USB_HP_CAN1_TX_IRQHandler                0x08001ac3   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USB_LP_CAN1_RX0_IRQHandler               0x08001ac3   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    WWDG_IRQHandler                          0x08001ac3   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    __user_initial_stackheap                 0x08001ac5   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    _sys_exit                                0x08001ae9   Thumb Code     4  usart.o(.text)
    fputc                                    0x08001aed   Thumb Code    24  usart.o(.text)
    uart_init                                0x08001b05   Thumb Code   158  usart.o(.text)
    uart2_init                               0x08001ba3   Thumb Code   160  usart.o(.text)
    USART1_IRQHandler                        0x08001c43   Thumb Code    24  usart.o(.text)
    USART2_IRQHandler                        0x08001c5b   Thumb Code    40  usart.o(.text)
    delay_init                               0x08001c99   Thumb Code    50  delay.o(.text)
    delay_us                                 0x08001ccb   Thumb Code    72  delay.o(.text)
    delay_ms                                 0x08001d13   Thumb Code    72  delay.o(.text)
    NVIC_Configuration                       0x08001d6d   Thumb Code    12  sys.o(.text)
    __use_no_semihosting                     0x08001d79   Thumb Code     2  use_no_semi_2.o(.text)
    __2printf                                0x08001d7d   Thumb Code    20  noretval__2printf.o(.text)
    __printf                                 0x08001d95   Thumb Code   104  __printf.o(.text)
    _printf_str                              0x08001dfd   Thumb Code    82  _printf_str.o(.text)
    strstr                                   0x08001e4f   Thumb Code    36  strstr.o(.text)
    __aeabi_memcpy4                          0x08001e73   Thumb Code     0  rt_memcpy_w.o(.text)
    __aeabi_memcpy8                          0x08001e73   Thumb Code     0  rt_memcpy_w.o(.text)
    __rt_memcpy_w                            0x08001e73   Thumb Code   100  rt_memcpy_w.o(.text)
    _memcpy_lastbytes_aligned                0x08001ebb   Thumb Code     0  rt_memcpy_w.o(.text)
    __use_two_region_memory                  0x08001ed7   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_escrow$2region                 0x08001ed9   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_expand$2region                 0x08001edb   Thumb Code     2  heapauxi.o(.text)
    __I$use$semihosting                      0x08001edd   Thumb Code     0  use_no_semi.o(.text)
    __use_no_semihosting_swi                 0x08001edd   Thumb Code     2  use_no_semi.o(.text)
    _printf_cs_common                        0x08001edf   Thumb Code    20  _printf_char.o(.text)
    _printf_char                             0x08001ef3   Thumb Code    16  _printf_char.o(.text)
    _printf_string                           0x08001f03   Thumb Code     8  _printf_char.o(.text)
    _printf_char_file                        0x08001f0d   Thumb Code    32  _printf_char_file.o(.text)
    _printf_char_common                      0x08001f3b   Thumb Code    32  _printf_char_common.o(.text)
    ferror                                   0x08001f61   Thumb Code     8  ferror.o(.text)
    __user_setup_stackheap                   0x08001f69   Thumb Code    74  sys_stackheap_outer.o(.text)
    exit                                     0x08001fb3   Thumb Code    18  exit.o(.text)
    __user_libspace                          0x08001fc5   Thumb Code     8  libspace.o(.text)
    __user_perproc_libspace                  0x08001fc5   Thumb Code     0  libspace.o(.text)
    __user_perthread_libspace                0x08001fc5   Thumb Code     0  libspace.o(.text)
    Region$$Table$$Base                      0x08001fcc   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x08001fec   Number         0  anon$$obj.o(Region$$Table)
    SystemCoreClock                          0x20000000   Data           4  system_stm32f10x.o(.data)
    AHBPrescTable                            0x20000004   Data          16  system_stm32f10x.o(.data)
    strx                                     0x20000014   Data           4  ec800.o(.data)
    extstrx                                  0x20000018   Data           4  ec800.o(.data)
    Readystrx                                0x2000001c   Data           4  ec800.o(.data)
    Errstrx                                  0x20000020   Data           4  ec800.o(.data)
    __stdout                                 0x20000038   Data           4  usart.o(.data)
    RxCounter                                0x2000003c   Data           1  usart.o(.data)
    USART_RX_STA                             0x2000003e   Data           2  usart.o(.data)
    RxBuffer                                 0x20000044   Data         100  usart.o(.bss)
    __libspace_start                         0x200000a8   Data          96  libspace.o(.bss)
    __temporary_stack_top$libspace           0x20000108   Data           0  libspace.o(.bss)



==============================================================================

Memory Map of the image

  Image Entry point : 0x08000131

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x00002030, Max: 0x00010000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x00001fec, Max: 0x00010000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x00000130   Data   RO          378    RESET               startup_stm32f10x_hd.o
    0x08000130   0x08000130   0x00000008   Code   RO          472  * !!!main             c_w.l(__main.o)
    0x08000138   0x08000138   0x00000034   Code   RO          642    !!!scatter          c_w.l(__scatter.o)
    0x0800016c   0x0800016c   0x0000001a   Code   RO          644    !!handler_copy      c_w.l(__scatter_copy.o)
    0x08000186   0x08000186   0x00000002   PAD
    0x08000188   0x08000188   0x0000001c   Code   RO          646    !!handler_zi        c_w.l(__scatter_zi.o)
    0x080001a4   0x080001a4   0x00000000   Code   RO          465    .ARM.Collect$$_printf_percent$$00000000  c_w.l(_printf_percent.o)
    0x080001a4   0x080001a4   0x00000006   Code   RO          464    .ARM.Collect$$_printf_percent$$00000014  c_w.l(_printf_s.o)
    0x080001aa   0x080001aa   0x00000004   Code   RO          481    .ARM.Collect$$_printf_percent$$00000017  c_w.l(_printf_percent_end.o)
    0x080001ae   0x080001ae   0x00000002   Code   RO          514    .ARM.Collect$$libinit$$00000000  c_w.l(libinit.o)
    0x080001b0   0x080001b0   0x00000000   Code   RO          521    .ARM.Collect$$libinit$$00000002  c_w.l(libinit2.o)
    0x080001b0   0x080001b0   0x00000000   Code   RO          523    .ARM.Collect$$libinit$$00000004  c_w.l(libinit2.o)
    0x080001b0   0x080001b0   0x00000000   Code   RO          526    .ARM.Collect$$libinit$$0000000A  c_w.l(libinit2.o)
    0x080001b0   0x080001b0   0x00000000   Code   RO          528    .ARM.Collect$$libinit$$0000000C  c_w.l(libinit2.o)
    0x080001b0   0x080001b0   0x00000000   Code   RO          530    .ARM.Collect$$libinit$$0000000E  c_w.l(libinit2.o)
    0x080001b0   0x080001b0   0x00000000   Code   RO          533    .ARM.Collect$$libinit$$00000011  c_w.l(libinit2.o)
    0x080001b0   0x080001b0   0x00000000   Code   RO          535    .ARM.Collect$$libinit$$00000013  c_w.l(libinit2.o)
    0x080001b0   0x080001b0   0x00000000   Code   RO          537    .ARM.Collect$$libinit$$00000015  c_w.l(libinit2.o)
    0x080001b0   0x080001b0   0x00000000   Code   RO          539    .ARM.Collect$$libinit$$00000017  c_w.l(libinit2.o)
    0x080001b0   0x080001b0   0x00000000   Code   RO          541    .ARM.Collect$$libinit$$00000019  c_w.l(libinit2.o)
    0x080001b0   0x080001b0   0x00000000   Code   RO          543    .ARM.Collect$$libinit$$0000001B  c_w.l(libinit2.o)
    0x080001b0   0x080001b0   0x00000000   Code   RO          545    .ARM.Collect$$libinit$$0000001D  c_w.l(libinit2.o)
    0x080001b0   0x080001b0   0x00000000   Code   RO          547    .ARM.Collect$$libinit$$0000001F  c_w.l(libinit2.o)
    0x080001b0   0x080001b0   0x00000000   Code   RO          549    .ARM.Collect$$libinit$$00000021  c_w.l(libinit2.o)
    0x080001b0   0x080001b0   0x00000000   Code   RO          551    .ARM.Collect$$libinit$$00000023  c_w.l(libinit2.o)
    0x080001b0   0x080001b0   0x00000000   Code   RO          553    .ARM.Collect$$libinit$$00000025  c_w.l(libinit2.o)
    0x080001b0   0x080001b0   0x00000000   Code   RO          557    .ARM.Collect$$libinit$$0000002C  c_w.l(libinit2.o)
    0x080001b0   0x080001b0   0x00000000   Code   RO          559    .ARM.Collect$$libinit$$0000002E  c_w.l(libinit2.o)
    0x080001b0   0x080001b0   0x00000000   Code   RO          561    .ARM.Collect$$libinit$$00000030  c_w.l(libinit2.o)
    0x080001b0   0x080001b0   0x00000000   Code   RO          563    .ARM.Collect$$libinit$$00000032  c_w.l(libinit2.o)
    0x080001b0   0x080001b0   0x00000002   Code   RO          564    .ARM.Collect$$libinit$$00000033  c_w.l(libinit2.o)
    0x080001b2   0x080001b2   0x00000002   Code   RO          582    .ARM.Collect$$libshutdown$$00000000  c_w.l(libshutdown.o)
    0x080001b4   0x080001b4   0x00000000   Code   RO          592    .ARM.Collect$$libshutdown$$00000002  c_w.l(libshutdown2.o)
    0x080001b4   0x080001b4   0x00000000   Code   RO          594    .ARM.Collect$$libshutdown$$00000004  c_w.l(libshutdown2.o)
    0x080001b4   0x080001b4   0x00000000   Code   RO          596    .ARM.Collect$$libshutdown$$00000006  c_w.l(libshutdown2.o)
    0x080001b4   0x080001b4   0x00000000   Code   RO          599    .ARM.Collect$$libshutdown$$00000009  c_w.l(libshutdown2.o)
    0x080001b4   0x080001b4   0x00000000   Code   RO          602    .ARM.Collect$$libshutdown$$0000000C  c_w.l(libshutdown2.o)
    0x080001b4   0x080001b4   0x00000000   Code   RO          604    .ARM.Collect$$libshutdown$$0000000E  c_w.l(libshutdown2.o)
    0x080001b4   0x080001b4   0x00000000   Code   RO          607    .ARM.Collect$$libshutdown$$00000011  c_w.l(libshutdown2.o)
    0x080001b4   0x080001b4   0x00000002   Code   RO          608    .ARM.Collect$$libshutdown$$00000012  c_w.l(libshutdown2.o)
    0x080001b6   0x080001b6   0x00000000   Code   RO          476    .ARM.Collect$$rtentry$$00000000  c_w.l(__rtentry.o)
    0x080001b6   0x080001b6   0x00000000   Code   RO          483    .ARM.Collect$$rtentry$$00000002  c_w.l(__rtentry2.o)
    0x080001b6   0x080001b6   0x00000006   Code   RO          495    .ARM.Collect$$rtentry$$00000004  c_w.l(__rtentry4.o)
    0x080001bc   0x080001bc   0x00000000   Code   RO          485    .ARM.Collect$$rtentry$$00000009  c_w.l(__rtentry2.o)
    0x080001bc   0x080001bc   0x00000004   Code   RO          486    .ARM.Collect$$rtentry$$0000000A  c_w.l(__rtentry2.o)
    0x080001c0   0x080001c0   0x00000000   Code   RO          488    .ARM.Collect$$rtentry$$0000000C  c_w.l(__rtentry2.o)
    0x080001c0   0x080001c0   0x00000008   Code   RO          489    .ARM.Collect$$rtentry$$0000000D  c_w.l(__rtentry2.o)
    0x080001c8   0x080001c8   0x00000002   Code   RO          518    .ARM.Collect$$rtexit$$00000000  c_w.l(rtexit.o)
    0x080001ca   0x080001ca   0x00000000   Code   RO          566    .ARM.Collect$$rtexit$$00000002  c_w.l(rtexit2.o)
    0x080001ca   0x080001ca   0x00000004   Code   RO          567    .ARM.Collect$$rtexit$$00000003  c_w.l(rtexit2.o)
    0x080001ce   0x080001ce   0x00000006   Code   RO          568    .ARM.Collect$$rtexit$$00000004  c_w.l(rtexit2.o)
    0x080001d4   0x080001d4   0x000000b4   Code   RO            1    .text               main.o
    0x08000288   0x08000288   0x0000001a   Code   RO           88    .text               stm32f10x_it.o
    0x080002a2   0x080002a2   0x00000002   PAD
    0x080002a4   0x080002a4   0x0000024c   Code   RO          135    .text               system_stm32f10x.o
    0x080004f0   0x080004f0   0x00000068   Code   RO          156    .text               led.o
    0x08000558   0x08000558   0x0000002c   Code   RO          168    .text               wdg.o
    0x08000584   0x08000584   0x00000344   Code   RO          209    .text               ec800.o
    0x080008c8   0x080008c8   0x0000035c   Code   RO          291    .text               stm32f10x_gpio.o
    0x08000c24   0x08000c24   0x00000040   Code   RO          303    .text               stm32f10x_iwdg.o
    0x08000c64   0x08000c64   0x00000408   Code   RO          315    .text               stm32f10x_usart.o
    0x0800106c   0x0800106c   0x000003a4   Code   RO          327    .text               stm32f10x_rcc.o
    0x08001410   0x08001410   0x000005bc   Code   RO          341    .text               stm32f10x_flash.o
    0x080019cc   0x080019cc   0x000000dc   Code   RO          353    .text               misc.o
    0x08001aa8   0x08001aa8   0x00000040   Code   RO          379    .text               startup_stm32f10x_hd.o
    0x08001ae8   0x08001ae8   0x000001b0   Code   RO          383    .text               usart.o
    0x08001c98   0x08001c98   0x000000d4   Code   RO          406    .text               delay.o
    0x08001d6c   0x08001d6c   0x0000000c   Code   RO          420    .text               sys.o
    0x08001d78   0x08001d78   0x00000002   Code   RO          434    .text               c_w.l(use_no_semi_2.o)
    0x08001d7a   0x08001d7a   0x00000002   PAD
    0x08001d7c   0x08001d7c   0x00000018   Code   RO          438    .text               c_w.l(noretval__2printf.o)
    0x08001d94   0x08001d94   0x00000068   Code   RO          440    .text               c_w.l(__printf.o)
    0x08001dfc   0x08001dfc   0x00000052   Code   RO          442    .text               c_w.l(_printf_str.o)
    0x08001e4e   0x08001e4e   0x00000024   Code   RO          466    .text               c_w.l(strstr.o)
    0x08001e72   0x08001e72   0x00000064   Code   RO          468    .text               c_w.l(rt_memcpy_w.o)
    0x08001ed6   0x08001ed6   0x00000006   Code   RO          470    .text               c_w.l(heapauxi.o)
    0x08001edc   0x08001edc   0x00000002   Code   RO          474    .text               c_w.l(use_no_semi.o)
    0x08001ede   0x08001ede   0x0000002c   Code   RO          477    .text               c_w.l(_printf_char.o)
    0x08001f0a   0x08001f0a   0x00000002   PAD
    0x08001f0c   0x08001f0c   0x00000024   Code   RO          479    .text               c_w.l(_printf_char_file.o)
    0x08001f30   0x08001f30   0x00000030   Code   RO          497    .text               c_w.l(_printf_char_common.o)
    0x08001f60   0x08001f60   0x00000008   Code   RO          499    .text               c_w.l(ferror.o)
    0x08001f68   0x08001f68   0x0000004a   Code   RO          503    .text               c_w.l(sys_stackheap_outer.o)
    0x08001fb2   0x08001fb2   0x00000012   Code   RO          507    .text               c_w.l(exit.o)
    0x08001fc4   0x08001fc4   0x00000008   Code   RO          515    .text               c_w.l(libspace.o)
    0x08001fcc   0x08001fcc   0x00000020   Data   RO          640    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x08001fec, Size: 0x00000708, Max: 0x00005000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x08001fec   0x00000014   Data   RW          136    .data               system_stm32f10x.o
    0x20000014   0x08002000   0x00000010   Data   RW          211    .data               ec800.o
    0x20000024   0x08002010   0x00000014   Data   RW          328    .data               stm32f10x_rcc.o
    0x20000038   0x08002024   0x00000008   Data   RW          385    .data               usart.o
    0x20000040   0x0800202c   0x00000004   Data   RW          407    .data               delay.o
    0x20000044        -       0x00000064   Zero   RW          384    .bss                usart.o
    0x200000a8        -       0x00000060   Zero   RW          516    .bss                c_w.l(libspace.o)
    0x20000108        -       0x00000200   Zero   RW          377    HEAP                startup_stm32f10x_hd.o
    0x20000308        -       0x00000400   Zero   RW          376    STACK               startup_stm32f10x_hd.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

         0          0          0          0          0         32   core_cm3.o
       212         18          0          4          0       1011   delay.o
       836        212          0         16          0     204398   ec800.o
       104         14          0          0          0        571   led.o
       180         54          0          0          0     215327   main.o
       220         22          0          0          0       1777   misc.o
        64         26        304          0       1536        784   startup_stm32f10x_hd.o
      1468         34          0          0          0       8186   stm32f10x_flash.o
       860         38          0          0          0       5709   stm32f10x_gpio.o
        26          0          0          0          0      24294   stm32f10x_it.o
        64          6          0          0          0       1265   stm32f10x_iwdg.o
       932         36          0         20          0       8876   stm32f10x_rcc.o
      1032         22          0          0          0       8436   stm32f10x_usart.o
        12          0          0          0          0        441   sys.o
       588         36          0         20          0       8297   system_stm32f10x.o
       432         22          0          8        100       3677   usart.o
        44          0          0          0          0     204006   wdg.o

    ----------------------------------------------------------------------
      7076        <USER>        <GROUP>         68       1636     697087   Object Totals
         0          0         32          0          0          0   (incl. Generated)
         2          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

         8          0          0          0          0         68   __main.o
       104          0          0          0          0         84   __printf.o
         0          0          0          0          0          0   __rtentry.o
        12          0          0          0          0          0   __rtentry2.o
         6          0          0          0          0          0   __rtentry4.o
        52          8          0          0          0          0   __scatter.o
        26          0          0          0          0          0   __scatter_copy.o
        28          0          0          0          0          0   __scatter_zi.o
        44          0          0          0          0        108   _printf_char.o
        48          6          0          0          0         96   _printf_char_common.o
        36          4          0          0          0         80   _printf_char_file.o
         0          0          0          0          0          0   _printf_percent.o
         4          0          0          0          0          0   _printf_percent_end.o
         6          0          0          0          0          0   _printf_s.o
        82          0          0          0          0         80   _printf_str.o
        18          0          0          0          0         80   exit.o
         8          0          0          0          0         68   ferror.o
         6          0          0          0          0        152   heapauxi.o
         2          0          0          0          0          0   libinit.o
         2          0          0          0          0          0   libinit2.o
         2          0          0          0          0          0   libshutdown.o
         2          0          0          0          0          0   libshutdown2.o
         8          4          0          0         96         68   libspace.o
        24          4          0          0          0         84   noretval__2printf.o
       100          0          0          0          0         80   rt_memcpy_w.o
         2          0          0          0          0          0   rtexit.o
        10          0          0          0          0          0   rtexit2.o
        36          0          0          0          0         80   strstr.o
        74          0          0          0          0         80   sys_stackheap_outer.o
         2          0          0          0          0         68   use_no_semi.o
         2          0          0          0          0         68   use_no_semi_2.o

    ----------------------------------------------------------------------
       760         <USER>          <GROUP>          0         96       1344   Library Totals
         6          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

       754         26          0          0         96       1344   c_w.l

    ----------------------------------------------------------------------
       760         <USER>          <GROUP>          0         96       1344   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

      7836        566        336         68       1732     696555   Grand Totals
      7836        566        336         68       1732     696555   ELF Image Totals
      7836        566        336         68          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                 8172 (   7.98kB)
    Total RW  Size (RW Data + ZI Data)              1800 (   1.76kB)
    Total ROM Size (Code + RO Data + RW Data)       8240 (   8.05kB)

==============================================================================

