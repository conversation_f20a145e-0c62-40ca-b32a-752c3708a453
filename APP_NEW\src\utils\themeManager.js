// 主题管理工具
class ThemeManager {
  constructor() {
    this.currentTheme = uni.getStorageSync('app_theme') || 'light'
    this.themes = {
      light: {
        name: '浅色主题',
        colors: {
          background: '#ffffff',
          surface: '#f8fafc',
          primary: '#3b82f6',
          secondary: '#64748b',
          text: '#0f172a',
          textSecondary: '#64748b',
          border: '#e2e8f0',
          shadow: 'rgba(0, 0, 0, 0.1)'
        }
      },
      dark: {
        name: '深色主题',
        colors: {
          background: '#0f172a',
          surface: '#1e293b',
          primary: '#60a5fa',
          secondary: '#94a3b8',
          text: '#f1f5f9',
          textSecondary: '#cbd5e1',
          border: '#334155',
          shadow: 'rgba(0, 0, 0, 0.3)'
        }
      }
    }
    
    // 初始化主题
    this.applyTheme(this.currentTheme)
  }

  // 应用主题
  applyTheme(themeName) {
    const theme = this.themes[themeName]
    if (!theme) return

    this.currentTheme = themeName
    
    // 设置CSS变量
    const root = document.documentElement
    Object.entries(theme.colors).forEach(([key, value]) => {
      root.style.setProperty(`--color-${key}`, value)
    })

    // 设置body类名
    document.body.className = document.body.className.replace(/theme-\w+/g, '')
    document.body.classList.add(`theme-${themeName}`)

    // 保存到本地存储
    uni.setStorageSync('app_theme', themeName)

    // 触发全局主题切换事件
    uni.$emit('themeChanged', themeName)
  }

  // 切换主题
  toggleTheme() {
    const newTheme = this.currentTheme === 'light' ? 'dark' : 'light'
    this.applyTheme(newTheme)
    return newTheme
  }

  // 设置主题
  setTheme(themeName) {
    this.applyTheme(themeName)
  }

  // 获取当前主题
  getCurrentTheme() {
    return this.currentTheme
  }

  // 获取主题信息
  getThemeInfo(themeName = this.currentTheme) {
    return this.themes[themeName]
  }

  // 获取支持的主题列表
  getSupportedThemes() {
    return Object.keys(this.themes).map(key => ({
      key,
      name: this.themes[key].name,
      colors: this.themes[key].colors
    }))
  }

  // 判断是否为深色主题
  isDarkTheme() {
    return this.currentTheme === 'dark'
  }
}

// 创建全局实例
const themeManager = new ThemeManager()

export default themeManager
