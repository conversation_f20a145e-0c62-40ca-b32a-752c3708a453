// 简化的MQTT服务，用于演示目的
// 在生产环境中，可以根据需要集成真实的MQTT客户端

class MqttService {
  constructor() {
    this.client = null
    this.isConnected = false
    this.topics = [
      'radiation/data',
      'radiation/alert',
      'device/status',
      'health/data',
      'location/data'
    ]
    this.callbacks = new Map()
    this.simulationInterval = null
  }

  // 连接MQTT服务器（模拟）
  connect(options = {}) {
    const defaultOptions = {
      host: 'broker.emqx.io',
      port: 8083,
      protocol: 'ws',
      clientId: `radiation_app_${Math.random().toString(16).substr(2, 8)}`,
      username: '',
      password: '',
      clean: true,
      keepalive: 60,
      reconnectPeriod: 1000,
      ...options
    }

    console.log('正在连接MQTT服务器（模拟）...', defaultOptions)

    // 模拟连接过程
    setTimeout(() => {
      this.isConnected = true
      console.log('MQTT连接成功（模拟）')
      
      // 通知连接成功
      if (this.callbacks.has('connected')) {
        this.callbacks.get('connected')()
      }

      // 开始模拟数据
      this.startSimulation()
    }, 1000)

    return this
  }

  // 开始模拟数据传输
  startSimulation() {
    if (this.simulationInterval) {
      clearInterval(this.simulationInterval)
    }

    this.simulationInterval = setInterval(() => {
      // 模拟辐射数据
      const radiationData = {
        doseRate: 0.1 + Math.random() * 0.05,
        cps: 50 + Math.random() * 20,
        doseSum: Math.random() * 0.001,
        alarmStatus: Math.random() > 0.98 ? 2 : 0, // 2%概率报警
        temperature: 25 + Math.random() * 5,
        timestamp: Date.now()
      }

      this.handleMessage('radiation/data', radiationData)

      // 如果有报警，发送报警消息
      if (radiationData.alarmStatus > 0) {
        const alertData = {
          type: 'high_dose_rate',
          level: 'error',
          message: `剂量率过高: ${radiationData.doseRate.toFixed(3)} μSv/h`,
          doseRate: radiationData.doseRate,
          doseSum: radiationData.doseSum,
          timestamp: Date.now()
        }
        this.handleMessage('radiation/alert', alertData)
      }

      // 模拟设备状态
      const deviceStatus = {
        battery: {
          level: 80 + Math.random() * 20,
          charging: Math.random() > 0.7,
          voltage: 3.8 + Math.random() * 0.4
        },
        connection: {
          mqtt: this.isConnected,
          bluetooth: Math.random() > 0.3,
          gps: Math.random() > 0.2,
          cellular: Math.random() > 0.1
        }
      }

      this.handleMessage('device/status', deviceStatus)

      // 模拟健康数据
      const healthData = {
        heartRate: 70 + Math.round(Math.random() * 20 - 10),
        spO2: 96 + Math.round(Math.random() * 4),
        bodyTemp: 36.5 + (Math.random() * 1.0 - 0.5),
        steps: Math.round(8000 + Math.random() * 2000),
        timestamp: Date.now()
      }

      this.handleMessage('health/data', healthData)

      // 模拟位置数据
      const locationData = {
        latitude: 39.9042 + (Math.random() - 0.5) * 0.01,
        longitude: 116.4074 + (Math.random() - 0.5) * 0.01,
        altitude: 50 + Math.random() * 20,
        accuracy: 5 + Math.random() * 10,
        timestamp: Date.now()
      }

      this.handleMessage('location/data', locationData)

    }, 5000) // 每5秒更新一次数据
  }

  // 订阅主题（模拟）
  subscribeTopics() {
    if (!this.isConnected) return

    this.topics.forEach(topic => {
      console.log(`成功订阅主题（模拟）: ${topic}`)
    })
  }

  // 处理接收到的消息
  handleMessage(topic, data) {
    console.log(`收到消息（模拟） [${topic}]:`, data)
    
    // 根据主题分发消息
    switch (topic) {
      case 'radiation/data':
        this.notifyCallback('radiationData', data)
        break
      case 'radiation/alert':
        this.notifyCallback('radiationAlert', data)
        break
      case 'device/status':
        this.notifyCallback('deviceStatus', data)
        break
      case 'health/data':
        this.notifyCallback('healthData', data)
        break
      case 'location/data':
        this.notifyCallback('locationData', data)
        break
      default:
        console.log('未知主题:', topic)
    }
  }

  // 发布消息（模拟）
  publish(topic, message) {
    if (!this.isConnected) {
      console.error('MQTT未连接，无法发布消息（模拟）')
      return false
    }

    try {
      const payload = typeof message === 'string' ? message : JSON.stringify(message)
      console.log(`发布消息（模拟） [${topic}]:`, payload)
      return true
    } catch (error) {
      console.error('发布消息失败（模拟）:', error)
      return false
    }
  }

  // 注册回调函数
  onMessage(event, callback) {
    this.callbacks.set(event, callback)
  }

  // 移除回调函数
  offMessage(event) {
    this.callbacks.delete(event)
  }

  // 通知回调函数
  notifyCallback(event, data) {
    if (this.callbacks.has(event)) {
      this.callbacks.get(event)(data)
    }
  }

  // 断开连接
  disconnect() {
    if (this.simulationInterval) {
      clearInterval(this.simulationInterval)
      this.simulationInterval = null
    }
    
    this.isConnected = false
    console.log('MQTT连接已断开（模拟）')
    
    if (this.callbacks.has('disconnected')) {
      this.callbacks.get('disconnected')()
    }
  }

  // 检查连接状态
  getConnectionStatus() {
    return {
      isConnected: this.isConnected,
      clientId: `radiation_app_${Math.random().toString(16).substr(2, 8)}`,
      host: 'broker.emqx.io（模拟）',
      port: 8083,
      subscribedTopics: this.topics
    }
  }
}

export default new MqttService() 