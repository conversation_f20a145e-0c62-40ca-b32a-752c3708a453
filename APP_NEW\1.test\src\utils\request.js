/**
 * 网络请求工具封装
 */

// 基础URL，根据接口文档中提供的地址
const BASE_URL = 'https://zarkychmvpiv.sealoshzh.site';

/**
 * 请求拦截器
 * @param {Object} config 请求配置
 */
const requestInterceptor = (config) => {
  // 获取本地存储的token
  const token = uni.getStorageSync('token');
  
  // 设置请求头
  config.header = {
    ...config.header,
    'Content-Type': 'application/json'
  };
  
  // 如果存在token，添加到请求头
  if (token) {
    config.header.Authorization = `Bearer ${token}`;
  }
  
  // 添加基础URL
  if (!config.url.startsWith('http')) {
    config.url = BASE_URL + config.url;
  }
  
  return config;
};

/**
 * 响应拦截器
 * @param {Object} response 响应数据
 * @param {Object} config 请求配置
 */
const responseInterceptor = (response, config) => {
  // 获取响应状态码和数据
  const { statusCode, data } = response;
  
  // 如果响应成功（HTTP状态码为200）
  if (statusCode === 200) {
    // 判断业务状态码
    if (data.code === 200) {
      // 业务成功，返回数据
      return data;
    } else {
      // 业务失败，显示错误消息
      uni.showToast({
        icon: 'none',
        title: data.message || '操作失败'
      });
      
      // 如果是401未授权错误，跳转到登录页
      if (data.code === 401) {
        // 清除本地存储的token
        uni.removeStorageSync('token');
        
        // 延迟跳转，让用户看到错误提示
        setTimeout(() => {
          uni.reLaunch({
            url: '/pages/index/index'
          });
        }, 1500);
      }
      
      // 返回Promise.reject，方便后续捕获错误
      return Promise.reject(data);
    }
  }
  
  // HTTP请求失败
  uni.showToast({
    icon: 'none',
    title: '网络请求失败，请检查网络连接'
  });
  
  return Promise.reject(response);
};

/**
 * 请求失败处理
 * @param {Object} error 错误信息
 */
const requestFail = (error) => {
  uni.hideLoading();
  
  let message = '网络请求失败，请检查网络连接';
  if (error && error.message) {
    message = error.message;
  } else if (typeof error === 'string') {
    message = error;
  }
  
  uni.showToast({
    icon: 'none',
    title: message
  });
  
  return Promise.reject(error);
};

/**
 * 封装的请求方法
 * @param {Object} options 请求配置
 * @returns {Promise} 返回Promise对象
 */
const request = (options) => {
  // 应用请求拦截器
  const config = requestInterceptor(options);
  
  // 是否显示加载提示
  if (options.loading !== false) {
    uni.showLoading({
      title: options.loadingText || '加载中...',
      mask: true
    });
  }
  
  // 发起请求
  return new Promise((resolve, reject) => {
    uni.request({
      ...config,
      success: (response) => {
        // 隐藏加载提示
        if (options.loading !== false) {
          uni.hideLoading();
        }
        
        // 应用响应拦截器
        try {
          const result = responseInterceptor(response, config);
          resolve(result);
        } catch (error) {
          reject(error);
        }
      },
      fail: (error) => {
        // 隐藏加载提示
        if (options.loading !== false) {
          uni.hideLoading();
        }
        
        // 处理请求失败
        reject(requestFail(error));
      }
    });
  });
};

/**
 * GET请求
 * @param {String} url 请求地址
 * @param {Object} params 请求参数
 * @param {Object} options 请求配置
 * @returns {Promise} 返回Promise对象
 */
const get = (url, params = {}, options = {}) => {
  return request({
    url,
    method: 'GET',
    data: params,
    ...options
  });
};

/**
 * POST请求
 * @param {String} url 请求地址
 * @param {Object} data 请求体
 * @param {Object} options 请求配置
 * @returns {Promise} 返回Promise对象
 */
const post = (url, data = {}, options = {}) => {
  return request({
    url,
    method: 'POST',
    data,
    ...options
  });
};

/**
 * PUT请求
 * @param {String} url 请求地址
 * @param {Object} data 请求体
 * @param {Object} options 请求配置
 * @returns {Promise} 返回Promise对象
 */
const put = (url, data = {}, options = {}) => {
  return request({
    url,
    method: 'PUT',
    data,
    ...options
  });
};

/**
 * DELETE请求
 * @param {String} url 请求地址
 * @param {Object} params 请求参数
 * @param {Object} options 请求配置
 * @returns {Promise} 返回Promise对象
 */
const del = (url, params = {}, options = {}) => {
  return request({
    url,
    method: 'DELETE',
    data: params,
    ...options
  });
};

export default {
  request,
  get,
  post,
  put,
  delete: del
}; 