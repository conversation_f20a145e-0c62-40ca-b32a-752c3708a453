<template>
  <view class="map-container">
    <!-- 地图工具栏 -->
    <view class="map-toolbar">
      <view class="toolbar-left">
        <view class="tool-button" @tap="toggleLayer">
          <text class="tool-icon">🗺️</text>
          <text class="tool-text">图层</text>
        </view>
        <view class="tool-button" @tap="showLocationInfo">
          <text class="tool-icon">📍</text>
          <text class="tool-text">位置</text>
        </view>
      </view>
      <view class="toolbar-right">
        <view class="tool-button emergency" @tap="showAlarmScreen" v-if="hasActiveAlarm">
          <text class="tool-icon">🚨</text>
          <text class="tool-text">报警</text>
        </view>
        <view class="tool-button" @tap="centerToLocation">
          <text class="tool-icon">🎯</text>
          <text class="tool-text">定位</text>
        </view>
      </view>
    </view>

    <!-- 地图主体 -->
    <view class="map-main">
      <map 
        id="radiation-map"
        class="map-canvas"
        :longitude="mapCenter.longitude"
        :latitude="mapCenter.latitude"
        :scale="mapScale"
        :markers="mapMarkers"
        :polyline="mapPolylines"
        :enable-satellite="enableSatellite"
        :enable-traffic="enableTraffic"
        @markertap="onMarkerTap"
        @regionchange="onRegionChange"
        @tap="onMapTap">
        
        <!-- 自定义控件 -->
        <cover-view class="map-controls">
          <cover-view class="control-group">
            <cover-view class="control-button" @tap="zoomIn">
              <cover-view class="control-icon">+</cover-view>
            </cover-view>
            <cover-view class="control-button" @tap="zoomOut">
              <cover-view class="control-icon">-</cover-view>
            </cover-view>
          </cover-view>
          
          <cover-view class="control-group">
            <cover-view class="control-button" @tap="toggleSatellite">
              <cover-view class="control-icon">🛰️</cover-view>
            </cover-view>
            <cover-view class="control-button" @tap="toggleTraffic">
              <cover-view class="control-icon">🚗</cover-view>
            </cover-view>
          </cover-view>
        </cover-view>

        <!-- 实时数据悬浮窗 -->
        <cover-view class="realtime-overlay">
          <cover-view class="realtime-card">
            <cover-view class="realtime-header">
              <cover-view class="status-indicator" :class="radiationLevelClass">
                <cover-view class="indicator-dot"></cover-view>
              </cover-view>
              <cover-view class="realtime-title">实时监测</cover-view>
            </cover-view>
            <cover-view class="realtime-data">
              <cover-view class="data-row">
                <cover-view class="data-label">剂量率</cover-view>
                <cover-view class="data-value">{{ radiationState.currentData.doseRate.toFixed(3) }} μSv/h</cover-view>
              </cover-view>
              <cover-view class="data-row">
                <cover-view class="data-label">位置</cover-view>
                <cover-view class="data-value">{{ formatCoordinate(locationState.current.latitude, locationState.current.longitude) }}</cover-view>
              </cover-view>
            </cover-view>
          </cover-view>
        </cover-view>
      </map>
    </view>

    <!-- 地图图层选择器 -->
    <view class="layer-selector" v-if="showLayerSelector">
      <view class="layer-header">
        <text class="layer-title">地图图层</text>
        <text class="layer-close" @tap="hideLayerSelector">✕</text>
      </view>
      <view class="layer-options">
        <view class="layer-option" @tap="toggleHeatmap">
          <text class="option-icon">🔥</text>
          <text class="option-text">辐射热力图</text>
          <view class="option-toggle" :class="{ active: showHeatmap }">
            <view class="toggle-knob"></view>
          </view>
        </view>
        <view class="layer-option" @tap="toggleTrajectory">
          <text class="option-icon">📈</text>
          <text class="option-text">历史轨迹</text>
          <view class="option-toggle" :class="{ active: showTrajectory }">
            <view class="toggle-knob"></view>
          </view>
        </view>
        <view class="layer-option" @tap="toggleAlertPoints">
          <text class="option-icon">⚠️</text>
          <text class="option-text">报警点位</text>
          <view class="option-toggle" :class="{ active: showAlertPoints }">
            <view class="toggle-knob"></view>
          </view>
        </view>
        <view class="layer-option" @tap="toggleMonitoringPoints">
          <text class="option-icon">📡</text>
          <text class="option-text">监测点位</text>
          <view class="option-toggle" :class="{ active: showMonitoringPoints }">
            <view class="toggle-knob"></view>
          </view>
        </view>
      </view>
    </view>

    <!-- 位置信息面板 -->
    <view class="location-panel" v-if="showLocationPanel">
      <view class="panel-header">
        <text class="panel-title">位置信息</text>
        <text class="panel-close" @tap="hideLocationPanel">✕</text>
      </view>
      <view class="panel-content">
        <view class="info-group">
          <view class="info-item">
            <text class="info-label">纬度</text>
            <text class="info-value">{{ locationState.current.latitude.toFixed(6) }}°</text>
          </view>
          <view class="info-item">
            <text class="info-label">经度</text>
            <text class="info-value">{{ locationState.current.longitude.toFixed(6) }}°</text>
          </view>
          <view class="info-item">
            <text class="info-label">海拔</text>
            <text class="info-value">{{ locationState.current.altitude }} m</text>
          </view>
          <view class="info-item">
            <text class="info-label">精度</text>
            <text class="info-value">± {{ locationState.current.accuracy }} m</text>
          </view>
        </view>
        
        <view class="info-group">
          <view class="info-item">
            <text class="info-label">当前剂量率</text>
            <text class="info-value" :class="radiationLevelClass">{{ radiationState.currentData.doseRate.toFixed(3) }} μSv/h</text>
          </view>
          <view class="info-item">
            <text class="info-label">安全评估</text>
            <text class="info-value" :class="radiationLevelClass">{{ radiationStatusText }}</text>
          </view>
        </view>

        <view class="panel-actions">
          <text class="action-btn" @tap="shareLocation">📤 分享位置</text>
          <text class="action-btn" @tap="addPOI">📌 添加标记</text>
        </view>
      </view>
    </view>

    <!-- 大屏报警展示 -->
    <view class="alarm-screen" v-if="showAlarmScreen">
      <view class="alarm-overlay">
        <view class="alarm-content">
          <view class="alarm-header">
            <text class="alarm-title">⚠️ 辐射报警</text>
            <text class="alarm-close" @tap="hideAlarmScreen">✕</text>
          </view>
          
          <view class="alarm-main">
            <view class="alarm-level" :class="currentAlarm?.level">
              <text class="level-text">{{ getAlarmLevelText(currentAlarm?.level) }}</text>
            </view>
            
            <view class="alarm-data">
              <view class="alarm-value">
                <text class="value-number">{{ radiationState.currentData.doseRate.toFixed(3) }}</text>
                <text class="value-unit">μSv/h</text>
              </view>
              
              <view class="alarm-info">
                <text class="alarm-message">{{ currentAlarm?.message }}</text>
                <text class="alarm-time">{{ formatAlarmTime(currentAlarm?.timestamp) }}</text>
              </view>
            </view>
            
            <view class="alarm-location" v-if="currentAlarm?.location">
              <text class="location-title">报警位置</text>
              <text class="location-coords">
                {{ currentAlarm.location.latitude.toFixed(4) }}, {{ currentAlarm.location.longitude.toFixed(4) }}
              </text>
            </view>
          </view>
          
          <view class="alarm-actions">
            <text class="alarm-btn primary" @tap="confirmAlarm">确认报警</text>
            <text class="alarm-btn secondary" @tap="silenceAlarm">静音报警</text>
            <text class="alarm-btn danger" @tap="emergencyCall">紧急呼叫</text>
          </view>
        </view>
        
        <!-- 脉冲动画 -->
        <view class="pulse-animation">
          <view class="pulse-ring"></view>
          <view class="pulse-ring"></view>
          <view class="pulse-ring"></view>
        </view>
      </view>
    </view>

    <!-- 标记详情弹窗 -->
    <view class="marker-popup" v-if="selectedMarker">
      <view class="popup-content">
        <view class="popup-header">
          <text class="popup-title">{{ selectedMarker.title }}</text>
          <text class="popup-close" @tap="closeMarkerPopup">✕</text>
        </view>
        <view class="popup-body">
          <view class="popup-info">
            <text class="info-text">{{ selectedMarker.description }}</text>
            <text class="info-time">{{ formatTime(selectedMarker.timestamp) }}</text>
          </view>
          <view class="popup-data" v-if="selectedMarker.data">
            <view class="data-item">
              <text class="data-label">剂量率</text>
              <text class="data-value">{{ selectedMarker.data.doseRate }} μSv/h</text>
            </view>
            <view class="data-item">
              <text class="data-label">计数率</text>
              <text class="data-value">{{ selectedMarker.data.cps }} CPS</text>
            </view>
          </view>
        </view>
        <view class="popup-actions">
          <text class="popup-btn" @tap="navigateToMarker">📍 导航</text>
          <text class="popup-btn" @tap="shareMarker">📤 分享</text>
        </view>
      </view>
    </view>

    <!-- 底部统计栏 -->
    <view class="bottom-stats">
      <view class="stats-item">
        <text class="stats-label">监测点位</text>
        <text class="stats-value">{{ monitoringPointsCount }}</text>
      </view>
      <view class="stats-item">
        <text class="stats-label">报警点位</text>
        <text class="stats-value">{{ alertPointsCount }}</text>
      </view>
      <view class="stats-item">
        <text class="stats-label">轨迹长度</text>
        <text class="stats-value">{{ trajectoryLength.toFixed(1) }}km</text>
      </view>
      <view class="stats-item">
        <text class="stats-label">覆盖范围</text>
        <text class="stats-value">{{ coverageArea.toFixed(1) }}km²</text>
      </view>
    </view>
  </view>
</template>

<script>
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { radiationState, deviceState, locationState } from '../../utils/dataStore.js'
import dataStore from '../../utils/dataStore.js'

export default {
  name: 'MapView',
  setup() {
    const mapCenter = ref({
      latitude: 39.9042,
      longitude: 116.4074
    })
    const mapScale = ref(15)
    const showLayerSelector = ref(false)
    const showLocationPanel = ref(false)
    const showAlarmScreen = ref(false)
    const showHeatmap = ref(false)
    const showTrajectory = ref(true)
    const showAlertPoints = ref(true)
    const showMonitoringPoints = ref(true)
    const enableSatellite = ref(false)
    const enableTraffic = ref(false)
    const selectedMarker = ref(null)

    // 地图标记和路线
    const mapMarkers = ref([])
    const mapPolylines = ref([])

    // 计算属性
    const radiationLevel = computed(() => {
      const doseRate = radiationState.currentData.doseRate
      const { maxDoseRate, minDoseRate } = radiationState.settings
      
      if (doseRate > maxDoseRate) return 'danger'
      if (doseRate < minDoseRate) return 'warning'
      return 'safe'
    })

    const radiationLevelClass = computed(() => {
      return `level-${radiationLevel.value}`
    })

    const radiationStatusText = computed(() => {
      switch (radiationLevel.value) {
        case 'danger': return '危险区域'
        case 'warning': return '注意区域'
        default: return '安全区域'
      }
    })

    const hasActiveAlarm = computed(() => {
      return radiationState.alerts.length > 0 && 
             radiationState.alerts[0].timestamp > (Date.now() - 300000) // 5分钟内的报警
    })

    const currentAlarm = computed(() => {
      return hasActiveAlarm.value ? radiationState.alerts[0] : null
    })

    const monitoringPointsCount = computed(() => {
      return mapMarkers.value.filter(marker => marker.type === 'monitoring').length
    })

    const alertPointsCount = computed(() => {
      return mapMarkers.value.filter(marker => marker.type === 'alert').length
    })

    const trajectoryLength = computed(() => {
      if (locationState.history.length < 2) return 0
      
      let totalDistance = 0
      for (let i = 1; i < locationState.history.length; i++) {
        const prev = locationState.history[i - 1]
        const curr = locationState.history[i]
        totalDistance += calculateDistance(prev.latitude, prev.longitude, curr.latitude, curr.longitude)
      }
      return totalDistance
    })

    const coverageArea = computed(() => {
      if (locationState.history.length < 3) return 0
      
      // 简单的包围盒面积计算
      const lats = locationState.history.map(point => point.latitude)
      const lngs = locationState.history.map(point => point.longitude)
      
      const latRange = Math.max(...lats) - Math.min(...lats)
      const lngRange = Math.max(...lngs) - Math.min(...lngs)
      
      // 近似计算（1度约111km）
      return latRange * lngRange * 111 * 111
    })

    // 方法
    const toggleLayer = () => {
      showLayerSelector.value = !showLayerSelector.value
    }

    const hideLayerSelector = () => {
      showLayerSelector.value = false
    }

    const showLocationInfo = () => {
      showLocationPanel.value = true
    }

    const hideLocationPanel = () => {
      showLocationPanel.value = false
    }

    const showAlarmScreen = () => {
      showAlarmScreen.value = true
    }

    const hideAlarmScreen = () => {
      showAlarmScreen.value = false
    }

    const centerToLocation = () => {
      mapCenter.value = {
        latitude: locationState.current.latitude,
        longitude: locationState.current.longitude
      }
      uni.showToast({
        title: '已定位到当前位置',
        icon: 'success'
      })
    }

    const zoomIn = () => {
      if (mapScale.value < 18) {
        mapScale.value += 1
      }
    }

    const zoomOut = () => {
      if (mapScale.value > 3) {
        mapScale.value -= 1
      }
    }

    const toggleSatellite = () => {
      enableSatellite.value = !enableSatellite.value
    }

    const toggleTraffic = () => {
      enableTraffic.value = !enableTraffic.value
    }

    const toggleHeatmap = () => {
      showHeatmap.value = !showHeatmap.value
      updateMapLayers()
    }

    const toggleTrajectory = () => {
      showTrajectory.value = !showTrajectory.value
      updateMapLayers()
    }

    const toggleAlertPoints = () => {
      showAlertPoints.value = !showAlertPoints.value
      updateMapMarkers()
    }

    const toggleMonitoringPoints = () => {
      showMonitoringPoints.value = !showMonitoringPoints.value
      updateMapMarkers()
    }

    const updateMapMarkers = () => {
      const markers = []

      // 当前位置标记
      markers.push({
        id: 'current',
        latitude: locationState.current.latitude,
        longitude: locationState.current.longitude,
        iconPath: '/static/icons/current-location.png',
        width: 40,
        height: 40,
        title: '当前位置',
        type: 'current',
        data: radiationState.currentData
      })

      // 监测点位标记
      if (showMonitoringPoints.value) {
        radiationState.history.forEach((point, index) => {
          if (index % 10 === 0) { // 每10个点显示一个标记
            markers.push({
              id: `monitoring-${index}`,
              latitude: locationState.history[Math.min(index, locationState.history.length - 1)]?.latitude || point.latitude || locationState.current.latitude,
              longitude: locationState.history[Math.min(index, locationState.history.length - 1)]?.longitude || point.longitude || locationState.current.longitude,
              iconPath: '/static/icons/monitoring-point.png',
              width: 30,
              height: 30,
              title: '监测点',
              type: 'monitoring',
              data: point,
              timestamp: point.timestamp
            })
          }
        })
      }

      // 报警点位标记
      if (showAlertPoints.value) {
        radiationState.alerts.forEach((alert, index) => {
          if (alert.location) {
            markers.push({
              id: `alert-${index}`,
              latitude: alert.location.latitude,
              longitude: alert.location.longitude,
              iconPath: '/static/icons/alert-point.png',
              width: 35,
              height: 35,
              title: '报警点位',
              type: 'alert',
              data: {
                doseRate: alert.doseRate,
                doseSum: alert.doseSum
              },
              timestamp: alert.timestamp,
              description: alert.message
            })
          }
        })
      }

      mapMarkers.value = markers
    }

    const updateMapLayers = () => {
      const polylines = []

      // 历史轨迹
      if (showTrajectory.value && locationState.history.length > 1) {
        const points = locationState.history.slice(-50).map(point => ({
          latitude: point.latitude,
          longitude: point.longitude
        }))

        polylines.push({
          points,
          color: '#3cc51f',
          width: 4,
          dottedLine: false,
          arrowLine: true
        })
      }

      mapPolylines.value = polylines
    }

    const onMarkerTap = (e) => {
      const markerId = e.detail.markerId
      const marker = mapMarkers.value.find(m => m.id === markerId)
      if (marker) {
        selectedMarker.value = marker
      }
    }

    const closeMarkerPopup = () => {
      selectedMarker.value = null
    }

    const onRegionChange = (e) => {
      if (e.type === 'end') {
        mapCenter.value = {
          latitude: e.detail.centerLocation.latitude,
          longitude: e.detail.centerLocation.longitude
        }
      }
    }

    const onMapTap = (e) => {
      // 点击地图空白区域时关闭弹窗
      selectedMarker.value = null
      showLayerSelector.value = false
      showLocationPanel.value = false
    }

    const formatCoordinate = (lat, lng) => {
      return `${lat.toFixed(4)}, ${lng.toFixed(4)}`
    }

    const formatTime = (timestamp) => {
      return new Date(timestamp).toLocaleString('zh-CN', {
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      })
    }

    const formatAlarmTime = (timestamp) => {
      const now = Date.now()
      const diff = now - timestamp
      
      if (diff < 60000) return '刚刚'
      if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`
      if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`
      return new Date(timestamp).toLocaleDateString('zh-CN')
    }

    const getAlarmLevelText = (level) => {
      const levelMap = {
        'error': '严重报警',
        'warning': '警告报警',
        'info': '提示报警'
      }
      return levelMap[level] || '未知报警'
    }

    const calculateDistance = (lat1, lng1, lat2, lng2) => {
      const R = 6371 // 地球半径（公里）
      const dLat = (lat2 - lat1) * Math.PI / 180
      const dLng = (lng2 - lng1) * Math.PI / 180
      const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
                Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
                Math.sin(dLng/2) * Math.sin(dLng/2)
      const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a))
      return R * c
    }

    const shareLocation = () => {
      const location = locationState.current
      const message = `我的位置：${location.latitude.toFixed(6)}, ${location.longitude.toFixed(6)}\n当前辐射水平：${radiationState.currentData.doseRate.toFixed(3)} μSv/h`
      
      uni.showModal({
        title: '分享位置',
        content: message,
        confirmText: '复制',
        success: (res) => {
          if (res.confirm) {
            uni.setClipboardData({
              data: message,
              success: () => {
                uni.showToast({
                  title: '已复制到剪贴板',
                  icon: 'success'
                })
              }
            })
          }
        }
      })
    }

    const addPOI = () => {
      uni.showModal({
        title: '添加标记',
        content: '在当前位置添加自定义标记点',
        success: (res) => {
          if (res.confirm) {
            // 添加POI逻辑
            locationState.poi.push({
              latitude: locationState.current.latitude,
              longitude: locationState.current.longitude,
              name: `POI-${Date.now()}`,
              timestamp: Date.now(),
              radiationData: {...radiationState.currentData}
            })
            
            updateMapMarkers()
            
            uni.showToast({
              title: '标记已添加',
              icon: 'success'
            })
          }
        }
      })
    }

    const confirmAlarm = () => {
      uni.showToast({
        title: '报警已确认',
        icon: 'success'
      })
      hideAlarmScreen()
    }

    const silenceAlarm = () => {
      uni.showToast({
        title: '报警已静音',
        icon: 'success'
      })
      hideAlarmScreen()
    }

    const emergencyCall = () => {
      uni.showModal({
        title: '紧急呼叫',
        content: '是否拨打紧急电话？',
        confirmText: '拨打',
        success: (res) => {
          if (res.confirm) {
            uni.makePhoneCall({
              phoneNumber: '120' // 紧急电话
            })
          }
        }
      })
    }

    const navigateToMarker = () => {
      if (selectedMarker.value) {
        const marker = selectedMarker.value
        uni.openLocation({
          latitude: marker.latitude,
          longitude: marker.longitude,
          name: marker.title,
          scale: 18
        })
      }
    }

    const shareMarker = () => {
      if (selectedMarker.value) {
        const marker = selectedMarker.value
        const message = `${marker.title}\n位置：${marker.latitude.toFixed(6)}, ${marker.longitude.toFixed(6)}`
        
        uni.setClipboardData({
          data: message,
          success: () => {
            uni.showToast({
              title: '标记信息已复制',
              icon: 'success'
            })
          }
        })
      }
    }

    // 生命周期
    onMounted(() => {
      // 初始化地图中心为当前位置
      mapCenter.value = {
        latitude: locationState.current.latitude,
        longitude: locationState.current.longitude
      }

      // 更新地图标记和图层
      updateMapMarkers()
      updateMapLayers()

      // 监听位置变化
      const locationWatcher = setInterval(() => {
        updateMapMarkers()
        updateMapLayers()
      }, 10000) // 每10秒更新一次

      onUnmounted(() => {
        clearInterval(locationWatcher)
      })
    })

    return {
      mapCenter,
      mapScale,
      showLayerSelector,
      showLocationPanel,
      showAlarmScreen,
      showHeatmap,
      showTrajectory,
      showAlertPoints,
      showMonitoringPoints,
      enableSatellite,
      enableTraffic,
      selectedMarker,
      mapMarkers,
      mapPolylines,
      radiationState,
      locationState,
      radiationLevelClass,
      radiationStatusText,
      hasActiveAlarm,
      currentAlarm,
      monitoringPointsCount,
      alertPointsCount,
      trajectoryLength,
      coverageArea,
      toggleLayer,
      hideLayerSelector,
      showLocationInfo,
      hideLocationPanel,
      showAlarmScreen,
      hideAlarmScreen,
      centerToLocation,
      zoomIn,
      zoomOut,
      toggleSatellite,
      toggleTraffic,
      toggleHeatmap,
      toggleTrajectory,
      toggleAlertPoints,
      toggleMonitoringPoints,
      onMarkerTap,
      closeMarkerPopup,
      onRegionChange,
      onMapTap,
      formatCoordinate,
      formatTime,
      formatAlarmTime,
      getAlarmLevelText,
      shareLocation,
      addPOI,
      confirmAlarm,
      silenceAlarm,
      emergencyCall,
      navigateToMarker,
      shareMarker
    }
  }
}
</script>

<style scoped>
.map-container {
  height: 100vh;
  background: #0f0f1c;
  position: relative;
  overflow: hidden;
}

/* 地图工具栏 */
.map-toolbar {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  background: linear-gradient(180deg, rgba(15, 15, 28, 0.9) 0%, transparent 100%);
  z-index: 100;
}

.toolbar-left,
.toolbar-right {
  display: flex;
  gap: 15rpx;
}

.tool-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5rpx;
  padding: 15rpx;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border-radius: 15rpx;
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.tool-button.emergency {
  background: rgba(220, 53, 69, 0.2);
  border-color: rgba(220, 53, 69, 0.3);
  animation: emergencyPulse 1s infinite;
}

@keyframes emergencyPulse {
  0%, 100% { background: rgba(220, 53, 69, 0.2); }
  50% { background: rgba(220, 53, 69, 0.4); }
}

.tool-button:active {
  transform: scale(0.95);
  background: rgba(255, 255, 255, 0.2);
}

.tool-icon {
  font-size: 24rpx;
}

.tool-text {
  font-size: 18rpx;
  color: #ffffff;
}

/* 地图主体 */
.map-main {
  height: 100vh;
  position: relative;
}

.map-canvas {
  width: 100%;
  height: 100%;
}

/* 地图控件 */
.map-controls {
  position: absolute;
  right: 20rpx;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.control-group {
  display: flex;
  flex-direction: column;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border-radius: 15rpx;
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.control-button {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: transparent;
  transition: all 0.3s ease;
}

.control-button:active {
  background: rgba(255, 255, 255, 0.2);
}

.control-icon {
  font-size: 32rpx;
  color: #ffffff;
  font-weight: bold;
}

/* 实时数据悬浮窗 */
.realtime-overlay {
  position: absolute;
  top: 120rpx;
  left: 20rpx;
  z-index: 50;
}

.realtime-card {
  background: rgba(26, 26, 46, 0.9);
  backdrop-filter: blur(20px);
  border-radius: 20rpx;
  padding: 25rpx;
  border: 1px solid rgba(255, 255, 255, 0.1);
  min-width: 300rpx;
}

.realtime-header {
  display: flex;
  align-items: center;
  gap: 15rpx;
  margin-bottom: 20rpx;
}

.status-indicator {
  width: 20rpx;
  height: 20rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.status-indicator.level-safe {
  background: #3cc51f;
}

.status-indicator.level-warning {
  background: #ffc107;
}

.status-indicator.level-danger {
  background: #dc3545;
  animation: dangerPulse 1s infinite;
}

@keyframes dangerPulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.indicator-dot {
  width: 8rpx;
  height: 8rpx;
  background: #ffffff;
  border-radius: 50%;
}

.realtime-title {
  font-size: 24rpx;
  color: #ffffff;
  font-weight: 600;
}

.realtime-data {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.data-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.data-label {
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.7);
}

.data-value {
  font-size: 22rpx;
  color: #ffffff;
  font-weight: 600;
}

/* 图层选择器 */
.layer-selector {
  position: absolute;
  left: 20rpx;
  bottom: 200rpx;
  background: rgba(26, 26, 46, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20rpx;
  padding: 30rpx;
  border: 1px solid rgba(255, 255, 255, 0.1);
  z-index: 200;
  min-width: 400rpx;
  animation: slideUp 0.3s ease;
}

@keyframes slideUp {
  from { transform: translateY(20rpx); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

.layer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25rpx;
}

.layer-title {
  font-size: 28rpx;
  color: #ffffff;
  font-weight: 600;
}

.layer-close {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.7);
  padding: 10rpx;
}

.layer-options {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.layer-option {
  display: flex;
  align-items: center;
  gap: 20rpx;
  padding: 20rpx;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 15rpx;
  transition: all 0.3s ease;
}

.layer-option:active {
  background: rgba(255, 255, 255, 0.1);
}

.option-icon {
  font-size: 28rpx;
}

.option-text {
  flex: 1;
  font-size: 24rpx;
  color: #ffffff;
}

.option-toggle {
  width: 60rpx;
  height: 30rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 15rpx;
  position: relative;
  transition: all 0.3s ease;
}

.option-toggle.active {
  background: #3cc51f;
}

.toggle-knob {
  width: 26rpx;
  height: 26rpx;
  background: #ffffff;
  border-radius: 50%;
  position: absolute;
  top: 2rpx;
  left: 2rpx;
  transition: all 0.3s ease;
}

.option-toggle.active .toggle-knob {
  left: 32rpx;
}

/* 位置信息面板 */
.location-panel {
  position: absolute;
  right: 20rpx;
  bottom: 200rpx;
  background: rgba(26, 26, 46, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20rpx;
  padding: 30rpx;
  border: 1px solid rgba(255, 255, 255, 0.1);
  z-index: 200;
  min-width: 400rpx;
  animation: slideUp 0.3s ease;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25rpx;
}

.panel-title {
  font-size: 28rpx;
  color: #ffffff;
  font-weight: 600;
}

.panel-close {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.7);
  padding: 10rpx;
}

.panel-content {
  display: flex;
  flex-direction: column;
  gap: 25rpx;
}

.info-group {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
  padding: 20rpx;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 15rpx;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.info-label {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.7);
}

.info-value {
  font-size: 24rpx;
  color: #ffffff;
  font-weight: 600;
}

.info-value.level-safe {
  color: #3cc51f;
}

.info-value.level-warning {
  color: #ffc107;
}

.info-value.level-danger {
  color: #dc3545;
}

.panel-actions {
  display: flex;
  gap: 15rpx;
}

.action-btn {
  flex: 1;
  text-align: center;
  padding: 20rpx;
  background: rgba(60, 197, 31, 0.1);
  border: 1px solid rgba(60, 197, 31, 0.3);
  border-radius: 15rpx;
  color: #ffffff;
  font-size: 22rpx;
  transition: all 0.3s ease;
}

.action-btn:active {
  background: rgba(60, 197, 31, 0.2);
}

/* 大屏报警展示 */
.alarm-screen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  animation: alarmShow 0.5s ease;
}

@keyframes alarmShow {
  from { opacity: 0; }
  to { opacity: 1; }
}

.alarm-overlay {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(220, 53, 69, 0.9) 0%, rgba(139, 0, 0, 0.9) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.alarm-content {
  background: rgba(26, 26, 46, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 30rpx;
  padding: 60rpx;
  border: 2px solid rgba(220, 53, 69, 0.5);
  max-width: 80%;
  text-align: center;
  position: relative;
  z-index: 10;
}

.alarm-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 40rpx;
}

.alarm-title {
  font-size: 48rpx;
  color: #ffffff;
  font-weight: 700;
}

.alarm-close {
  font-size: 36rpx;
  color: rgba(255, 255, 255, 0.7);
  padding: 15rpx;
}

.alarm-main {
  margin-bottom: 50rpx;
}

.alarm-level {
  font-size: 32rpx;
  font-weight: 700;
  padding: 20rpx 40rpx;
  border-radius: 25rpx;
  margin-bottom: 30rpx;
}

.alarm-level.error {
  background: rgba(220, 53, 69, 0.2);
  color: #dc3545;
  border: 1px solid rgba(220, 53, 69, 0.3);
}

.alarm-level.warning {
  background: rgba(255, 193, 7, 0.2);
  color: #ffc107;
  border: 1px solid rgba(255, 193, 7, 0.3);
}

.alarm-data {
  margin-bottom: 30rpx;
}

.alarm-value {
  display: flex;
  align-items: baseline;
  justify-content: center;
  gap: 10rpx;
  margin-bottom: 20rpx;
}

.value-number {
  font-size: 120rpx;
  color: #dc3545;
  font-weight: 700;
  line-height: 1;
}

.value-unit {
  font-size: 36rpx;
  color: rgba(255, 255, 255, 0.8);
}

.alarm-info {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.alarm-message {
  font-size: 28rpx;
  color: #ffffff;
  line-height: 1.4;
}

.alarm-time {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.6);
}

.alarm-location {
  margin-bottom: 30rpx;
  padding: 25rpx;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 15rpx;
}

.location-title {
  display: block;
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 10rpx;
}

.location-coords {
  font-size: 28rpx;
  color: #ffffff;
  font-weight: 600;
}

.alarm-actions {
  display: flex;
  gap: 20rpx;
  justify-content: center;
}

.alarm-btn {
  padding: 25rpx 40rpx;
  border-radius: 25rpx;
  font-size: 26rpx;
  font-weight: 600;
  text-align: center;
  transition: all 0.3s ease;
}

.alarm-btn.primary {
  background: rgba(60, 197, 31, 0.2);
  border: 1px solid rgba(60, 197, 31, 0.3);
  color: #3cc51f;
}

.alarm-btn.secondary {
  background: rgba(255, 193, 7, 0.2);
  border: 1px solid rgba(255, 193, 7, 0.3);
  color: #ffc107;
}

.alarm-btn.danger {
  background: rgba(220, 53, 69, 0.2);
  border: 1px solid rgba(220, 53, 69, 0.3);
  color: #dc3545;
}

.alarm-btn:active {
  transform: scale(0.95);
}

/* 脉冲动画 */
.pulse-animation {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1;
}

.pulse-ring {
  position: absolute;
  width: 600rpx;
  height: 600rpx;
  border: 2px solid rgba(220, 53, 69, 0.3);
  border-radius: 50%;
  animation: pulseProp 2s infinite;
}

.pulse-ring:nth-child(2) {
  animation-delay: 0.5s;
}

.pulse-ring:nth-child(3) {
  animation-delay: 1s;
}

@keyframes pulseProp {
  0% {
    transform: scale(0.1);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 0;
  }
}

/* 标记详情弹窗 */
.marker-popup {
  position: absolute;
  bottom: 200rpx;
  left: 20rpx;
  right: 20rpx;
  z-index: 300;
  animation: slideUp 0.3s ease;
}

.popup-content {
  background: rgba(26, 26, 46, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20rpx;
  padding: 30rpx;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.popup-title {
  font-size: 28rpx;
  color: #ffffff;
  font-weight: 600;
}

.popup-close {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.7);
  padding: 10rpx;
}

.popup-body {
  margin-bottom: 25rpx;
}

.popup-info {
  margin-bottom: 20rpx;
}

.info-text {
  display: block;
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.4;
  margin-bottom: 10rpx;
}

.info-time {
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.5);
}

.popup-data {
  display: flex;
  gap: 30rpx;
  padding: 20rpx;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 15rpx;
}

.popup-actions {
  display: flex;
  gap: 15rpx;
}

.popup-btn {
  flex: 1;
  text-align: center;
  padding: 20rpx;
  background: rgba(60, 197, 31, 0.1);
  border: 1px solid rgba(60, 197, 31, 0.3);
  border-radius: 15rpx;
  color: #ffffff;
  font-size: 22rpx;
}

/* 底部统计栏 */
.bottom-stats {
  position: absolute;
  bottom: 120rpx;
  left: 20rpx;
  right: 20rpx;
  display: flex;
  gap: 20rpx;
  z-index: 50;
}

.stats-item {
  flex: 1;
  background: rgba(26, 26, 46, 0.9);
  backdrop-filter: blur(20px);
  border-radius: 15rpx;
  padding: 20rpx;
  text-align: center;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.stats-label {
  display: block;
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.6);
  margin-bottom: 8rpx;
}

.stats-value {
  font-size: 24rpx;
  color: #ffffff;
  font-weight: 600;
}
</style> 