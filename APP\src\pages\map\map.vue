<template>
  <view class="map-container">
    <!-- 页面头部 -->
    <view class="map-header">
      <view class="header-left">
        <text class="page-title">位置视图</text>
        <view class="location-status" :class="{ active: deviceState.connection.gps }">
          <text class="status-icon">�️</text>
          <text class="status-text">{{ deviceState.connection.gps ? '已定位' : '定位中' }}</text>
        </view>
      </view>
      <view class="header-right">
        <view class="satellite-info">
          <text class="satellite-icon">📡</text>
          <text class="satellite-count">{{ gpsInfo.satelliteCount }}</text>
        </view>
        <view class="signal-quality">
          <text class="signal-icon">�</text>
          <text class="signal-text">{{ getSignalQualityText(gpsInfo.signalQuality) }}</text>
        </view>
      </view>
    </view>

    <!-- GPS信息卡片 -->
    <view class="gps-info-card">
      <view class="gps-header">
        <text class="gps-title">GPS状态</text>
        <view class="gps-status" :class="gpsStatusClass">
          <text class="status-dot"></text>
          <text class="status-label">{{ gpsStatusText }}</text>
        </view>
      </view>
      <view class="gps-details">
        <view class="gps-item">
          <text class="gps-label">卫星数量</text>
          <text class="gps-value">{{ gpsInfo.satelliteCount }} 颗</text>
        </view>
        <view class="gps-item">
          <text class="gps-label">信号质量</text>
          <text class="gps-value">{{ gpsInfo.signalQuality }}/31</text>
        </view>
        <view class="gps-item">
          <text class="gps-label">定位精度</text>
          <text class="gps-value">± {{ locationState.current.accuracy }} m</text>
        </view>
        <view class="gps-item">
          <text class="gps-label">海拔高度</text>
          <text class="gps-value">{{ locationState.current.altitude }} m</text>
        </view>
      </view>
    </view>

    <!-- 地图主体 -->
    <view class="map-main">
      <map
        id="radiation-map"
        class="map-canvas"
        :longitude="mapCenter.longitude"
        :latitude="mapCenter.latitude"
        :scale="mapScale"
        :markers="mapMarkers"
        :polyline="mapPolylines"
        :enable-satellite="enableSatellite"
        :enable-traffic="enableTraffic"
        @markertap="onMarkerTap"
        @regionchange="onRegionChange"
        @tap="onMapTap">

        <!-- 地图控制按钮 -->
        <cover-view class="map-controls">
          <cover-view class="control-group zoom-controls">
            <cover-view class="control-button" @tap="zoomIn">
              <cover-view class="control-icon">+</cover-view>
            </cover-view>
            <cover-view class="control-button" @tap="zoomOut">
              <cover-view class="control-icon">-</cover-view>
            </cover-view>
          </cover-view>

          <cover-view class="control-group layer-controls">
            <cover-view class="control-button" @tap="toggleSatellite" :class="{ active: enableSatellite }">
              <cover-view class="control-icon">卫星</cover-view>
            </cover-view>
            <cover-view class="control-button" @tap="toggleTraffic" :class="{ active: enableTraffic }">
              <cover-view class="control-icon">路况</cover-view>
            </cover-view>
          </cover-view>

          <cover-view class="control-group location-controls">
            <cover-view class="control-button" @tap="centerToLocation">
              <cover-view class="control-icon">�</cover-view>
            </cover-view>
          </cover-view>
        </cover-view>

        <!-- 实时辐射监测悬浮窗 -->
        <cover-view class="radiation-overlay">
          <cover-view class="radiation-card" :class="radiationLevelClass">
            <cover-view class="radiation-header">
              <cover-view class="radiation-icon">☢️</cover-view>
              <cover-view class="radiation-title">实时监测</cover-view>
              <cover-view class="radiation-status" :class="radiationLevelClass">
                {{ radiationStatusText }}
              </cover-view>
            </cover-view>
            <cover-view class="radiation-data">
              <cover-view class="data-main">
                <cover-view class="dose-value">{{ radiationState.currentData.doseRate.toFixed(3) }}</cover-view>
                <cover-view class="dose-unit">μSv/h</cover-view>
              </cover-view>
              <cover-view class="data-secondary">
                <cover-view class="data-item">
                  <cover-view class="data-label">计数率</cover-view>
                  <cover-view class="data-value">{{ radiationState.currentData.cps.toFixed(1) }} CPS</cover-view>
                </cover-view>
                <cover-view class="data-item">
                  <cover-view class="data-label">累积剂量</cover-view>
                  <cover-view class="data-value">{{ radiationState.currentData.doseSum.toFixed(3) }} μSv</cover-view>
                </cover-view>
              </cover-view>
            </cover-view>
          </cover-view>
        </cover-view>
      </map>
    </view>

    <!-- 地图图层控制面板 -->
    <view class="layer-panel">
      <view class="panel-header">
        <text class="panel-title">图层控制</text>
        <view class="toggle-button" @tap="toggleLayerPanel">
          <text class="toggle-icon">{{ showLayerPanel ? '▼' : '▲' }}</text>
        </view>
      </view>
      <view class="layer-content" v-if="showLayerPanel">
        <view class="layer-options">
          <view class="layer-option" @tap="toggleHeatmap">
            <view class="option-info">
              <text class="option-icon">🔥</text>
              <text class="option-text">辐射热力图</text>
            </view>
            <view class="option-toggle" :class="{ active: showHeatmap }">
              <view class="toggle-knob"></view>
            </view>
          </view>
          <view class="layer-option" @tap="toggleTrajectory">
            <view class="option-info">
              <text class="option-icon">📈</text>
              <text class="option-text">历史轨迹</text>
            </view>
            <view class="option-toggle" :class="{ active: showTrajectory }">
              <view class="toggle-knob"></view>
            </view>
          </view>
          <view class="layer-option" @tap="toggleAlertPoints">
            <view class="option-info">
              <text class="option-icon">⚠️</text>
              <text class="option-text">报警点位</text>
            </view>
            <view class="option-toggle" :class="{ active: showAlertPoints }">
              <view class="toggle-knob"></view>
            </view>
          </view>
          <view class="layer-option" @tap="toggleMonitoringPoints">
            <view class="option-info">
              <text class="option-icon">📡</text>
              <text class="option-text">监测点位</text>
            </view>
            <view class="option-toggle" :class="{ active: showMonitoringPoints }">
              <view class="toggle-knob"></view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 位置详情面板 -->
    <view class="location-detail-panel" v-if="showLocationPanel">
      <view class="detail-header">
        <text class="detail-title">位置详情</text>
        <text class="detail-close" @tap="hideLocationPanel">✕</text>
      </view>
      <view class="detail-content">
        <view class="coordinate-section">
          <text class="section-title">坐标信息</text>
          <view class="coordinate-grid">
            <view class="coordinate-item">
              <text class="coord-label">纬度</text>
              <text class="coord-value">{{ locationState.current.latitude.toFixed(6) }}°</text>
            </view>
            <view class="coordinate-item">
              <text class="coord-label">经度</text>
              <text class="coord-value">{{ locationState.current.longitude.toFixed(6) }}°</text>
            </view>
            <view class="coordinate-item">
              <text class="coord-label">海拔</text>
              <text class="coord-value">{{ locationState.current.altitude }} m</text>
            </view>
            <view class="coordinate-item">
              <text class="coord-label">精度</text>
              <text class="coord-value">± {{ locationState.current.accuracy }} m</text>
            </view>
          </view>
        </view>

        <view class="radiation-section">
          <text class="section-title">辐射监测</text>
          <view class="radiation-grid">
            <view class="radiation-item">
              <text class="radiation-label">剂量率</text>
              <text class="radiation-value" :class="radiationLevelClass">{{ radiationState.currentData.doseRate.toFixed(3) }} μSv/h</text>
            </view>
            <view class="radiation-item">
              <text class="radiation-label">安全评估</text>
              <text class="radiation-value" :class="radiationLevelClass">{{ radiationStatusText }}</text>
            </view>
          </view>
        </view>

        <view class="action-section">
          <view class="action-buttons">
            <view class="action-btn primary" @tap="shareLocation">
              <text class="btn-icon">📤</text>
              <text class="btn-text">分享位置</text>
            </view>
            <view class="action-btn secondary" @tap="addPOI">
              <text class="btn-icon">📌</text>
              <text class="btn-text">添加标记</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 大屏报警展示 -->
    <view class="alarm-screen" v-if="showAlarmScreen">
      <view class="alarm-overlay">
        <view class="alarm-content">
          <view class="alarm-header">
            <text class="alarm-title">⚠️ 辐射报警</text>
            <text class="alarm-close" @tap="hideAlarmScreen">✕</text>
          </view>
          
          <view class="alarm-main">
            <view class="alarm-level" :class="currentAlarm?.level">
              <text class="level-text">{{ getAlarmLevelText(currentAlarm?.level) }}</text>
            </view>
            
            <view class="alarm-data">
              <view class="alarm-value">
                <text class="value-number">{{ radiationState.currentData.doseRate.toFixed(3) }}</text>
                <text class="value-unit">μSv/h</text>
              </view>
              
              <view class="alarm-info">
                <text class="alarm-message">{{ currentAlarm?.message }}</text>
                <text class="alarm-time">{{ formatAlarmTime(currentAlarm?.timestamp) }}</text>
              </view>
            </view>
            
            <view class="alarm-location" v-if="currentAlarm?.location">
              <text class="location-title">报警位置</text>
              <text class="location-coords">
                {{ currentAlarm.location.latitude.toFixed(4) }}, {{ currentAlarm.location.longitude.toFixed(4) }}
              </text>
            </view>
          </view>
          
          <view class="alarm-actions">
            <text class="alarm-btn primary" @tap="confirmAlarm">确认报警</text>
            <text class="alarm-btn secondary" @tap="silenceAlarm">静音报警</text>
            <text class="alarm-btn danger" @tap="emergencyCall">紧急呼叫</text>
          </view>
        </view>
        
        <!-- 脉冲动画 -->
        <view class="pulse-animation">
          <view class="pulse-ring"></view>
          <view class="pulse-ring"></view>
          <view class="pulse-ring"></view>
        </view>
      </view>
    </view>

    <!-- 标记详情弹窗 -->
    <view class="marker-popup" v-if="selectedMarker">
      <view class="popup-content">
        <view class="popup-header">
          <text class="popup-title">{{ selectedMarker.title }}</text>
          <text class="popup-close" @tap="closeMarkerPopup">✕</text>
        </view>
        <view class="popup-body">
          <view class="popup-info">
            <text class="info-text">{{ selectedMarker.description }}</text>
            <text class="info-time">{{ formatTime(selectedMarker.timestamp) }}</text>
          </view>
          <view class="popup-data" v-if="selectedMarker.data">
            <view class="data-item">
              <text class="data-label">剂量率</text>
              <text class="data-value">{{ selectedMarker.data.doseRate }} μSv/h</text>
            </view>
            <view class="data-item">
              <text class="data-label">计数率</text>
              <text class="data-value">{{ selectedMarker.data.cps }} CPS</text>
            </view>
          </view>
        </view>
        <view class="popup-actions">
          <text class="popup-btn" @tap="navigateToMarker">📍 导航</text>
          <text class="popup-btn" @tap="shareMarker">📤 分享</text>
        </view>
      </view>
    </view>

    <!-- 底部统计信息 -->
    <view class="bottom-stats">
      <view class="stats-container">
        <view class="stats-item">
          <view class="stats-icon">📡</view>
          <view class="stats-content">
            <text class="stats-value">{{ monitoringPointsCount }}</text>
            <text class="stats-label">监测点位</text>
          </view>
        </view>
        <view class="stats-item">
          <view class="stats-icon">⚠️</view>
          <view class="stats-content">
            <text class="stats-value">{{ alertPointsCount }}</text>
            <text class="stats-label">报警点位</text>
          </view>
        </view>
        <view class="stats-item">
          <view class="stats-icon">📈</view>
          <view class="stats-content">
            <text class="stats-value">{{ trajectoryLength.toFixed(1) }}km</text>
            <text class="stats-label">轨迹长度</text>
          </view>
        </view>
        <view class="stats-item">
          <view class="stats-icon">🗺️</view>
          <view class="stats-content">
            <text class="stats-value">{{ coverageArea.toFixed(1) }}km²</text>
            <text class="stats-label">覆盖范围</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 快速操作按钮 -->
    <view class="quick-actions">
      <view class="action-item" @tap="toggleLayer">
        <text class="action-icon">🗺️</text>
        <text class="action-text">图层</text>
      </view>
      <view class="action-item" @tap="showLocationInfo">
        <text class="action-icon">📍</text>
        <text class="action-text">位置</text>
      </view>
      <view class="action-item emergency" @tap="showAlarmScreen" v-if="hasActiveAlarm">
        <text class="action-icon">🚨</text>
        <text class="action-text">报警</text>
      </view>
    </view>
  </view>
</template>

<script>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { radiationState, deviceState, locationState } from '../../utils/dataStore.js'

export default {
  name: 'MapView',
  setup() {
    const mapCenter = ref({
      latitude: 39.9042,
      longitude: 116.4074
    })
    const mapScale = ref(15)
    const showLayerPanel = ref(false)
    const showLocationPanel = ref(false)
    const showAlarmScreen = ref(false)
    const showHeatmap = ref(false)
    const showTrajectory = ref(true)
    const showAlertPoints = ref(true)
    const showMonitoringPoints = ref(true)
    const enableSatellite = ref(false)
    const enableTraffic = ref(false)
    const selectedMarker = ref(null)

    // GPS信息
    const gpsInfo = ref({
      satelliteCount: 8,      // 卫星数量
      signalQuality: 25,      // 信号质量 (0-31)
      isFixed: true,          // 是否定位成功
      accuracy: 3.5,          // 定位精度
      lastUpdateTime: Date.now()
    })

    // 地图标记和路线
    const mapMarkers = ref([])
    const mapPolylines = ref([])

    // 计算属性
    const radiationLevel = computed(() => {
      const doseRate = radiationState.currentData.doseRate
      const { maxDoseRate, minDoseRate } = radiationState.settings

      if (doseRate > maxDoseRate) return 'danger'
      if (doseRate < minDoseRate) return 'warning'
      return 'safe'
    })

    const radiationLevelClass = computed(() => {
      return `level-${radiationLevel.value}`
    })

    const radiationStatusText = computed(() => {
      switch (radiationLevel.value) {
        case 'danger': return '危险区域'
        case 'warning': return '注意区域'
        default: return '安全区域'
      }
    })

    const hasActiveAlarm = computed(() => {
      return radiationState.alerts.length > 0 &&
             radiationState.alerts[0].timestamp > (Date.now() - 300000) // 5分钟内的报警
    })

    const currentAlarm = computed(() => {
      return hasActiveAlarm.value ? radiationState.alerts[0] : null
    })

    // GPS状态计算属性
    const gpsStatusClass = computed(() => {
      if (gpsInfo.value.satelliteCount >= 4 && gpsInfo.value.signalQuality > 15) {
        return 'good'
      } else if (gpsInfo.value.satelliteCount >= 3 && gpsInfo.value.signalQuality > 10) {
        return 'fair'
      } else {
        return 'poor'
      }
    })

    const gpsStatusText = computed(() => {
      switch (gpsStatusClass.value) {
        case 'good': return '信号良好'
        case 'fair': return '信号一般'
        case 'poor': return '信号较弱'
        default: return '无信号'
      }
    })

    // 获取信号质量文本
    const getSignalQualityText = (quality) => {
      if (quality > 20) return '强'
      if (quality > 15) return '良'
      if (quality > 10) return '中'
      if (quality > 5) return '弱'
      return '无'
    }

    const monitoringPointsCount = computed(() => {
      return mapMarkers.value.filter(marker => marker.type === 'monitoring').length
    })

    const alertPointsCount = computed(() => {
      return mapMarkers.value.filter(marker => marker.type === 'alert').length
    })

    const trajectoryLength = computed(() => {
      if (locationState.history.length < 2) return 0
      
      let totalDistance = 0
      for (let i = 1; i < locationState.history.length; i++) {
        const prev = locationState.history[i - 1]
        const curr = locationState.history[i]
        totalDistance += calculateDistance(prev.latitude, prev.longitude, curr.latitude, curr.longitude)
      }
      return totalDistance
    })

    const coverageArea = computed(() => {
      if (locationState.history.length < 3) return 0
      
      // 简单的包围盒面积计算
      const lats = locationState.history.map(point => point.latitude)
      const lngs = locationState.history.map(point => point.longitude)
      
      const latRange = Math.max(...lats) - Math.min(...lats)
      const lngRange = Math.max(...lngs) - Math.min(...lngs)
      
      // 近似计算（1度约111km）
      return latRange * lngRange * 111 * 111
    })

    // 方法
    const toggleLayer = () => {
      showLayerPanel.value = !showLayerPanel.value
    }

    const toggleLayerPanel = () => {
      showLayerPanel.value = !showLayerPanel.value
    }

    const showLocationInfo = () => {
      showLocationPanel.value = true
    }

    const hideLocationPanel = () => {
      showLocationPanel.value = false
    }

    const showAlarmScreen = () => {
      showAlarmScreen.value = true
    }

    const hideAlarmScreen = () => {
      showAlarmScreen.value = false
    }

    const centerToLocation = () => {
      mapCenter.value = {
        latitude: locationState.current.latitude,
        longitude: locationState.current.longitude
      }
      uni.showToast({
        title: '已定位到当前位置',
        icon: 'success'
      })
    }

    const zoomIn = () => {
      if (mapScale.value < 18) {
        mapScale.value += 1
      }
    }

    const zoomOut = () => {
      if (mapScale.value > 3) {
        mapScale.value -= 1
      }
    }

    const toggleSatellite = () => {
      enableSatellite.value = !enableSatellite.value
    }

    const toggleTraffic = () => {
      enableTraffic.value = !enableTraffic.value
    }

    const toggleHeatmap = () => {
      showHeatmap.value = !showHeatmap.value
      updateMapLayers()
    }

    const toggleTrajectory = () => {
      showTrajectory.value = !showTrajectory.value
      updateMapLayers()
    }

    const toggleAlertPoints = () => {
      showAlertPoints.value = !showAlertPoints.value
      updateMapMarkers()
    }

    const toggleMonitoringPoints = () => {
      showMonitoringPoints.value = !showMonitoringPoints.value
      updateMapMarkers()
    }

    const updateMapMarkers = () => {
      const markers = []

      // 当前位置标记 - 根据辐射水平选择不同图标
      const currentLocationIcon = radiationLevel.value === 'danger' ? '🚨' :
                                  radiationLevel.value === 'warning' ? '⚠️' : '📍'

      markers.push({
        id: 'current',
        latitude: locationState.current.latitude,
        longitude: locationState.current.longitude,
        iconPath: '/static/icons/current-location.png',
        width: 40,
        height: 40,
        title: `当前位置 ${currentLocationIcon}`,
        type: 'current',
        data: radiationState.currentData,
        callout: {
          content: `${radiationState.currentData.doseRate.toFixed(3)} μSv/h`,
          color: radiationLevel.value === 'danger' ? '#dc3545' :
                 radiationLevel.value === 'warning' ? '#ffc107' : '#28a745',
          fontSize: 12,
          borderRadius: 5,
          bgColor: '#ffffff',
          padding: 5,
          display: 'ALWAYS'
        }
      })

      // 如果当前位置辐射超标，添加特殊的报警标记
      if (radiationLevel.value === 'danger') {
        markers.push({
          id: 'current-alert',
          latitude: locationState.current.latitude,
          longitude: locationState.current.longitude,
          iconPath: '/static/icons/radiation-alert.png',
          width: 50,
          height: 50,
          title: '⚠️ 辐射报警',
          type: 'radiation-alert',
          data: {
            ...radiationState.currentData,
            alertLevel: 'danger',
            alertMessage: '当前位置辐射水平超标，请立即撤离！'
          },
          callout: {
            content: '⚠️ 辐射超标',
            color: '#ffffff',
            fontSize: 14,
            borderRadius: 8,
            bgColor: '#dc3545',
            padding: 8,
            display: 'ALWAYS'
          }
        })
      }

      // 监测点位标记
      if (showMonitoringPoints.value) {
        radiationState.history.forEach((point, index) => {
          if (index % 10 === 0) { // 每10个点显示一个标记
            const pointLocation = locationState.history[Math.min(index, locationState.history.length - 1)]
            const lat = pointLocation?.latitude || point.latitude || locationState.current.latitude
            const lng = pointLocation?.longitude || point.longitude || locationState.current.longitude

            // 根据该点的辐射水平确定标记样式
            const pointLevel = point.doseRate > radiationState.settings.maxDoseRate ? 'danger' :
                              point.doseRate < radiationState.settings.minDoseRate ? 'warning' : 'safe'

            markers.push({
              id: `monitoring-${index}`,
              latitude: lat,
              longitude: lng,
              iconPath: '/static/icons/monitoring-point.png',
              width: 30,
              height: 30,
              title: `监测点 ${pointLevel === 'danger' ? '🚨' : pointLevel === 'warning' ? '⚠️' : '✅'}`,
              type: 'monitoring',
              data: point,
              timestamp: point.timestamp,
              callout: pointLevel !== 'safe' ? {
                content: `${point.doseRate?.toFixed(3) || 'N/A'} μSv/h`,
                color: pointLevel === 'danger' ? '#dc3545' : '#ffc107',
                fontSize: 10,
                borderRadius: 3,
                bgColor: '#ffffff',
                padding: 3,
                display: 'BYCLICK'
              } : null
            })
          }
        })
      }

      // 报警点位标记
      if (showAlertPoints.value) {
        radiationState.alerts.forEach((alert, index) => {
          if (alert.location) {
            markers.push({
              id: `alert-${index}`,
              latitude: alert.location.latitude,
              longitude: alert.location.longitude,
              iconPath: '/static/icons/alert-point.png',
              width: 35,
              height: 35,
              title: '🚨 报警点位',
              type: 'alert',
              data: {
                doseRate: alert.doseRate,
                doseSum: alert.doseSum
              },
              timestamp: alert.timestamp,
              description: alert.message,
              callout: {
                content: `⚠️ ${alert.message}`,
                color: '#ffffff',
                fontSize: 12,
                borderRadius: 5,
                bgColor: '#dc3545',
                padding: 5,
                display: 'ALWAYS'
              }
            })
          }
        })
      }

      mapMarkers.value = markers
    }

    const updateMapLayers = () => {
      const polylines = []

      // 历史轨迹
      if (showTrajectory.value && locationState.history.length > 1) {
        const points = locationState.history.slice(-50).map(point => ({
          latitude: point.latitude,
          longitude: point.longitude
        }))

        polylines.push({
          points,
          color: '#3cc51f',
          width: 4,
          dottedLine: false,
          arrowLine: true
        })
      }

      mapPolylines.value = polylines
    }

    const onMarkerTap = (e) => {
      const markerId = e.detail.markerId
      const marker = mapMarkers.value.find(m => m.id === markerId)
      if (marker) {
        selectedMarker.value = marker
      }
    }

    const closeMarkerPopup = () => {
      selectedMarker.value = null
    }

    const onRegionChange = (e) => {
      if (e.type === 'end') {
        mapCenter.value = {
          latitude: e.detail.centerLocation.latitude,
          longitude: e.detail.centerLocation.longitude
        }
      }
    }

    const onMapTap = () => {
      // 点击地图空白区域时关闭弹窗
      selectedMarker.value = null
      showLayerPanel.value = false
      showLocationPanel.value = false
    }

    const formatCoordinate = (lat, lng) => {
      return `${lat.toFixed(4)}, ${lng.toFixed(4)}`
    }

    const formatTime = (timestamp) => {
      return new Date(timestamp).toLocaleString('zh-CN', {
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      })
    }

    const formatAlarmTime = (timestamp) => {
      const now = Date.now()
      const diff = now - timestamp
      
      if (diff < 60000) return '刚刚'
      if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`
      if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`
      return new Date(timestamp).toLocaleDateString('zh-CN')
    }

    const getAlarmLevelText = (level) => {
      const levelMap = {
        'error': '严重报警',
        'warning': '警告报警',
        'info': '提示报警'
      }
      return levelMap[level] || '未知报警'
    }

    const calculateDistance = (lat1, lng1, lat2, lng2) => {
      const R = 6371 // 地球半径（公里）
      const dLat = (lat2 - lat1) * Math.PI / 180
      const dLng = (lng2 - lng1) * Math.PI / 180
      const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
                Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
                Math.sin(dLng/2) * Math.sin(dLng/2)
      const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a))
      return R * c
    }

    const shareLocation = () => {
      const location = locationState.current
      const message = `我的位置：${location.latitude.toFixed(6)}, ${location.longitude.toFixed(6)}\n当前辐射水平：${radiationState.currentData.doseRate.toFixed(3)} μSv/h`
      
      uni.showModal({
        title: '分享位置',
        content: message,
        confirmText: '复制',
        success: (res) => {
          if (res.confirm) {
            uni.setClipboardData({
              data: message,
              success: () => {
                uni.showToast({
                  title: '已复制到剪贴板',
                  icon: 'success'
                })
              }
            })
          }
        }
      })
    }

    const addPOI = () => {
      uni.showModal({
        title: '添加标记',
        content: '在当前位置添加自定义标记点',
        success: (res) => {
          if (res.confirm) {
            // 添加POI逻辑
            locationState.poi.push({
              latitude: locationState.current.latitude,
              longitude: locationState.current.longitude,
              name: `POI-${Date.now()}`,
              timestamp: Date.now(),
              radiationData: {...radiationState.currentData}
            })
            
            updateMapMarkers()
            
            uni.showToast({
              title: '标记已添加',
              icon: 'success'
            })
          }
        }
      })
    }

    const confirmAlarm = () => {
      uni.showToast({
        title: '报警已确认',
        icon: 'success'
      })
      hideAlarmScreen()
    }

    const silenceAlarm = () => {
      uni.showToast({
        title: '报警已静音',
        icon: 'success'
      })
      hideAlarmScreen()
    }

    const emergencyCall = () => {
      uni.showModal({
        title: '紧急呼叫',
        content: '是否拨打紧急电话？',
        confirmText: '拨打',
        success: (res) => {
          if (res.confirm) {
            uni.makePhoneCall({
              phoneNumber: '120' // 紧急电话
            })
          }
        }
      })
    }

    const navigateToMarker = () => {
      if (selectedMarker.value) {
        const marker = selectedMarker.value
        uni.openLocation({
          latitude: marker.latitude,
          longitude: marker.longitude,
          name: marker.title,
          scale: 18
        })
      }
    }

    const shareMarker = () => {
      if (selectedMarker.value) {
        const marker = selectedMarker.value
        const message = `${marker.title}\n位置：${marker.latitude.toFixed(6)}, ${marker.longitude.toFixed(6)}`
        
        uni.setClipboardData({
          data: message,
          success: () => {
            uni.showToast({
              title: '标记信息已复制',
              icon: 'success'
            })
          }
        })
      }
    }

    // 生命周期
    onMounted(() => {
      // 初始化地图中心为当前位置
      mapCenter.value = {
        latitude: locationState.current.latitude,
        longitude: locationState.current.longitude
      }

      // 更新地图标记和图层
      updateMapMarkers()
      updateMapLayers()

      // 监听位置变化
      const locationWatcher = setInterval(() => {
        updateMapMarkers()
        updateMapLayers()
      }, 10000) // 每10秒更新一次

      onUnmounted(() => {
        clearInterval(locationWatcher)
      })
    })

    return {
      mapCenter,
      mapScale,
      showLayerPanel,
      showLocationPanel,
      showAlarmScreen,
      showHeatmap,
      showTrajectory,
      showAlertPoints,
      showMonitoringPoints,
      enableSatellite,
      enableTraffic,
      selectedMarker,
      mapMarkers,
      mapPolylines,
      gpsInfo,
      radiationState,
      deviceState,
      locationState,
      radiationLevelClass,
      radiationStatusText,
      hasActiveAlarm,
      currentAlarm,
      gpsStatusClass,
      gpsStatusText,
      getSignalQualityText,
      monitoringPointsCount,
      alertPointsCount,
      trajectoryLength,
      coverageArea,
      toggleLayer,
      toggleLayerPanel,
      showLocationInfo,
      hideLocationPanel,
      showAlarmScreen,
      hideAlarmScreen,
      centerToLocation,
      zoomIn,
      zoomOut,
      toggleSatellite,
      toggleTraffic,
      toggleHeatmap,
      toggleTrajectory,
      toggleAlertPoints,
      toggleMonitoringPoints,
      onMarkerTap,
      closeMarkerPopup,
      onRegionChange,
      onMapTap,
      formatCoordinate,
      formatTime,
      formatAlarmTime,
      getAlarmLevelText,
      shareLocation,
      addPOI,
      confirmAlarm,
      silenceAlarm,
      emergencyCall,
      navigateToMarker,
      shareMarker
    }
  }
}
</script>

<style scoped>
.map-container {
  height: 100vh;
  background: #ffffff;
  position: relative;
  overflow: hidden;
}

/* 页面头部 */
.map-header {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  z-index: 100;
  box-shadow: 0 2rpx 20rpx rgba(0, 0, 0, 0.1);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.page-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}

.location-status {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 8rpx 16rpx;
  background: rgba(255, 193, 7, 0.1);
  border-radius: 20rpx;
  border: 1px solid rgba(255, 193, 7, 0.2);
}

.location-status.active {
  background: rgba(40, 167, 69, 0.1);
  border-color: rgba(40, 167, 69, 0.2);
}

.status-icon {
  font-size: 20rpx;
}

.status-text {
  font-size: 20rpx;
  color: #666666;
  font-weight: 500;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.satellite-info,
.signal-quality {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 8rpx 12rpx;
  background: rgba(0, 123, 255, 0.1);
  border-radius: 15rpx;
  border: 1px solid rgba(0, 123, 255, 0.2);
}

.satellite-icon,
.signal-icon {
  font-size: 18rpx;
}

.satellite-count,
.signal-text {
  font-size: 18rpx;
  color: #007bff;
  font-weight: 600;
}

/* GPS信息卡片 */
.gps-info-card {
  position: absolute;
  top: 100rpx;
  left: 20rpx;
  right: 20rpx;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20rpx;
  padding: 25rpx;
  border: 1px solid rgba(0, 0, 0, 0.05);
  box-shadow: 0 4rpx 30rpx rgba(0, 0, 0, 0.1);
  z-index: 90;
}

.gps-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.gps-title {
  font-size: 24rpx;
  font-weight: 600;
  color: #333333;
}

.gps-status {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 6rpx 12rpx;
  border-radius: 15rpx;
}

.gps-status.good {
  background: rgba(40, 167, 69, 0.1);
  border: 1px solid rgba(40, 167, 69, 0.2);
}

.gps-status.fair {
  background: rgba(255, 193, 7, 0.1);
  border: 1px solid rgba(255, 193, 7, 0.2);
}

.gps-status.poor {
  background: rgba(220, 53, 69, 0.1);
  border: 1px solid rgba(220, 53, 69, 0.2);
}

.status-dot {
  width: 8rpx;
  height: 8rpx;
  border-radius: 50%;
  background: currentColor;
}

.status-label {
  font-size: 18rpx;
  font-weight: 500;
}

.gps-status.good .status-label {
  color: #28a745;
}

.gps-status.fair .status-label {
  color: #ffc107;
}

.gps-status.poor .status-label {
  color: #dc3545;
}

.gps-details {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15rpx;
}

.gps-item {
  display: flex;
  flex-direction: column;
  gap: 5rpx;
  padding: 15rpx;
  background: rgba(248, 249, 250, 0.8);
  border-radius: 12rpx;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.gps-label {
  font-size: 18rpx;
  color: #666666;
}

.gps-value {
  font-size: 20rpx;
  font-weight: 600;
  color: #333333;
}

/* 地图主体 */
.map-main {
  height: 100vh;
  position: relative;
  margin-top: 200rpx;
}

.map-canvas {
  width: 100%;
  height: calc(100% - 200rpx);
  border-radius: 20rpx 20rpx 0 0;
  overflow: hidden;
}

/* 地图控件 */
.map-controls {
  position: absolute;
  right: 20rpx;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.control-group {
  display: flex;
  flex-direction: column;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 15rpx;
  overflow: hidden;
  border: 1px solid rgba(0, 0, 0, 0.05);
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.zoom-controls .control-button {
  width: 70rpx;
  height: 70rpx;
}

.layer-controls .control-button {
  width: 70rpx;
  height: 50rpx;
}

.location-controls .control-button {
  width: 70rpx;
  height: 70rpx;
}

.control-button {
  display: flex;
  align-items: center;
  justify-content: center;
  background: transparent;
  transition: all 0.3s ease;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.control-button:last-child {
  border-bottom: none;
}

.control-button:active {
  background: rgba(0, 123, 255, 0.1);
}

.control-button.active {
  background: rgba(0, 123, 255, 0.1);
  color: #007bff;
}

.control-icon {
  font-size: 24rpx;
  color: #333333;
  font-weight: 600;
}

.control-button.active .control-icon {
  color: #007bff;
}

/* 实时辐射监测悬浮窗 */
.radiation-overlay {
  position: absolute;
  bottom: 200rpx;
  left: 20rpx;
  z-index: 50;
}

.radiation-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20rpx;
  padding: 25rpx;
  border: 1px solid rgba(0, 0, 0, 0.05);
  box-shadow: 0 4rpx 30rpx rgba(0, 0, 0, 0.1);
  min-width: 320rpx;
}

.radiation-card.level-danger {
  border-color: rgba(220, 53, 69, 0.3);
  box-shadow: 0 4rpx 30rpx rgba(220, 53, 69, 0.2);
}

.radiation-card.level-warning {
  border-color: rgba(255, 193, 7, 0.3);
  box-shadow: 0 4rpx 30rpx rgba(255, 193, 7, 0.2);
}

.radiation-header {
  display: flex;
  align-items: center;
  gap: 15rpx;
  margin-bottom: 20rpx;
}

.radiation-icon {
  font-size: 24rpx;
}

.radiation-title {
  font-size: 24rpx;
  color: #333333;
  font-weight: 600;
  flex: 1;
}

.radiation-status {
  font-size: 18rpx;
  font-weight: 600;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
}

.radiation-status.level-safe {
  background: rgba(40, 167, 69, 0.1);
  color: #28a745;
  border: 1px solid rgba(40, 167, 69, 0.2);
}

.radiation-status.level-warning {
  background: rgba(255, 193, 7, 0.1);
  color: #ffc107;
  border: 1px solid rgba(255, 193, 7, 0.2);
}

.radiation-status.level-danger {
  background: rgba(220, 53, 69, 0.1);
  color: #dc3545;
  border: 1px solid rgba(220, 53, 69, 0.2);
  animation: dangerPulse 1s infinite;
}

@keyframes dangerPulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

.radiation-data {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.data-main {
  display: flex;
  align-items: baseline;
  gap: 8rpx;
}

.dose-value {
  font-size: 48rpx;
  color: #333333;
  font-weight: 700;
  line-height: 1;
}

.dose-unit {
  font-size: 20rpx;
  color: #666666;
  font-weight: 500;
}

.data-secondary {
  display: flex;
  gap: 20rpx;
}

.data-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 5rpx;
  padding: 12rpx;
  background: rgba(248, 249, 250, 0.8);
  border-radius: 10rpx;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.data-label {
  font-size: 18rpx;
  color: #666666;
}

.data-value {
  font-size: 20rpx;
  color: #333333;
  font-weight: 600;
}

/* 图层控制面板 */
.layer-panel {
  position: absolute;
  left: 20rpx;
  bottom: 150rpx;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20rpx;
  border: 1px solid rgba(0, 0, 0, 0.05);
  box-shadow: 0 4rpx 30rpx rgba(0, 0, 0, 0.1);
  z-index: 200;
  min-width: 400rpx;
  animation: slideUp 0.3s ease;
}

@keyframes slideUp {
  from { transform: translateY(20rpx); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 25rpx;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.panel-title {
  font-size: 24rpx;
  color: #333333;
  font-weight: 600;
}

.toggle-button {
  padding: 8rpx 12rpx;
  background: rgba(0, 123, 255, 0.1);
  border-radius: 12rpx;
  border: 1px solid rgba(0, 123, 255, 0.2);
}

.toggle-icon {
  font-size: 18rpx;
  color: #007bff;
  font-weight: 600;
}

.layer-content {
  padding: 20rpx 25rpx;
}

.layer-options {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.layer-option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15rpx;
  background: rgba(248, 249, 250, 0.8);
  border-radius: 12rpx;
  border: 1px solid rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.layer-option:active {
  background: rgba(0, 123, 255, 0.05);
}

.option-info {
  display: flex;
  align-items: center;
  gap: 15rpx;
}

.option-icon {
  font-size: 24rpx;
}

.option-text {
  font-size: 22rpx;
  color: #333333;
  font-weight: 500;
}

.option-toggle {
  width: 60rpx;
  height: 30rpx;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 15rpx;
  position: relative;
  transition: all 0.3s ease;
}

.option-toggle.active {
  background: #007bff;
}

.toggle-knob {
  width: 26rpx;
  height: 26rpx;
  background: #ffffff;
  border-radius: 50%;
  position: absolute;
  top: 2rpx;
  left: 2rpx;
  transition: all 0.3s ease;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.option-toggle.active .toggle-knob {
  left: 32rpx;
}

/* 位置详情面板 */
.location-detail-panel {
  position: absolute;
  right: 20rpx;
  bottom: 150rpx;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20rpx;
  border: 1px solid rgba(0, 0, 0, 0.05);
  box-shadow: 0 4rpx 30rpx rgba(0, 0, 0, 0.1);
  z-index: 200;
  min-width: 450rpx;
  animation: slideUp 0.3s ease;
}

.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 25rpx;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.detail-title {
  font-size: 24rpx;
  color: #333333;
  font-weight: 600;
}

.detail-close {
  font-size: 24rpx;
  color: #666666;
  padding: 8rpx;
  background: rgba(0, 0, 0, 0.05);
  border-radius: 50%;
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.detail-content {
  padding: 25rpx;
  display: flex;
  flex-direction: column;
  gap: 25rpx;
}

.section-title {
  font-size: 20rpx;
  color: #666666;
  font-weight: 600;
  margin-bottom: 15rpx;
  text-transform: uppercase;
  letter-spacing: 1rpx;
}

.coordinate-section,
.radiation-section {
  display: flex;
  flex-direction: column;
}

.coordinate-grid,
.radiation-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12rpx;
}

.coordinate-item,
.radiation-item {
  display: flex;
  flex-direction: column;
  gap: 5rpx;
  padding: 15rpx;
  background: rgba(248, 249, 250, 0.8);
  border-radius: 12rpx;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.coord-label,
.radiation-label {
  font-size: 18rpx;
  color: #666666;
}

.coord-value,
.radiation-value {
  font-size: 20rpx;
  color: #333333;
  font-weight: 600;
}

.radiation-value.level-safe {
  color: #28a745;
}

.radiation-value.level-warning {
  color: #ffc107;
}

.radiation-value.level-danger {
  color: #dc3545;
}

.action-section {
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  padding-top: 20rpx;
}

.action-buttons {
  display: flex;
  gap: 15rpx;
}

.action-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  padding: 15rpx 20rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
  font-weight: 500;
  transition: all 0.3s ease;
}

.action-btn.primary {
  background: rgba(0, 123, 255, 0.1);
  border: 1px solid rgba(0, 123, 255, 0.2);
  color: #007bff;
}

.action-btn.secondary {
  background: rgba(108, 117, 125, 0.1);
  border: 1px solid rgba(108, 117, 125, 0.2);
  color: #6c757d;
}

.action-btn:active {
  transform: scale(0.98);
}

.btn-icon {
  font-size: 18rpx;
}

.btn-text {
  font-size: 18rpx;
}

/* 大屏报警展示 */
.alarm-screen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  animation: alarmShow 0.5s ease;
}

@keyframes alarmShow {
  from { opacity: 0; }
  to { opacity: 1; }
}

.alarm-overlay {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(220, 53, 69, 0.9) 0%, rgba(139, 0, 0, 0.9) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.alarm-content {
  background: rgba(26, 26, 46, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 30rpx;
  padding: 60rpx;
  border: 2px solid rgba(220, 53, 69, 0.5);
  max-width: 80%;
  text-align: center;
  position: relative;
  z-index: 10;
}

.alarm-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 40rpx;
}

.alarm-title {
  font-size: 48rpx;
  color: #ffffff;
  font-weight: 700;
}

.alarm-close {
  font-size: 36rpx;
  color: rgba(255, 255, 255, 0.7);
  padding: 15rpx;
}

.alarm-main {
  margin-bottom: 50rpx;
}

.alarm-level {
  font-size: 32rpx;
  font-weight: 700;
  padding: 20rpx 40rpx;
  border-radius: 25rpx;
  margin-bottom: 30rpx;
}

.alarm-level.error {
  background: rgba(220, 53, 69, 0.2);
  color: #dc3545;
  border: 1px solid rgba(220, 53, 69, 0.3);
}

.alarm-level.warning {
  background: rgba(255, 193, 7, 0.2);
  color: #ffc107;
  border: 1px solid rgba(255, 193, 7, 0.3);
}

.alarm-data {
  margin-bottom: 30rpx;
}

.alarm-value {
  display: flex;
  align-items: baseline;
  justify-content: center;
  gap: 10rpx;
  margin-bottom: 20rpx;
}

.value-number {
  font-size: 120rpx;
  color: #dc3545;
  font-weight: 700;
  line-height: 1;
}

.value-unit {
  font-size: 36rpx;
  color: rgba(255, 255, 255, 0.8);
}

.alarm-info {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.alarm-message {
  font-size: 28rpx;
  color: #ffffff;
  line-height: 1.4;
}

.alarm-time {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.6);
}

.alarm-location {
  margin-bottom: 30rpx;
  padding: 25rpx;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 15rpx;
}

.location-title {
  display: block;
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 10rpx;
}

.location-coords {
  font-size: 28rpx;
  color: #ffffff;
  font-weight: 600;
}

.alarm-actions {
  display: flex;
  gap: 20rpx;
  justify-content: center;
}

.alarm-btn {
  padding: 25rpx 40rpx;
  border-radius: 25rpx;
  font-size: 26rpx;
  font-weight: 600;
  text-align: center;
  transition: all 0.3s ease;
}

.alarm-btn.primary {
  background: rgba(60, 197, 31, 0.2);
  border: 1px solid rgba(60, 197, 31, 0.3);
  color: #3cc51f;
}

.alarm-btn.secondary {
  background: rgba(255, 193, 7, 0.2);
  border: 1px solid rgba(255, 193, 7, 0.3);
  color: #ffc107;
}

.alarm-btn.danger {
  background: rgba(220, 53, 69, 0.2);
  border: 1px solid rgba(220, 53, 69, 0.3);
  color: #dc3545;
}

.alarm-btn:active {
  transform: scale(0.95);
}

/* 脉冲动画 */
.pulse-animation {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1;
}

.pulse-ring {
  position: absolute;
  width: 600rpx;
  height: 600rpx;
  border: 2px solid rgba(220, 53, 69, 0.3);
  border-radius: 50%;
  animation: pulseProp 2s infinite;
}

.pulse-ring:nth-child(2) {
  animation-delay: 0.5s;
}

.pulse-ring:nth-child(3) {
  animation-delay: 1s;
}

@keyframes pulseProp {
  0% {
    transform: scale(0.1);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 0;
  }
}

/* 标记详情弹窗 */
.marker-popup {
  position: absolute;
  bottom: 200rpx;
  left: 20rpx;
  right: 20rpx;
  z-index: 300;
  animation: slideUp 0.3s ease;
}

.popup-content {
  background: rgba(26, 26, 46, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20rpx;
  padding: 30rpx;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.popup-title {
  font-size: 28rpx;
  color: #ffffff;
  font-weight: 600;
}

.popup-close {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.7);
  padding: 10rpx;
}

.popup-body {
  margin-bottom: 25rpx;
}

.popup-info {
  margin-bottom: 20rpx;
}

.info-text {
  display: block;
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.4;
  margin-bottom: 10rpx;
}

.info-time {
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.5);
}

.popup-data {
  display: flex;
  gap: 30rpx;
  padding: 20rpx;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 15rpx;
}

.popup-actions {
  display: flex;
  gap: 15rpx;
}

.popup-btn {
  flex: 1;
  text-align: center;
  padding: 20rpx;
  background: rgba(60, 197, 31, 0.1);
  border: 1px solid rgba(60, 197, 31, 0.3);
  border-radius: 15rpx;
  color: #ffffff;
  font-size: 22rpx;
}

/* 底部统计栏 */
.bottom-stats {
  position: absolute;
  bottom: 80rpx;
  left: 20rpx;
  right: 20rpx;
  z-index: 50;
}

.stats-container {
  display: flex;
  gap: 15rpx;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20rpx;
  padding: 20rpx;
  border: 1px solid rgba(0, 0, 0, 0.05);
  box-shadow: 0 4rpx 30rpx rgba(0, 0, 0, 0.1);
}

.stats-item {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 12rpx;
  padding: 15rpx;
  background: rgba(248, 249, 250, 0.8);
  border-radius: 15rpx;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.stats-icon {
  font-size: 24rpx;
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 123, 255, 0.1);
  border-radius: 50%;
  border: 1px solid rgba(0, 123, 255, 0.2);
}

.stats-content {
  display: flex;
  flex-direction: column;
  gap: 2rpx;
}

.stats-value {
  font-size: 22rpx;
  color: #333333;
  font-weight: 700;
  line-height: 1;
}

.stats-label {
  font-size: 16rpx;
  color: #666666;
  font-weight: 500;
}

/* 快速操作按钮 */
.quick-actions {
  position: absolute;
  bottom: 20rpx;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 15rpx;
  z-index: 60;
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5rpx;
  padding: 15rpx 20rpx;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20rpx;
  border: 1px solid rgba(0, 0, 0, 0.05);
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.action-item.emergency {
  background: rgba(220, 53, 69, 0.1);
  border-color: rgba(220, 53, 69, 0.3);
  animation: emergencyPulse 1s infinite;
}

@keyframes emergencyPulse {
  0%, 100% {
    background: rgba(220, 53, 69, 0.1);
    box-shadow: 0 4rpx 20rpx rgba(220, 53, 69, 0.2);
  }
  50% {
    background: rgba(220, 53, 69, 0.2);
    box-shadow: 0 4rpx 30rpx rgba(220, 53, 69, 0.4);
  }
}

.action-item:active {
  transform: scale(0.95);
}

.action-icon {
  font-size: 24rpx;
}

.action-text {
  font-size: 18rpx;
  color: #333333;
  font-weight: 500;
}

.action-item.emergency .action-text {
  color: #dc3545;
  font-weight: 600;
}
</style>