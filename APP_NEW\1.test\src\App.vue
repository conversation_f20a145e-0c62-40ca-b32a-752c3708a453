<script>
import NotificationService from '@/utils/notificationService.js';

export default {
  onLaunch: function () {
    console.log('App Launch');
    // 启动通知服务
    NotificationService.start();
  },
  onShow: function () {
    console.log('App Show')
  },
  onHide: function () {
    console.log('App Hide')
  },
}
</script>

<style>
/*每个页面公共css */
page {
  min-height: 100%;
  display: flex;
  flex-direction: column;
  background: linear-gradient(120deg, #e3f0ff 0%, #f7f7f7 100%);
}

/* 确保页面填充满整个屏幕 */
.container {
  flex: 1;
  display: flex;
  flex-direction: column;
  width: 100vw;
  min-height: 100vh;
  overflow-x: hidden;
  align-items: center;
  justify-content: center;
}

/* 原型样式，确保设备尺寸适配 */
.prototype {
  width: 100%;
  max-width: 375px;
  height: auto;
  aspect-ratio: 375/812;
  background: linear-gradient(145deg, #f0f0f0, #fafafa);
  border-radius: 50px;
  overflow: hidden;
  box-shadow: 0 20px 60px -10px rgba(30, 136, 229, 0.18);
  position: relative;
  max-height: 92vh;
}

/* 登录和其他容器的样式调整 */
.login-container,
.home-container,
.register-container,
.reset-password-container,
.settings-container,
.user-container {
  padding: 10% 5%;
  display: flex;
  flex-direction: column;
  height: 100%;
  position: relative;
  overflow: hidden;
  box-shadow: inset 0 0 24px rgba(30, 136, 229, 0.06);
  background: rgba(255,255,255,0.98);
  backdrop-filter: blur(2px);
}

/* 统一输入框样式 */
input {
  border: none;
  border-radius: 18px;
  background-color: #fff;
  color: #333;
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.08);
}

input:focus {
  outline: none;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.12), 0 0 0 2px rgba(30, 136, 229, 0.2);
  transform: translateY(-3px);
}

/* 按钮统一样式 */
button {
  border: none;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
}

/* 确保内容不会被遮挡 */
.content-wrapper {
  position: relative;
  z-index: 1;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 0 10px;
}

/* 媒体查询，适配小屏设备 */
@media screen and (max-width: 480px) {
  .prototype {
    border-radius: 24px;
    max-height: 100vh;
    width: 100%;
    box-shadow: none;
  }
  
  .login-container,
  .home-container,
  .register-container,
  .reset-password-container,
  .settings-container,
  .user-container {
    padding: 10% 8%;
    min-height: 100vh;
  }
  
  .content-wrapper {
    padding: 0;
  }
}
</style>
