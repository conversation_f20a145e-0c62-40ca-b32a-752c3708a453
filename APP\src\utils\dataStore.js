import { reactive, ref } from 'vue'

// 辐射数据状态
export const radiationState = reactive({
  currentData: {
    doseRate: 0,          // 当前剂量率 (μSv/h)
    cps: 0,               // 计数率 (counts per second)
    doseSum: 0,           // 累积剂量 (μSv)
    alarmStatus: 0,       // 报警状态位
    temperature: 0,       // 温度 (°C)
    timestamp: Date.now()
  },
  history: [],            // 历史数据
  alerts: [],             // 报警记录
  settings: {
    maxDoseRate: 1.0,     // 剂量率报警阈值
    minDoseRate: 0.01,    // 最小剂量率阈值
    maxDoseSum: 100.0,    // 累积剂量报警阈值
    autoUpload: true,     // 自动上传开关
    uploadInterval: 60,   // 上传间隔(秒)
    soundAlert: true,     // 声音报警
    vibrationAlert: true  // 震动报警
  }
})

// 设备状态
export const deviceState = reactive({
  battery: {
    level: 85,            // 电池电量百分比
    charging: false,      // 充电状态
    voltage: 4.1         // 电池电压
  },
  connection: {
    mqtt: false,          // MQTT连接状态
    bluetooth: false,     // 蓝牙连接状态
    gps: false,          // GPS状态
    cellular: false       // 移动网络状态
  },
  deviceInfo: {
    imei: '',            // 设备IMEI
    iccid: '',           // SIM卡ICCID
    firmwareVersion: '',  // 固件版本
    serialNumber: ''     // 设备序列号
  }
})

// 位置数据状态
export const locationState = reactive({
  current: {
    latitude: 39.9042,    // 纬度
    longitude: 116.4074,  // 经度
    altitude: 50,         // 海拔
    accuracy: 10,         // 精度
    timestamp: Date.now()
  },
  history: [],           // 位置历史
  poi: []               // 兴趣点
})

// 健康数据状态
export const healthState = reactive({
  current: {
    heartRate: 75,        // 心率 (bpm)
    spO2: 98,            // 血氧饱和度 (%)
    bodyTemp: 36.5,      // 体温 (°C)
    steps: 8543,         // 步数
    timestamp: Date.now()
  },
  history: [],           // 历史数据
  dailyStats: {
    avgHeartRate: 72,
    maxHeartRate: 95,
    minHeartRate: 58,
    avgSpO2: 97
  }
})

// 数据管理类
class DataStore {
  constructor() {
    this.maxHistoryLength = 1000  // 最大历史记录数
    this.storageKey = 'radiationAppData'
    this.loadFromStorage()
  }

  // 更新辐射数据
  updateRadiationData(data) {
    // 更新当前数据
    Object.assign(radiationState.currentData, {
      ...data,
      timestamp: Date.now()
    })

    // 添加到历史记录
    this.addToHistory(radiationState.history, {...radiationState.currentData})

    // 检查报警条件
    this.checkAlerts(data)

    // 保存到本地存储
    this.saveToStorage()
  }

  // 检查报警条件
  checkAlerts(data) {
    const { doseRate, doseSum, alarmStatus } = data
    const { maxDoseRate, minDoseRate, maxDoseSum } = radiationState.settings

    let alertType = null
    let alertMessage = ''
    let alertLevel = 'info'

    // 检查剂量率过高
    if (alarmStatus & 0x02) {
      alertType = 'high_dose_rate'
      alertMessage = `剂量率过高: ${doseRate.toFixed(3)} μSv/h (阈值: ${maxDoseRate} μSv/h)`
      alertLevel = 'error'
    }
    // 检查剂量率过低
    else if (alarmStatus & 0x01) {
      alertType = 'low_dose_rate'
      alertMessage = `剂量率异常低: ${doseRate.toFixed(3)} μSv/h (阈值: ${minDoseRate} μSv/h)`
      alertLevel = 'warning'
    }
    // 检查累积剂量过高
    else if (alarmStatus & 0x04) {
      alertType = 'high_dose_sum'
      alertMessage = `累积剂量过高: ${doseSum.toFixed(3)} μSv (阈值: ${maxDoseSum} μSv)`
      alertLevel = 'error'
    }

    if (alertType) {
      const alert = {
        type: alertType,
        message: alertMessage,
        level: alertLevel,
        doseRate,
        doseSum,
        timestamp: Date.now(),
        location: {...locationState.current}
      }

      radiationState.alerts.unshift(alert)
      
      // 限制报警记录数量
      if (radiationState.alerts.length > 100) {
        radiationState.alerts = radiationState.alerts.slice(0, 100)
      }

      // 触发系统通知
      this.showAlert(alert)
    }
  }

  // 显示报警通知
  showAlert(alert) {
    const { level, message } = alert
    
    // 使用uni.showToast显示通知
    uni.showToast({
      title: message,
      icon: level === 'error' ? 'error' : 'none',
      duration: 3000,
      mask: true
    })

    // 震动反馈
    if (radiationState.settings.vibrationAlert) {
      uni.vibrateShort()
    }
  }

  // 更新设备状态
  updateDeviceStatus(data) {
    Object.assign(deviceState, data)
    this.saveToStorage()
  }

  // 更新位置数据
  updateLocationData(data) {
    Object.assign(locationState.current, {
      ...data,
      timestamp: Date.now()
    })

    this.addToHistory(locationState.history, {...locationState.current})
    this.saveToStorage()
  }

  // 更新健康数据
  updateHealthData(data) {
    Object.assign(healthState.current, {
      ...data,
      timestamp: Date.now()
    })

    this.addToHistory(healthState.history, {...healthState.current})
    this.calculateDailyStats()
    this.saveToStorage()
  }

  // 计算每日统计数据
  calculateDailyStats() {
    const today = new Date().toDateString()
    const todayData = healthState.history.filter(item => 
      new Date(item.timestamp).toDateString() === today
    )

    if (todayData.length > 0) {
      const heartRates = todayData.map(item => item.heartRate).filter(hr => hr > 0)
      const spO2Values = todayData.map(item => item.spO2).filter(sp => sp > 0)

      if (heartRates.length > 0) {
        healthState.dailyStats.avgHeartRate = Math.round(
          heartRates.reduce((sum, hr) => sum + hr, 0) / heartRates.length
        )
        healthState.dailyStats.maxHeartRate = Math.max(...heartRates)
        healthState.dailyStats.minHeartRate = Math.min(...heartRates)
      }

      if (spO2Values.length > 0) {
        healthState.dailyStats.avgSpO2 = Math.round(
          spO2Values.reduce((sum, sp) => sum + sp, 0) / spO2Values.length
        )
      }
    }
  }

  // 添加到历史记录
  addToHistory(historyArray, data) {
    historyArray.unshift(data)
    
    // 限制历史记录长度
    if (historyArray.length > this.maxHistoryLength) {
      historyArray.splice(this.maxHistoryLength)
    }
  }

  // 保存到本地存储
  saveToStorage() {
    try {
      const data = {
        radiation: radiationState,
        device: deviceState,
        location: locationState,
        health: healthState
      }
      uni.setStorageSync(this.storageKey, JSON.stringify(data))
    } catch (error) {
      console.error('保存数据失败:', error)
    }
  }

  // 从本地存储加载
  loadFromStorage() {
    try {
      const data = uni.getStorageSync(this.storageKey)
      if (data) {
        const parsedData = JSON.parse(data)
        
        if (parsedData.radiation) {
          Object.assign(radiationState, parsedData.radiation)
        }
        if (parsedData.device) {
          Object.assign(deviceState, parsedData.device)
        }
        if (parsedData.location) {
          Object.assign(locationState, parsedData.location)
        }
        if (parsedData.health) {
          Object.assign(healthState, parsedData.health)
        }
      }
    } catch (error) {
      console.error('加载数据失败:', error)
    }
  }

  // 清除历史数据
  clearHistory() {
    radiationState.history = []
    radiationState.alerts = []
    locationState.history = []
    healthState.history = []
    this.saveToStorage()
  }

  // 导出数据
  exportData(type = 'all') {
    const data = {
      radiation: radiationState,
      device: deviceState,
      location: locationState,
      health: healthState,
      exportTime: new Date().toISOString()
    }

    return type === 'all' ? data : data[type]
  }
}

export default new DataStore() 