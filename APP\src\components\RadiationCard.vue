<template>
  <view class="radiation-card" :class="levelClass">
    <view class="card-header">
      <view class="status-indicator" :class="levelClass">
        <text class="indicator-icon">{{ statusIcon }}</text>
      </view>
      <text class="card-title">{{ title }}</text>
      <view class="card-actions" v-if="showActions">
        <text class="action-btn" @tap="$emit('export')">📤</text>
        <text class="action-btn" @tap="$emit('settings')">⚙️</text>
      </view>
    </view>
    
    <view class="card-content">
      <view class="main-value">
        <text class="value-number">{{ formatValue(value) }}</text>
        <text class="value-unit">{{ unit }}</text>
      </view>
      
      <view class="status-text">
        <text :class="levelClass">{{ statusText }}</text>
      </view>
      
      <view class="additional-info" v-if="additionalData">
        <view class="info-item" v-for="(item, key) in additionalData" :key="key">
          <text class="info-label">{{ item.label }}</text>
          <text class="info-value">{{ item.value }} {{ item.unit }}</text>
        </view>
      </view>
    </view>
    
    <view class="card-footer" v-if="showTrend">
      <view class="trend-indicator" :class="trendClass">
        <text class="trend-icon">{{ trendIcon }}</text>
        <text class="trend-text">{{ trendText }}</text>
      </view>
      <text class="last-update">{{ lastUpdateText }}</text>
    </view>
  </view>
</template>

<script>
import { computed } from 'vue'

export default {
  name: 'RadiationCard',
  props: {
    title: {
      type: String,
      default: '辐射监测'
    },
    value: {
      type: Number,
      required: true
    },
    unit: {
      type: String,
      default: 'μSv/h'
    },
    level: {
      type: String,
      default: 'safe' // safe, warning, danger
    },
    additionalData: {
      type: Object,
      default: null
    },
    showActions: {
      type: Boolean,
      default: false
    },
    showTrend: {
      type: Boolean,
      default: true
    },
    trend: {
      type: String,
      default: 'stable' // up, down, stable
    },
    trendValue: {
      type: Number,
      default: 0
    },
    lastUpdate: {
      type: Number,
      default: Date.now
    }
  },
  emits: ['export', 'settings'],
  setup(props) {
    const levelClass = computed(() => `level-${props.level}`)
    
    const statusIcon = computed(() => {
      switch (props.level) {
        case 'danger': return '🚨'
        case 'warning': return '⚠️'
        default: return '✅'
      }
    })
    
    const statusText = computed(() => {
      switch (props.level) {
        case 'danger': return '危险水平'
        case 'warning': return '需要注意'
        default: return '安全水平'
      }
    })
    
    const trendClass = computed(() => `trend-${props.trend}`)
    
    const trendIcon = computed(() => {
      switch (props.trend) {
        case 'up': return '📈'
        case 'down': return '📉'
        default: return '➖'
      }
    })
    
    const trendText = computed(() => {
      if (props.trend === 'stable') return '数值稳定'
      const sign = props.trend === 'up' ? '+' : ''
      return `${sign}${props.trendValue.toFixed(2)}%`
    })
    
    const lastUpdateText = computed(() => {
      const now = Date.now()
      const diff = now - props.lastUpdate
      
      if (diff < 60000) return '刚刚更新'
      if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`
      return `${Math.floor(diff / 3600000)}小时前`
    })
    
    const formatValue = (value) => {
      if (value < 0.001) return value.toExponential(2)
      if (value < 1) return value.toFixed(3)
      return value.toFixed(2)
    }
    
    return {
      levelClass,
      statusIcon,
      statusText,
      trendClass,
      trendIcon,
      trendText,
      lastUpdateText,
      formatValue
    }
  }
}
</script>

<style scoped>
.radiation-card {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20px);
  border-radius: 25rpx;
  padding: 30rpx;
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.radiation-card.level-safe {
  border-color: rgba(60, 197, 31, 0.3);
  box-shadow: 0 0 25rpx rgba(60, 197, 31, 0.1);
}

.radiation-card.level-warning {
  border-color: rgba(255, 193, 7, 0.3);
  box-shadow: 0 0 25rpx rgba(255, 193, 7, 0.1);
}

.radiation-card.level-danger {
  border-color: rgba(220, 53, 69, 0.3);
  box-shadow: 0 0 25rpx rgba(220, 53, 69, 0.1);
  animation: dangerPulse 2s infinite;
}

@keyframes dangerPulse {
  0%, 100% { box-shadow: 0 0 25rpx rgba(220, 53, 69, 0.1); }
  50% { box-shadow: 0 0 40rpx rgba(220, 53, 69, 0.3); }
}

.card-header {
  display: flex;
  align-items: center;
  gap: 15rpx;
  margin-bottom: 25rpx;
}

.status-indicator {
  width: 50rpx;
  height: 50rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: pulse 2s infinite;
}

.status-indicator.level-safe {
  background: rgba(60, 197, 31, 0.2);
}

.status-indicator.level-warning {
  background: rgba(255, 193, 7, 0.2);
}

.status-indicator.level-danger {
  background: rgba(220, 53, 69, 0.2);
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

.indicator-icon {
  font-size: 24rpx;
}

.card-title {
  flex: 1;
  font-size: 28rpx;
  color: #ffffff;
  font-weight: 600;
}

.card-actions {
  display: flex;
  gap: 10rpx;
}

.action-btn {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  font-size: 20rpx;
  transition: all 0.3s ease;
}

.action-btn:active {
  transform: scale(0.9);
  background: rgba(255, 255, 255, 0.2);
}

.card-content {
  text-align: center;
  margin-bottom: 25rpx;
}

.main-value {
  display: flex;
  align-items: baseline;
  justify-content: center;
  gap: 8rpx;
  margin-bottom: 15rpx;
}

.value-number {
  font-size: 72rpx;
  font-weight: 700;
  color: #ffffff;
  line-height: 1;
}

.value-unit {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.7);
  font-weight: 500;
}

.status-text {
  margin-bottom: 20rpx;
}

.status-text text {
  font-size: 24rpx;
  font-weight: 600;
  padding: 8rpx 20rpx;
  border-radius: 15rpx;
}

.status-text .level-safe {
  color: #3cc51f;
  background: rgba(60, 197, 31, 0.1);
}

.status-text .level-warning {
  color: #ffc107;
  background: rgba(255, 193, 7, 0.1);
}

.status-text .level-danger {
  color: #dc3545;
  background: rgba(220, 53, 69, 0.1);
}

.additional-info {
  display: flex;
  justify-content: space-around;
  gap: 15rpx;
}

.info-item {
  text-align: center;
  flex: 1;
}

.info-label {
  display: block;
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.6);
  margin-bottom: 5rpx;
}

.info-value {
  display: block;
  font-size: 24rpx;
  color: #ffffff;
  font-weight: 600;
}

.card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 20rpx;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.trend-indicator {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 8rpx 15rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
}

.trend-indicator.trend-up {
  background: rgba(220, 53, 69, 0.1);
  color: #dc3545;
}

.trend-indicator.trend-down {
  background: rgba(60, 197, 31, 0.1);
  color: #3cc51f;
}

.trend-indicator.trend-stable {
  background: rgba(255, 193, 7, 0.1);
  color: #ffc107;
}

.trend-icon {
  font-size: 18rpx;
}

.trend-text {
  font-weight: 500;
}

.last-update {
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.5);
}
</style> 