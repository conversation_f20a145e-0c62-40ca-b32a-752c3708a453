<template>
  <view class="toast-container-wrapper">
    <Toast
      v-for="toast in toastList"
      :key="toast.id"
      :message="toast.message"
      :type="toast.type"
      :duration="toast.duration"
      :showCountdown="toast.showCountdown"
      @close="handleClose(toast.id)"
      ref="toastRefs"
    />
  </view>
</template>

<script>
import { onMounted, ref, nextTick } from 'vue'
import Toast from './Toast.vue'
import { toastList, toastManager } from '../utils/toastManager.js'

export default {
  name: 'ToastContainer',
  components: {
    Toast
  },
  setup() {
    const toastRefs = ref([])
    
    // 监听新Toast的添加
    let lastToastCount = 0
    const checkNewToasts = () => {
      if (toastList.value.length > lastToastCount) {
        // 有新的Toast添加，显示最新的
        nextTick(() => {
          const newToastIndex = toastList.value.length - 1
          if (toastRefs.value[newToastIndex]) {
            toastRefs.value[newToastIndex].show()
          }
        })
      }
      lastToastCount = toastList.value.length
    }
    
    // 定时检查新Toast
    onMounted(() => {
      setInterval(checkNewToasts, 100)
    })
    
    const handleClose = (id) => {
      toastManager.remove(id)
    }
    
    return {
      toastList,
      toastRefs,
      handleClose
    }
  }
}
</script>

<style scoped>
.toast-container-wrapper {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 9999;
  pointer-events: none;
}

.toast-container-wrapper > * {
  pointer-events: auto;
}
</style>
