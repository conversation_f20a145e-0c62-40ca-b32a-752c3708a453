/**
 * 用户相关接口
 */
import request from '../utils/request';

/**
 * 用户注册
 * @param {Object} data 注册信息
 * @returns {Promise} Promise对象
 */
export function register(data) {
  return request.post('/api/user/register', data);
}

/**
 * 用户登录
 * @param {Object} data 登录信息
 * @returns {Promise} Promise对象
 */
export function login(data) {
  return request.post('/api/user/login', data);
}

/**
 * 发送验证码
 * @param {Object} data 手机号
 * @returns {Promise} Promise对象
 */
export function sendVerificationCode(data) {
  return request.post('/api/user/sendVerificationCode', data);
}

/**
 * 获取用户信息
 * @returns {Promise} Promise对象
 */
export function getUserInfo() {
  return request.get('/api/user/info');
}

/**
 * 修改用户信息
 * @param {Object} data 用户信息
 * @returns {Promise} Promise对象
 */
export function updateUserInfo(data) {
  return request.put('/api/user/updateInfo', data);
}

/**
 * 修改密码
 * @param {Object} data 密码信息
 * @returns {Promise} Promise对象
 */
export function changePassword(data) {
  return request.put('/api/user/changePassword', data);
}

/**
 * 重置密码
 * @param {Object} data 重置密码信息
 * @returns {Promise} Promise对象
 */
export function resetPassword(data) {
  return request.post('/api/user/resetPassword', data);
} 