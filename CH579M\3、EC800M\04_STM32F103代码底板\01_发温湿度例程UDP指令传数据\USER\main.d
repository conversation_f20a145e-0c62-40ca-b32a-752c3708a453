.\main.o: main.c
.\main.o: ..\HARDWARE\LED\led.h
.\main.o: ..\SYSTEM\sys\sys.h
.\main.o: ..\USER\stm32f10x.h
.\main.o: ..\CORE\core_cm3.h
.\main.o: D:\Keil_v534\ARM\ARMCC\Bin\..\include\stdint.h
.\main.o: ..\USER\system_stm32f10x.h
.\main.o: ..\USER\stm32f10x_conf.h
.\main.o: ..\STM32F10x_FWLib\inc\stm32f10x_can.h
.\main.o: ..\USER\stm32f10x.h
.\main.o: ..\STM32F10x_FWLib\inc\stm32f10x_fsmc.h
.\main.o: ..\STM32F10x_FWLib\inc\stm32f10x_gpio.h
.\main.o: ..\STM32F10x_FWLib\inc\stm32f10x_rcc.h
.\main.o: ..\STM32F10x_FWLib\inc\stm32f10x_usart.h
.\main.o: ..\STM32F10x_FWLib\inc\misc.h
.\main.o: ..\SYSTEM\delay\delay.h
.\main.o: ..\SYSTEM\usart\usart.h
.\main.o: D:\Keil_v534\ARM\ARMCC\Bin\..\include\stdio.h
.\main.o: D:\Keil_v534\ARM\ARMCC\Bin\..\include\math.h
.\main.o: ..\STM32F10x_FWLib\inc\stm32f10x_flash.h
.\main.o: D:\Keil_v534\ARM\ARMCC\Bin\..\include\stdlib.h
.\main.o: D:\Keil_v534\ARM\ARMCC\Bin\..\include\string.h
.\main.o: ..\HARDWARE\WDG\wdg.h
.\main.o: ..\HARDWARE\EC800\EC800.h
.\main.o: ..\HARDWARE\AHT20\aht20.h
.\main.o: ..\STM32F10x_FWLib\inc\stm32f10x_tim.h
.\main.o: ..\HARDWARE\AHT20\bsp_i2c.h
