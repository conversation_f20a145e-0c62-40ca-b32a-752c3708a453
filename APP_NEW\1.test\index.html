<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>智能家居系统登录</title>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Poppins', -apple-system, BlinkMacSystemFont, "SF Pro Text", "Helvetica Neue", Arial, sans-serif;
        }
        
        body {
            background: linear-gradient(120deg, #e3f0ff 0%, #f7f7f7 100%);
            display: flex;
            flex-direction: column;
            min-height: 100vh;
            height: 100vh;
            overflow: hidden;
        }
        
        .container {
            display: flex;
            width: 100vw;
            height: 100vh;
            margin: 0;
            padding: 0;
            flex-wrap: wrap;
            justify-content: center;
            align-items: center;
            background: none;
        }
        
        /* 原型部分 */
        .prototype {
            width: 100%;
            max-width: 375px;
            height: auto;
            aspect-ratio: 375/812;
            background: linear-gradient(145deg, #f0f0f0, #fafafa);
            border-radius: 50px;
            overflow: hidden;
            box-shadow: 0 20px 60px -10px rgba(30, 136, 229, 0.18);
            position: relative;
            max-height: 92vh;
            /* 已无黑色边框 */
        }
        
        .login-container {
            padding: 10% 5%;
            display: flex;
            flex-direction: column;
            height: 100%;
            position: relative;
            overflow: hidden;
            box-shadow: inset 0 0 24px rgba(30, 136, 229, 0.06);
            background: rgba(255,255,255,0.98);
            /* 轻微磨砂美化 */
            backdrop-filter: blur(2px);
        }
        
        .login-container::before {
            content: "";
            position: absolute;
            top: -150px;
            right: -100px;
            width: 300px;
            height: 300px;
            border-radius: 50%;
            background: linear-gradient(135deg, rgba(30, 136, 229, 0.1), rgba(30, 136, 229, 0.05));
            z-index: 0;
        }
        
        .login-container::after {
            content: "";
            position: absolute;
            bottom: -150px;
            left: -100px;
            width: 300px;
            height: 300px;
            border-radius: 50%;
            background: linear-gradient(135deg, rgba(30, 136, 229, 0.05), rgba(30, 136, 229, 0.02));
            z-index: 0;
        }
        
        .content-wrapper {
            position: relative;
            z-index: 1;
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
            padding: 0 10px;
        }
        
        .logo-container {
            margin-bottom: 30px;
            text-align: center;
        }
        
        .logo {
            font-size: 32px;
            font-weight: 700;
            color: #222;
            position: relative;
            display: inline-block;
        }
        
        .logo::after {
            content: "";
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(90deg, #1e88e5, #64b5f6);
            border-radius: 2px;
        }
        
        h1 {
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 4px;
            color: #222;
            letter-spacing: -0.5px;
        }
        
        .subtitle {
            font-size: 16px;
            color: #999;
            margin-bottom: 40px;
        }
        
        .input-container {
            position: relative;
            width: 100%;
            margin-bottom: 32px;
        }
        
        .input-field {
            width: 100%;
            padding: 20px 20px 20px 60px;
            font-size: 17px;
            border: none;
            border-radius: 18px;
            background-color: #fff;
            color: #333;
            transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
            box-shadow: 0 6px 16px rgba(0, 0, 0, 0.08);
            height: 64px;
        }
        
        .input-field:focus {
            outline: none;
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.12), 0 0 0 2px rgba(30, 136, 229, 0.2);
            transform: translateY(-3px);
        }
        
        .input-icon {
            position: absolute;
            left: 20px;
            top: 50%;
            transform: translateY(-50%);
            color: #777;
            z-index: 2; /* 提高图标层级 */
            pointer-events: none; /* 确保图标不会拦截点击事件 */
            display: flex;
            align-items: center;
            justify-content: center;
            background: transparent;
        }
        
        .input-icon svg {
            width: 24px;
            height: 24px;
        }
        
        .input-with-icon {
            text-indent: 0; /* 确保文本不会被图标覆盖 */
        }
        
        .password-field {
            letter-spacing: 6px;
            font-size: 18px;
        }
        
        .toggle-password {
            position: absolute;
            right: 16px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: #333;
            cursor: pointer;
        }
        
        .toggle-password svg {
            width: 24px;
            height: 24px;
            transition: all 0.3s ease;
        }
        
        .toggle-password:hover svg {
            color: #1e88e5;
        }
        
        .forgot-password {
            text-align: right;
            margin: 8px 0 32px;
        }
        
        .forgot-password a {
            color: #999;
            text-decoration: none;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        
        .forgot-password a:hover {
            color: #1e88e5;
        }
        
        .login-button {
            background: linear-gradient(135deg, #222, #444);
            color: white;
            border: none;
            border-radius: 16px;
            padding: 18px;
            font-size: 16px;
            font-weight: 600;
            width: 100%;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            position: relative;
            overflow: hidden;
        }
        
        .login-button::after {
            content: "";
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: all 0.6s ease;
        }
        
        .login-button:hover::after {
            left: 100%;
        }
        
        .login-button:active {
            transform: scale(0.98);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .signup-prompt {
            text-align: center;
            font-size: 14px;
            color: #999;
            margin: 24px 0;
        }
        
        .signup-prompt a {
            color: #1e88e5;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .signup-prompt a:hover {
            text-decoration: underline;
        }
        
        .divider {
            display: flex;
            align-items: center;
            margin: 24px 0;
            color: #999;
            font-size: 14px;
        }
        
        .divider::before,
        .divider::after {
            content: "";
            flex: 1;
            height: 1px;
            background-color: #e0e0e0;
        }
        
        .divider span {
            padding: 0 16px;
        }
        
        .alt-login {
            display: flex;
            justify-content: space-between;
            gap: 16px;
        }
        
        .alt-login-button {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 14px;
            background-color: #fff;
            border-radius: 16px;
            border: none;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        
        .alt-login-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
        }
        
        .alt-login-button:active {
            transform: scale(0.98);
        }
        
        .alt-login-button svg,
        .alt-login-button img {
            height: 24px;
            width: 24px;
            margin-right: 8px;
            transition: all 0.3s ease;
        }
        
        .alt-login-button:hover svg {
            transform: scale(1.1);
        }
        
        .alt-login-button span {
            font-size: 14px;
            font-weight: 600;
            color: #333;
        }
        
        .dot-container {
            display: flex;
            justify-content: center;
            margin: 16px 0;
            gap: 8px;
        }
        
        .dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background-color: #000;
        }
        
        @keyframes pulse {
            0% {
                transform: scale(1);
                opacity: 1;
            }
            50% {
                transform: scale(1.05);
                opacity: 0.8;
            }
            100% {
                transform: scale(1);
                opacity: 1;
            }
        }
        
        /* 删除设计说明相关样式 */
        /* 删除 .design-notes、.design-note 及相关样式 */
        
        .color-palette {
            display: flex;
            gap: 16px;
            margin-top: 12px;
        }
        
        .color-box {
            width: 60px;
            height: 60px;
            border-radius: 8px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            font-size: 12px;
            color: white;
            text-shadow: 0 1px 1px rgba(0, 0, 0, 0.2);
        }
        
        .primary {
            background: linear-gradient(135deg, #222, #444);
        }
        
        .secondary {
            background: linear-gradient(145deg, #f0f0f0, #fafafa);
            color: #000;
            text-shadow: none;
        }
        
        .accent {
            background: linear-gradient(90deg, #1e88e5, #64b5f6);
        }
        
        .text-dark {
            background-color: #222222;
        }
        
        .text-light {
            background-color: #999999;
        }
        
        .background {
            background-color: #fff;
            color: #000;
            text-shadow: none;
            border: 1px solid #eee;
        }

        @media (max-width: 1000px) {
            .container {
                flex-direction: column;
                align-items: center;
                padding: 0;
            }
            .prototype {
                max-width: 100vw;
                max-height: 90vh;
                border-radius: 30px;
            }
            .login-container {
                padding: 10% 5%;
            }
        }
        @media (max-width: 480px) {
            .prototype {
                border-radius: 24px;
                max-height: 100vh;
                width: 100%;
                box-shadow: none;
            }
            .login-container {
                padding: 10% 8%;
                display: flex;
                flex-direction: column;
                justify-content: center;
                min-height: 100vh;
            }
            .content-wrapper {
                padding: 0;
            }
            h1 {
                font-size: 24px;
                margin-bottom: 4px;
            }
            .subtitle {
                font-size: 15px;
                margin-bottom: 36px;
            }
            .input-container {
                margin-bottom: 28px;
            }
            .input-field {
                padding: 18px 18px 18px 54px;
                font-size: 16px;
                border-radius: 16px;
                height: 60px;
            }
            .input-icon {
                left: 18px;
            }
            .input-icon svg {
                width: 22px;
                height: 22px;
            }
            .toggle-password {
                right: 16px;
            }
            .toggle-password svg {
                width: 22px;
                height: 22px;
            }
            .login-button {
                padding: 18px;
                border-radius: 16px;
                margin-bottom: 20px;
            }
            .alt-login-button {
                padding: 14px;
                border-radius: 14px;
            }
            .alt-login-button span {
                font-size: 13px;
            }
            .alt-login {
                gap: 12px;
            }
            .forgot-password {
                margin-bottom: 36px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 原型部分 -->
        <div class="prototype">
            <div class="login-container">
                <div class="content-wrapper">
                    <div class="logo-container">
                        <div class="logo">SmartHome</div>
                    </div>
                    
                    <h1>欢迎</h1>
                    <p class="subtitle">登录智能家居系统</p>
                    
                    <div class="input-container">
                        <div class="input-icon">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"></path>
                            </svg>
                        </div>
                        <input type="tel" id="phone" class="input-field input-with-icon" placeholder="请输入手机号码">
                    </div>
                    
                    <div class="input-container">
                        <div class="input-icon">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <rect x="3" y="11" width="18" height="11" rx="2" ry="2"></rect>
                                <path d="M7 11V7a5 5 0 0 1 10 0v4"></path>
                            </svg>
                        </div>
                        <input type="password" id="password" class="input-field input-with-icon password-field" placeholder="• • • • • •">
                        <button class="toggle-password" id="togglePassword">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                                <circle cx="12" cy="12" r="3"></circle>
                            </svg>
                        </button>
                    </div>
                    
                    <div class="forgot-password">
                        <a href="#" id="forgotPasswordLink">忘记密码？</a>
                    </div>
                    
                    <button class="login-button" id="loginButton">登 录</button>
                    
                    <div class="signup-prompt">
                        没有账号？<a href="register.html" id="signupLink">立即注册</a>
                    </div>
                    
                    <div class="divider">
                        <span>其他登录方式</span>
                    </div>
                    
                    <div class="alt-login">
                        <button class="alt-login-button" id="smsLogin">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="#1e88e5" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
                                <line x1="9" y1="10" x2="15" y2="10"></line>
                                <line x1="12" y1="7" x2="12" y2="13"></line>
                            </svg>
                            <span>验证码登录</span>
                        </button>
                        <button class="alt-login-button" id="emailLogin">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="#1e88e5" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path>
                                <polyline points="22,6 12,13 2,6"></polyline>
                            </svg>
                            <span>邮箱登录</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
    
    <script>
        function togglePassword() {
            const passwordInput = document.getElementById('password');
            const toggleButton = document.getElementById('togglePassword');
            
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                passwordInput.classList.remove('password-field');
                toggleButton.innerHTML = `
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24"></path>
                        <line x1="1" y1="1" x2="23" y2="23"></line>
                    </svg>
                `;
            } else {
                passwordInput.type = 'password';
                passwordInput.classList.add('password-field');
                toggleButton.innerHTML = `
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                        <circle cx="12" cy="12" r="3"></circle>
                    </svg>
                `;
            }
        }
        
        // 添加交互效果
        document.getElementById('loginButton').addEventListener('click', function() {
            const phone = document.getElementById('phone').value;
            const password = document.getElementById('password').value;
            
            if (phone && password) {
                this.classList.add('animate-login');
                setTimeout(() => {
                    window.location.href = 'home.html';
                }, 500);
            } else {
                alert('请输入手机号码和密码');
            }
        });
        
        document.getElementById('forgotPasswordLink').addEventListener('click', function(e) {
            e.preventDefault();
            alert('忘记密码功能将在后续版本中推出');
        });
        
        document.getElementById('signupLink').addEventListener('click', function(e) {
            e.preventDefault();
            window.location.href = 'register.html';
        });
        
        document.getElementById('smsLogin').addEventListener('click', function() {
            alert('验证码登录功能将在后续版本中推出');
        });
        
        document.getElementById('emailLogin').addEventListener('click', function() {
            alert('邮箱登录功能将在后续版本中推出');
        });
        
        document.getElementById('togglePassword').addEventListener('click', togglePassword);
        
        // 输入框焦点效果
        const inputs = document.querySelectorAll('.input-field');
        inputs.forEach(input => {
            input.addEventListener('focus', function() {
                this.style.boxShadow = '0 8px 20px rgba(0, 0, 0, 0.12), 0 0 0 2px rgba(30, 136, 229, 0.2)';
                this.style.transform = 'translateY(-3px)';
                // 确保图标也跟随输入框移动
                const iconContainer = this.previousElementSibling;
                if (iconContainer && iconContainer.classList.contains('input-icon')) {
                    iconContainer.style.transform = 'translateY(calc(-50% - 3px))';
                }
            });
            
            input.addEventListener('blur', function() {
                this.style.boxShadow = '0 6px 16px rgba(0, 0, 0, 0.08)';
                this.style.transform = 'translateY(0)';
                // 恢复图标位置
                const iconContainer = this.previousElementSibling;
                if (iconContainer && iconContainer.classList.contains('input-icon')) {
                    iconContainer.style.transform = 'translateY(-50%)';
                }
            });
        });
        
        // 添加品牌标识动画效果
        const logo = document.querySelector('.logo');
        setInterval(() => {
            logo.style.animation = 'pulse 1.5s ease-in-out';
            setTimeout(() => {
                logo.style.animation = 'none';
            }, 1500);
        }, 5000);
    </script>
  </body>
</html>
