<template>
  <view v-if="visible" class="modal-overlay" @tap="closeModal">
    <view class="modal-container" @tap.stop :class="animationClass">
      <view class="modal-header">
        <view class="modal-icon">
          <text class="icon-text">{{ iconText }}</text>
        </view>
        <text class="modal-title">{{ title }}</text>
        <view class="modal-close" @tap="closeModal">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <line x1="18" y1="6" x2="6" y2="18"></line>
            <line x1="6" y1="6" x2="18" y2="18"></line>
          </svg>
        </view>
      </view>
      
      <view class="modal-content">
        <view class="info-items">
          <view v-for="(item, index) in infoItems" :key="index" class="info-item">
            <view class="info-icon">
              <text class="info-emoji">{{ item.icon }}</text>
            </view>
            <view class="info-details">
              <text class="info-label">{{ item.label }}</text>
              <text class="info-value">{{ item.value }}</text>
            </view>
          </view>
        </view>
        
        <view class="description-section" v-if="description">
          <text class="description-text">{{ description }}</text>
        </view>
      </view>
      
      <view class="modal-footer">
        <view class="modal-button confirm" @tap="closeModal">
          <text class="button-text">{{ confirmText }}</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { ref, watch } from 'vue'

export default {
  name: 'InfoModal',
  props: {
    modelValue: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: '信息'
    },
    iconText: {
      type: String,
      default: 'ℹ️'
    },
    infoItems: {
      type: Array,
      default: () => []
    },
    description: {
      type: String,
      default: ''
    },
    confirmText: {
      type: String,
      default: '确定'
    }
  },
  emits: ['update:modelValue', 'confirm'],
  setup(props, { emit }) {
    const visible = ref(false)
    const animationClass = ref('')
    
    watch(() => props.modelValue, (newVal) => {
      if (newVal) {
        show()
      } else {
        hide()
      }
    }, { immediate: true })
    
    const show = () => {
      visible.value = true
      animationClass.value = 'modal-enter'
      // 触发震动反馈
      uni.vibrateShort()
    }
    
    const hide = () => {
      animationClass.value = 'modal-leave'
      setTimeout(() => {
        visible.value = false
        animationClass.value = ''
      }, 300)
    }
    
    const closeModal = () => {
      emit('update:modelValue', false)
      emit('confirm')
    }
    
    return {
      visible,
      animationClass,
      closeModal
    }
  }
}
</script>

<style scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  z-index: 10001;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  animation: fadeIn 0.3s ease;
  box-sizing: border-box;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.modal-container {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(248, 250, 252, 0.95) 100%);
  border-radius: 24px;
  max-width: 400px;
  width: 100%;
  max-height: 80vh;
  overflow: hidden;
  box-shadow:
    0 25px 80px rgba(0, 0, 0, 0.2),
    0 12px 40px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.4);
  position: relative;
  margin: auto;
}

.modal-enter {
  animation: slideInScale 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.modal-leave {
  animation: slideOutScale 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes slideInScale {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes slideOutScale {
  from {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
  to {
    opacity: 0;
    transform: translateY(30px) scale(0.9);
  }
}

.modal-header {
  display: flex;
  align-items: center;
  padding: 28px 24px 20px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.7) 100%);
  border-bottom: 1px solid rgba(226, 232, 240, 0.3);
}

.modal-icon {
  width: 48px;
  height: 48px;
  border-radius: 16px;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  box-shadow: 0 4px 16px rgba(59, 130, 246, 0.3);
}

.icon-text {
  font-size: 24px;
  color: white;
}

.modal-title {
  flex: 1;
  font-size: 20px;
  font-weight: 700;
  color: #0f172a;
  line-height: 1.3;
}

.modal-close {
  width: 32px;
  height: 32px;
  border-radius: 16px;
  background: rgba(248, 250, 252, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  border: 1px solid rgba(226, 232, 240, 0.6);
}

.modal-close:active {
  background: rgba(226, 232, 240, 0.8);
  transform: scale(0.95);
}

.modal-close svg {
  width: 16px;
  height: 16px;
  color: #64748b;
}

.modal-content {
  padding: 24px;
  max-height: 60vh;
  overflow-y: auto;
}

.info-items {
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin-bottom: 24px;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  background: rgba(248, 250, 252, 0.6);
  border-radius: 16px;
  border: 1px solid rgba(226, 232, 240, 0.6);
  transition: all 0.3s ease;
}

.info-item:hover {
  background: rgba(248, 250, 252, 0.8);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.info-icon {
  width: 40px;
  height: 40px;
  border-radius: 12px;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(59, 130, 246, 0.2) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.info-emoji {
  font-size: 20px;
}

.info-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.info-label {
  font-size: 14px;
  color: #64748b;
  font-weight: 500;
}

.info-value {
  font-size: 16px;
  color: #0f172a;
  font-weight: 600;
}

.description-section {
  padding: 20px;
  background: rgba(59, 130, 246, 0.05);
  border-radius: 16px;
  border: 1px solid rgba(59, 130, 246, 0.1);
}

.description-text {
  font-size: 15px;
  color: #374151;
  line-height: 1.6;
  text-align: left;
}

.modal-footer {
  padding: 16px 24px 28px;
  background: linear-gradient(135deg, rgba(248, 250, 252, 0.8) 0%, rgba(255, 255, 255, 0.6) 100%);
}

.modal-button {
  width: 100%;
  padding: 16px 20px;
  border-radius: 14px;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  box-shadow: 0 4px 16px rgba(59, 130, 246, 0.3);
  position: relative;
  overflow: hidden;
}

.modal-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s;
}

.modal-button:active::before {
  left: 100%;
}

.modal-button:active {
  transform: scale(0.98);
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.4);
}

.button-text {
  font-size: 16px;
  font-weight: 600;
  color: white;
  position: relative;
  z-index: 1;
}
</style>
