<template>
  <view class="app-container">
    <!-- 全局加载状态 -->
    <LoadingSpinner 
      :visible="isLoading"
      :text="loadingText"
      :type="loadingType"
      :show-progress="showLoadingProgress"
      :progress="loadingProgress"
      @cancel="cancelLoading" />
    
    <!-- 全局Toast通知 -->
    <view class="toast-container" v-if="toastVisible">
      <view class="toast-content" :class="toastType">
        <text class="toast-icon">{{ toastIcon }}</text>
        <text class="toast-message">{{ toastMessage }}</text>
      </view>
    </view>
    
    <!-- 页面内容 -->
    <view class="page-content">
      <!-- 这里是页面组件的插槽 -->
    </view>
    
    <!-- 全局网络状态提示 -->
    <view class="network-status" v-if="!isOnline">
      <text class="status-icon">📶</text>
      <text class="status-text">网络连接已断开</text>
    </view>
    
    <!-- 全局更新提示 -->
    <view class="update-banner" v-if="hasUpdate">
      <view class="update-content">
        <text class="update-title">发现新版本</text>
        <text class="update-desc">{{ updateInfo.description }}</text>
      </view>
      <view class="update-actions">
        <text class="update-btn later" @tap="laterUpdate">稍后</text>
        <text class="update-btn now" @tap="updateNow">立即更新</text>
      </view>
    </view>
  </view>
</template>

<script>
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import LoadingSpinner from './components/LoadingSpinner.vue'
import { radiationState, deviceState } from './utils/dataStore.js'
import mqttService from './utils/mqttService.js'

export default {
  name: 'App',
  components: {
    LoadingSpinner
  },
  setup() {
    // 全局状态
    const isLoading = ref(false)
    const loadingText = ref('')
    const loadingType = ref('ring')
    const showLoadingProgress = ref(false)
    const loadingProgress = ref(0)
    
    const toastVisible = ref(false)
    const toastMessage = ref('')
    const toastType = ref('info')
    const toastIcon = ref('ℹ️')
    
    const isOnline = ref(true)
    const hasUpdate = ref(false)
    const updateInfo = reactive({
      version: '',
      description: '',
      url: ''
    })

    let toastTimer = null
    let networkChecker = null
    
    // 全局加载控制
    const showLoading = (text = '加载中...', type = 'ring', showProgress = false) => {
      isLoading.value = true
      loadingText.value = text
      loadingType.value = type
      showLoadingProgress.value = showProgress
      loadingProgress.value = 0
    }
    
    const hideLoading = () => {
      isLoading.value = false
      loadingText.value = ''
      showLoadingProgress.value = false
      loadingProgress.value = 0
    }
    
    const updateLoadingProgress = (progress) => {
      loadingProgress.value = Math.max(0, Math.min(100, progress))
    }
    
    const cancelLoading = () => {
      hideLoading()
    }
    
    // Toast通知控制
    const showToast = (message, type = 'info', duration = 3000) => {
      toastMessage.value = message
      toastType.value = type
      
      // 设置图标
      switch (type) {
        case 'success':
          toastIcon.value = '✅'
          break
        case 'error':
          toastIcon.value = '❌'
          break
        case 'warning':
          toastIcon.value = '⚠️'
          break
        case 'radiation':
          toastIcon.value = '☢️'
          break
        default:
          toastIcon.value = 'ℹ️'
      }
      
      toastVisible.value = true
      
      if (toastTimer) {
        clearTimeout(toastTimer)
      }
      
      toastTimer = setTimeout(() => {
        toastVisible.value = false
      }, duration)
    }
    
    // 网络状态检查
    const checkNetworkStatus = () => {
      uni.getNetworkType({
        success: (res) => {
          isOnline.value = res.networkType !== 'none'
        },
        fail: () => {
          isOnline.value = false
        }
      })
    }
    
    // 更新检查
    const checkForUpdates = () => {
      setTimeout(() => {
        const shouldUpdate = Math.random() > 0.8
        if (shouldUpdate) {
          hasUpdate.value = true
          updateInfo.version = 'v1.0.1'
          updateInfo.description = '修复了数据同步问题，优化了界面性能'
          updateInfo.url = 'https://example.com/update'
        }
      }, 5000)
    }
    
    const laterUpdate = () => {
      hasUpdate.value = false
    }
    
    const updateNow = () => {
      showLoading('正在更新...', 'ring', true)
      
      let progress = 0
      const updateTimer = setInterval(() => {
        progress += Math.random() * 20
        updateLoadingProgress(progress)
        
        if (progress >= 100) {
          clearInterval(updateTimer)
          hideLoading()
          hasUpdate.value = false
          showToast('更新完成', 'success')
        }
      }, 500)
    }
    
    // 初始化应用
    const initializeApp = async () => {
      showLoading('正在初始化...', 'dots')
      
      try {
        await new Promise(resolve => setTimeout(resolve, 500))
        updateLoadingProgress(20)
        
        showLoading('连接服务器...', 'ring')
        mqttService.connect()
        await new Promise(resolve => setTimeout(resolve, 1000))
        updateLoadingProgress(50)
        
        showLoading('加载配置...', 'dots')
        await new Promise(resolve => setTimeout(resolve, 500))
        updateLoadingProgress(70)
        
        showLoading('初始化传感器...', 'pulse')
        await new Promise(resolve => setTimeout(resolve, 800))
        updateLoadingProgress(90)
        
        await new Promise(resolve => setTimeout(resolve, 300))
        updateLoadingProgress(100)
        
        hideLoading()
        showToast('系统初始化完成', 'success')
        
      } catch (error) {
        hideLoading()
        showToast('初始化失败: ' + error.message, 'error')
      }
    }
    
    // 注册全局事件监听
    const registerGlobalEvents = () => {
      mqttService.onMessage('connected', () => {
        deviceState.connection.mqtt = true
        showToast('MQTT连接成功', 'success')
      })
      
      mqttService.onMessage('disconnected', () => {
        deviceState.connection.mqtt = false
        showToast('MQTT连接断开', 'warning')
      })
      
      mqttService.onMessage('radiationAlert', (data) => {
        const { level, message } = data
        showToast(message, 'radiation', 5000)
        
        if (radiationState.settings.vibrationAlert) {
          uni.vibrateShort()
        }
      })
      
      uni.onNetworkStatusChange((res) => {
        isOnline.value = res.isConnected
        if (res.isConnected) {
          showToast('网络已连接', 'success')
        } else {
          showToast('网络连接断开', 'error')
        }
      })
    }
    
    // 生命周期
    onMounted(() => {
      initializeApp()
      registerGlobalEvents()
      checkNetworkStatus()
      networkChecker = setInterval(checkNetworkStatus, 30000)
      checkForUpdates()
    })
    
    onUnmounted(() => {
      if (toastTimer) {
        clearTimeout(toastTimer)
      }
      if (networkChecker) {
        clearInterval(networkChecker)
      }
      mqttService.disconnect()
    })
    
    return {
      isLoading,
      loadingText,
      loadingType,
      showLoadingProgress,
      loadingProgress,
      toastVisible,
      toastMessage,
      toastType,
      toastIcon,
      isOnline,
      hasUpdate,
      updateInfo,
      cancelLoading,
      laterUpdate,
      updateNow
    }
  }
}
</script>

<style>
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

.app-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #0f0f1c 0%, #1a1a2e 100%);
  color: #ffffff;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
}

.page-content {
  min-height: 100vh;
}

.toast-container {
  position: fixed;
  top: 100rpx;
  left: 50%;
  transform: translateX(-50%);
  z-index: 10000;
  animation: toastSlideIn 0.3s ease;
}

@keyframes toastSlideIn {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(-20rpx);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

.toast-content {
  display: flex;
  align-items: center;
  gap: 15rpx;
  padding: 20rpx 30rpx;
  background: rgba(26, 26, 46, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 25rpx;
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.3);
  max-width: 600rpx;
}

.toast-content.success {
  border-color: rgba(60, 197, 31, 0.3);
  background: rgba(60, 197, 31, 0.1);
}

.toast-content.error {
  border-color: rgba(220, 53, 69, 0.3);
  background: rgba(220, 53, 69, 0.1);
}

.toast-content.warning {
  border-color: rgba(255, 193, 7, 0.3);
  background: rgba(255, 193, 7, 0.1);
}

.toast-content.radiation {
  border-color: rgba(255, 87, 34, 0.3);
  background: rgba(255, 87, 34, 0.1);
  animation: radiationPulse 1s infinite;
}

@keyframes radiationPulse {
  0%, 100% { box-shadow: 0 10rpx 30rpx rgba(255, 87, 34, 0.2); }
  50% { box-shadow: 0 10rpx 40rpx rgba(255, 87, 34, 0.4); }
}

.toast-icon {
  font-size: 28rpx;
}

.toast-message {
  font-size: 26rpx;
  color: #ffffff;
  font-weight: 500;
  line-height: 1.3;
}

.network-status {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: rgba(220, 53, 69, 0.9);
  padding: 15rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10rpx;
  z-index: 9998;
  animation: slideDown 0.3s ease;
}

@keyframes slideDown {
  from { transform: translateY(-100%); }
  to { transform: translateY(0); }
}

.status-icon {
  font-size: 24rpx;
}

.status-text {
  font-size: 24rpx;
  color: #ffffff;
  font-weight: 500;
}

.update-banner {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(33, 150, 243, 0.95);
  backdrop-filter: blur(20px);
  padding: 25rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  z-index: 9997;
  animation: slideUp 0.3s ease;
}

@keyframes slideUp {
  from { transform: translateY(100%); }
  to { transform: translateY(0); }
}

.update-content {
  flex: 1;
  margin-right: 20rpx;
}

.update-title {
  display: block;
  font-size: 26rpx;
  color: #ffffff;
  font-weight: 600;
  margin-bottom: 5rpx;
}

.update-desc {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.3;
}

.update-actions {
  display: flex;
  gap: 15rpx;
}

.update-btn {
  padding: 15rpx 25rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 500;
  text-align: center;
  transition: all 0.3s ease;
}

.update-btn.later {
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.update-btn.now {
  background: rgba(255, 255, 255, 0.9);
  color: #2196f3;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.update-btn:active {
  transform: scale(0.95);
}

::-webkit-scrollbar {
  width: 8rpx;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 4rpx;
}

::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 4rpx;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}

input, textarea {
  background: rgba(255, 255, 255, 0.1) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  border-radius: 12rpx !important;
  color: #ffffff !important;
  font-size: 26rpx !important;
}

input:focus, textarea:focus {
  border-color: rgba(60, 197, 31, 0.5) !important;
  background: rgba(60, 197, 31, 0.05) !important;
}

.btn {
  padding: 20rpx 40rpx;
  border-radius: 20rpx;
  font-size: 26rpx;
  font-weight: 500;
  text-align: center;
  transition: all 0.3s ease;
  border: none;
}

.btn:active {
  transform: scale(0.95);
}

.btn-primary {
  background: linear-gradient(45deg, #3cc51f, #4caf50);
  color: #ffffff;
}

.btn-secondary {
  background: rgba(255, 255, 255, 0.1);
  color: #ffffff;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.btn-danger {
  background: linear-gradient(45deg, #dc3545, #e53e3e);
  color: #ffffff;
}

.card {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20px);
  border-radius: 20rpx;
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.card:hover {
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(255, 255, 255, 0.2);
}

@media (max-width: 750rpx) {
  .toast-content {
    max-width: 90%;
    padding: 15rpx 20rpx;
  }
  
  .update-banner {
    flex-direction: column;
    gap: 15rpx;
    text-align: center;
  }
  
  .update-actions {
    width: 100%;
    justify-content: center;
  }
}
</style>