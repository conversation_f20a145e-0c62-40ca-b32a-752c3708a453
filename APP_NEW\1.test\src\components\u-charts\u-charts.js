/*
 * uCharts® 
 * 高性能跨平台图表库，支持H5、APP、小程序
 * Copyright (c) 2021 QIUN® 秋云
 * https://www.ucharts.cn
 * 简化版本
 */

'use strict';

var config = {
  yAxisWidth: 15,
  yAxisSplit: 5,
  xAxisHeight: 22,
  xAxisLineHeight: 15,
  legendHeight: 15,
  yAxisTitleWidth: 15,
  padding: [10, 10, 10, 10],
  pixelRatio: 1,
  rotate: false,
  columePadding: 3,
  fontSize: 13,
  fontColor: '#666666',
  dataPointShape: ['circle', 'circle', 'circle', 'circle'],
  color: ['#1890FF', '#91CB74', '#FAC858', '#EE6666', '#73C0DE', '#3CA272', '#FC8452', '#9A60B4', '#ea7ccc'],
  linearColor: ['#0983fe', '#52cfbb', '#f8ba3b', '#e05d5d', '#53b9e5', '#39a367', '#f77a3b', '#814c9f', '#d4429d'],
  pieChartLinePadding: 15,
  pieChartTextPadding: 5,
  xAxisTextPadding: 3,
  titleColor: '#333333',
  titleFontSize: 20,
  subtitleColor: '#999999',
  subtitleFontSize: 15,
  toolTipPadding: 3,
  toolTipBackground: '#000000',
  toolTipOpacity: 0.7,
  toolTipLineHeight: 20,
  radarGridCount: 3,
  radarLabelTextMargin: 15,
  gaugeLabelTextMargin: 15
};

var util = {
  toFixed: function toFixed(num, limit) {
    limit = limit || 2;
    if (this.isFloat(num)) {
      num = num.toFixed(limit);
    }
    return num;
  },
  isFloat: function isFloat(num) {
    return num % 1 !== 0;
  },
  approximatelyEqual: function approximatelyEqual(num1, num2) {
    return Math.abs(num1 - num2) < 1e-10;
  },
  isSameSign: function isSameSign(num1, num2) {
    return Math.abs(num1) === num1 && Math.abs(num2) === num2 || Math.abs(num1) !== num1 && Math.abs(num2) !== num2;
  },
  isSameXCoordinateArea: function isSameXCoordinateArea(p1, p2) {
    return this.isSameSign(p1.x, p2.x);
  },
  isCollision: function isCollision(obj1, obj2) {
    obj1.end = {};
    obj1.end.x = obj1.start.x + obj1.width;
    obj1.end.y = obj1.start.y - obj1.height;
    obj2.end = {};
    obj2.end.x = obj2.start.x + obj2.width;
    obj2.end.y = obj2.start.y - obj2.height;
    var flag = obj2.start.x > obj1.end.x || obj2.end.x < obj1.start.x || obj2.end.y > obj1.start.y || obj2.start.y < obj1.end.y;
    return !flag;
  }
};

//简单的图表类
function uCharts(opts) {
  this.opts = opts;
  this.config = {};
  this.context = null;
  this.canvas = null;
  this.colors = {};
  this.canvasId = "";
  this.data = {};
  this.init(opts);
}

uCharts.prototype.init = function(opts) {
  this.type = opts.type;
  this.canvas = uni.createCanvasContext(opts.canvasId, this);
  this.canvasId = opts.canvasId;
  this.context = this.canvas;
  this.chartData = {};
  this.chartData.categories = opts.categories || [];
  this.chartData.series = opts.series || [];
  Object.assign(this.config, config);
  
  // 保存配置和数据
  this.internalConfig = {
    chartType: this.type,
    categories: this.chartData.categories,
    series: this.chartData.series
  };
};

uCharts.prototype.updateData = function(data) {
  this.chartData.categories = data.categories || this.chartData.categories;
  this.chartData.series = data.series || this.chartData.series;
  
  this.draw();
};

uCharts.prototype.draw = function() {
  // 简单绘制一个坐标轴作为占位，实际项目中应替换为完整的uCharts实现
  let width = 300, height = 200;
  let padding = this.config.padding;
  
  // 清空画布
  this.context.clearRect(0, 0, width, height);
  
  // 绘制X轴
  this.context.beginPath();
  this.context.moveTo(padding[3], height - padding[2]);
  this.context.lineTo(width - padding[1], height - padding[2]);
  this.context.setStrokeStyle('#cccccc');
  this.context.stroke();
  
  // 绘制Y轴
  this.context.beginPath();
  this.context.moveTo(padding[3], padding[0]);
  this.context.lineTo(padding[3], height - padding[2]);
  this.context.setStrokeStyle('#cccccc');
  this.context.stroke();
  
  // 绘制文字
  this.context.setFontSize(12);
  this.context.setFillStyle('#333333');
  this.context.fillText('图表绘制区域', width / 2 - 40, height / 2);
  
  // 完成绘制
  this.context.draw();
};

uCharts.prototype.clear = function() {
  this.context.clearRect(0, 0, this.opts.width || 375, this.opts.height || 250);
  this.context.draw();
};

export default uCharts;
