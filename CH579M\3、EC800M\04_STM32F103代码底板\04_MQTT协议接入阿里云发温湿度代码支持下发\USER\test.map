Component: ARM Compiler 5.06 update 7 (build 960) Tool: armlink [4d3601]

==============================================================================

Section Cross References

    main.o(.text) refers to delay.o(.text) for delay_init
    main.o(.text) refers to sys.o(.text) for NVIC_Configuration
    main.o(.text) refers to led.o(.text) for LED_Init
    main.o(.text) refers to usart.o(.text) for uart_init
    main.o(.text) refers to wdg.o(.text) for IWDG_Init
    main.o(.text) refers to noretval__2printf.o(.text) for __2printf
    main.o(.text) refers to bsp_i2c.o(.text) for I2C_Bus_Init
    main.o(.text) refers to ec800.o(.text) for Uart1_SendStr
    main.o(.text) refers to aht20.o(.text) for AHT20_Init
    system_stm32f10x.o(.text) refers to stm32f10x_rcc.o(.text) for RCC_DeInit
    system_stm32f10x.o(.text) refers to stm32f10x_flash.o(.text) for FLASH_PrefetchBufferCmd
    system_stm32f10x.o(.text) refers to system_stm32f10x.o(.data) for SystemCoreClock
    led.o(.text) refers to stm32f10x_rcc.o(.text) for RCC_APB2PeriphClockCmd
    led.o(.text) refers to stm32f10x_gpio.o(.text) for GPIO_Init
    wdg.o(.text) refers to stm32f10x_iwdg.o(.text) for IWDG_WriteAccessCmd
    ec800.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    ec800.o(.text) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    ec800.o(.text) refers to _printf_str.o(.text) for _printf_str
    ec800.o(.text) refers to _printf_pad.o(.text) for _printf_pre_padding
    ec800.o(.text) refers to _printf_truncate.o(.text) for _printf_truncate_unsigned
    ec800.o(.text) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    ec800.o(.text) refers to _printf_i.o(.ARM.Collect$$_printf_percent$$00000008) for _printf_i
    ec800.o(.text) refers to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    ec800.o(.text) refers to _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B) for _printf_o
    ec800.o(.text) refers to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    ec800.o(.text) refers to _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) for _printf_c
    ec800.o(.text) refers to _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) for _printf_lc
    ec800.o(.text) refers to _printf_dec.o(.text) for _printf_int_dec
    ec800.o(.text) refers to _printf_oct_int.o(.text) for _printf_longlong_oct
    ec800.o(.text) refers to _printf_hex_int.o(.text) for _printf_longlong_hex
    ec800.o(.text) refers to _printf_wctomb.o(.text) for _printf_wctomb
    ec800.o(.text) refers to wdg.o(.text) for IWDG_Feed
    ec800.o(.text) refers to noretval__2printf.o(.text) for __2printf
    ec800.o(.text) refers to delay.o(.text) for delay_ms
    ec800.o(.text) refers to strstr.o(.text) for strstr
    ec800.o(.text) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    ec800.o(.text) refers to noretval__2sprintf.o(.text) for __2sprintf
    ec800.o(.text) refers to strlen.o(.text) for strlen
    ec800.o(.text) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    ec800.o(.text) refers to cjson.o(.text) for cJSON_Parse
    ec800.o(.text) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    ec800.o(.text) refers to usart.o(.bss) for RxBuffer
    ec800.o(.text) refers to usart.o(.data) for RxCounter
    ec800.o(.text) refers to ec800.o(.data) for strx
    ec800.o(.text) refers to ec800.o(.conststring) for .conststring
    dht11.o(.text) refers to delay.o(.text) for delay_ms
    cjson.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    cjson.o(.text) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    cjson.o(.text) refers to _printf_dec.o(.text) for _printf_int_dec
    cjson.o(.text) refers to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    cjson.o(.text) refers to printf1.o(x$fpl$printf1) for _printf_fp_dec
    cjson.o(.text) refers to _printf_e.o(.ARM.Collect$$_printf_percent$$00000004) for _printf_e
    cjson.o(.text) refers to _printf_pad.o(.text) for _printf_pre_padding
    cjson.o(.text) refers to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    cjson.o(.text) refers to _printf_hex_int.o(.text) for _printf_longlong_hex
    cjson.o(.text) refers to tolower.o(.text) for tolower
    cjson.o(.text) refers to strlen.o(.text) for strlen
    cjson.o(.text) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    cjson.o(.text) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    cjson.o(.text) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    cjson.o(.text) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    cjson.o(.text) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    cjson.o(.text) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    cjson.o(.text) refers to pow.o(i.pow) for pow
    cjson.o(.text) refers to dfix.o(x$fpl$dfix) for __aeabi_d2iz
    cjson.o(.text) refers to deqf.o(x$fpl$deqf) for __aeabi_cdcmpeq
    cjson.o(.text) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    cjson.o(.text) refers to drleqf.o(x$fpl$drleqf) for __aeabi_cdrcmple
    cjson.o(.text) refers to cjson.o(.data) for ep
    cjson.o(.text) refers to h1_alloc.o(.text) for malloc
    cjson.o(.text) refers to h1_free.o(.text) for free
    cjson.o(.text) refers to noretval__2sprintf.o(.text) for __2sprintf
    cjson.o(.text) refers to floor.o(i.floor) for floor
    cjson.o(.text) refers to cjson.o(.constdata) for firstByteMark
    cjson.o(.text) refers to strcpy.o(.text) for strcpy
    cjson.o(.text) refers to strchr.o(.text) for strchr
    cjson.o(.text) refers to strncmp.o(.text) for strncmp
    cjson.o(.text) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    cjson.o(.text) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    cjson.o(.data) refers to h1_alloc.o(.text) for malloc
    cjson.o(.data) refers to h1_free.o(.text) for free
    protocol.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    protocol.o(.text) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    protocol.o(.text) refers to _printf_str.o(.text) for _printf_str
    protocol.o(.text) refers to cjson.o(.text) for cJSON_CreateObject
    protocol.o(.text) refers to noretval__2printf.o(.text) for __2printf
    protocol.o(.text) refers to h1_free.o(.text) for free
    protocol.o(.text) refers to memcmp.o(.text) for memcmp
    protocol.o(.text) refers to h1_alloc.o(.text) for malloc
    protocol.o(.text) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    protocol.o(.text) refers to strlen.o(.text) for strlen
    protocol.o(.text) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    aht20.o(.text) refers to bsp_i2c.o(.text) for Sensors_I2C_ReadRegister
    aht20.o(.text) refers to delay.o(.text) for delay_ms
    aht20.o(.text) refers to aht20.o(.data) for count
    bsp_i2c.o(.text) refers to stm32f10x_rcc.o(.text) for RCC_APB2PeriphClockCmd
    bsp_i2c.o(.text) refers to stm32f10x_gpio.o(.text) for GPIO_Init
    bsp_i2c.o(.text) refers to delay.o(.text) for delay_ms
    bsp_i2c.o(.text) refers to bsp_i2c.o(.data) for RETRY_IN_MLSEC
    stm32f10x_gpio.o(.text) refers to stm32f10x_rcc.o(.text) for RCC_APB2PeriphResetCmd
    stm32f10x_usart.o(.text) refers to stm32f10x_rcc.o(.text) for RCC_APB2PeriphResetCmd
    stm32f10x_rcc.o(.text) refers to stm32f10x_rcc.o(.data) for APBAHBPrescTable
    startup_stm32f10x_hd.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_hd.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_hd.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_hd.o(RESET) refers to startup_stm32f10x_hd.o(STACK) for __initial_sp
    startup_stm32f10x_hd.o(RESET) refers to startup_stm32f10x_hd.o(.text) for Reset_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(.text) for NMI_Handler
    startup_stm32f10x_hd.o(RESET) refers to usart.o(.text) for USART1_IRQHandler
    startup_stm32f10x_hd.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_hd.o(.text) refers to system_stm32f10x.o(.text) for SystemInit
    startup_stm32f10x_hd.o(.text) refers to __main.o(!!!main) for __main
    startup_stm32f10x_hd.o(.text) refers to startup_stm32f10x_hd.o(HEAP) for Heap_Mem
    startup_stm32f10x_hd.o(.text) refers to startup_stm32f10x_hd.o(STACK) for Stack_Mem
    usart.o(.text) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(.text) refers to stm32f10x_rcc.o(.text) for RCC_APB2PeriphClockCmd
    usart.o(.text) refers to stm32f10x_usart.o(.text) for USART_DeInit
    usart.o(.text) refers to stm32f10x_gpio.o(.text) for GPIO_Init
    usart.o(.text) refers to misc.o(.text) for NVIC_Init
    usart.o(.text) refers to usart.o(.data) for RxCounter
    usart.o(.text) refers to usart.o(.bss) for RxBuffer
    usart.o(.bss) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(.data) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    delay.o(.text) refers to misc.o(.text) for SysTick_CLKSourceConfig
    delay.o(.text) refers to system_stm32f10x.o(.data) for SystemCoreClock
    delay.o(.text) refers to delay.o(.data) for fac_us
    sys.o(.text) refers to misc.o(.text) for NVIC_PriorityGroupConfig
    use_no_semi_2.o(.text) refers (Special) to use_no_semi.o(.text) for __use_no_semihosting_swi
    malloc.o(.text) refers (Special) to hguard.o(.text) for __heap$guard
    malloc.o(.text) refers (Special) to init_alloc.o(.text) for _init_alloc
    malloc.o(.text) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    malloc.o(.text) refers to heapstubs.o(.text) for __Heap_Alloc
    free.o(.text) refers (Special) to hguard.o(.text) for __heap$guard
    free.o(.text) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    free.o(.text) refers to heapstubs.o(.text) for __Heap_Free
    h1_alloc.o(.text) refers (Special) to h1_init.o(.text) for __Heap_Initialize
    h1_alloc.o(.text) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    h1_alloc.o(.text) refers to init_alloc.o(.text) for __Heap_Full
    h1_free.o(.text) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    h1_alloc_mt.o(.text) refers (Special) to h1_init.o(.text) for __Heap_Initialize
    h1_alloc_mt.o(.text) refers to init_alloc.o(.text) for __Heap_Full
    h1_alloc_mt.o(.text) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    h1_free_mt.o(.text) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2.o(i._FDIterate) refers to heap2.o(.conststring) for .conststring
    heap2.o(i.___Heap_ProvideMemory$realtime) refers to fdtree.o(i._FDTree_Delete) for _FDTree_Delete
    heap2.o(i.___Heap_ProvideMemory$realtime) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2.o(i.___Heap_Stats$realtime) refers to heap2.o(i._Heap2_StatsIterate) for _Heap2_StatsIterate
    heap2.o(i.___Heap_Valid$realtime) refers to heap2.o(i._FDIterate) for _FDIterate
    heap2.o(i.___Heap_Valid$realtime) refers to heap2.o(.conststring) for .conststring
    heap2.o(i._free$realtime) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2.o(i._free$realtime) refers to fdtree.o(i._FDTree_Delete) for _FDTree_Delete
    heap2.o(i._free$realtime) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2.o(i._malloc$realtime) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2.o(i._malloc$realtime) refers to fdtree.o(i._FDTree_FindFirst) for _FDTree_FindFirst
    heap2.o(i._malloc$realtime) refers to init_alloc.o(.text) for __Heap_Full
    heap2.o(i._malloc$realtime) refers to fdtree.o(i._FDTree_RemoveNode) for _FDTree_RemoveNode
    heap2.o(i._malloc$realtime) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2.o(i._posix_memalign$realtime) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2.o(i._posix_memalign$realtime) refers to fdtree.o(i._FDTree_FindFirst) for _FDTree_FindFirst
    heap2.o(i._posix_memalign$realtime) refers to init_alloc.o(.text) for __Heap_Full
    heap2.o(i._posix_memalign$realtime) refers to fdtree.o(i._FDTree_RemoveNode) for _FDTree_RemoveNode
    heap2.o(i._posix_memalign$realtime) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2.o(i._realloc$realtime) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2.o(i._realloc$realtime) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2.o(i._realloc$realtime) refers to h1_free.o(.text) for free
    heap2.o(i._realloc$realtime) refers to h1_alloc.o(.text) for malloc
    heap2.o(i._realloc$realtime) refers to fdtree.o(i._FDTree_Delete) for _FDTree_Delete
    heap2.o(i._realloc$realtime) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    heap2mt.o(i._FDIterate) refers to heap2mt.o(.conststring) for .conststring
    heap2mt.o(i.___Heap_Initialize$realtime$concurrent) refers to mutex_dummy.o(.text) for _mutex_initialize
    heap2mt.o(i.___Heap_ProvideMemory$realtime$concurrent) refers to fdtree.o(i._FDTree_Delete) for _FDTree_Delete
    heap2mt.o(i.___Heap_ProvideMemory$realtime$concurrent) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2mt.o(i.___Heap_Stats$realtime$concurrent) refers to heap2mt.o(i._Heap2_StatsIterate) for _Heap2_StatsIterate
    heap2mt.o(i.___Heap_Valid$realtime$concurrent) refers to heap2mt.o(i._FDIterate) for _FDIterate
    heap2mt.o(i.___Heap_Valid$realtime$concurrent) refers to heap2mt.o(.conststring) for .conststring
    heap2mt.o(i._free$realtime$concurrent) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2mt.o(i._free$realtime$concurrent) refers to fdtree.o(i._FDTree_Delete) for _FDTree_Delete
    heap2mt.o(i._free$realtime$concurrent) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2mt.o(i._malloc$realtime$concurrent) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2mt.o(i._malloc$realtime$concurrent) refers to fdtree.o(i._FDTree_FindFirst) for _FDTree_FindFirst
    heap2mt.o(i._malloc$realtime$concurrent) refers to init_alloc.o(.text) for __Heap_Full
    heap2mt.o(i._malloc$realtime$concurrent) refers to fdtree.o(i._FDTree_RemoveNode) for _FDTree_RemoveNode
    heap2mt.o(i._malloc$realtime$concurrent) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2mt.o(i._posix_memalign$realtime$concurrent) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2mt.o(i._posix_memalign$realtime$concurrent) refers to fdtree.o(i._FDTree_FindFirst) for _FDTree_FindFirst
    heap2mt.o(i._posix_memalign$realtime$concurrent) refers to init_alloc.o(.text) for __Heap_Full
    heap2mt.o(i._posix_memalign$realtime$concurrent) refers to fdtree.o(i._FDTree_RemoveNode) for _FDTree_RemoveNode
    heap2mt.o(i._posix_memalign$realtime$concurrent) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2mt.o(i._realloc$realtime$concurrent) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2mt.o(i._realloc$realtime$concurrent) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2mt.o(i._realloc$realtime$concurrent) refers to h1_free.o(.text) for free
    heap2mt.o(i._realloc$realtime$concurrent) refers to h1_alloc.o(.text) for malloc
    heap2mt.o(i._realloc$realtime$concurrent) refers to fdtree.o(i._FDTree_Delete) for _FDTree_Delete
    heap2mt.o(i._realloc$realtime$concurrent) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    tolower.o(.text) refers to rt_ctype_table.o(.text) for __rt_ctype_table
    __2printf.o(.text) refers to _printf_char_file.o(.text) for _printf_char_file
    __2printf.o(.text) refers to usart.o(.data) for __stdout
    __2sprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    __2sprintf.o(.text) refers to _sputc.o(.text) for _sputc
    noretval__2printf.o(.text) refers to _printf_char_file.o(.text) for _printf_char_file
    noretval__2printf.o(.text) refers to usart.o(.data) for __stdout
    noretval__2sprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    noretval__2sprintf.o(.text) refers to _sputc.o(.text) for _sputc
    __printf.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    _printf_str.o(.text) refers (Special) to _printf_char.o(.text) for _printf_cs_common
    _printf_str.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_str.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_dec.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_signed
    _printf_dec.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_wctomb.o(.text) refers (Special) to _printf_wchar.o(.text) for _printf_lcs_common
    _printf_wctomb.o(.text) refers to _wcrtomb.o(.text) for _wcrtomb
    _printf_wctomb.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_wctomb.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_wctomb.o(.text) refers to _printf_wctomb.o(.constdata) for .constdata
    _printf_wctomb.o(.constdata) refers (Special) to _printf_wchar.o(.text) for _printf_lcs_common
    _printf_oct_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_oct_int.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int_ll.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll.o(.text) refers to _printf_hex_ll.o(.constdata) for .constdata
    _printf_hex_int.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int.o(.text) refers to _printf_hex_int.o(.constdata) for .constdata
    _printf_hex_int_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ll.o(.text) refers to _printf_hex_int_ll.o(.constdata) for .constdata
    _printf_hex_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ptr.o(.text) refers to _printf_hex_ptr.o(.constdata) for .constdata
    _printf_hex_int_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ptr.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ptr.o(.text) refers to _printf_hex_int_ptr.o(.constdata) for .constdata
    _printf_hex_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll_ptr.o(.text) refers to _printf_hex_ll_ptr.o(.constdata) for .constdata
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll_ptr.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_hex_int_ll_ptr.o(.constdata) for .constdata
    __printf_flags.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags.o(.text) refers to __printf_flags.o(.constdata) for .constdata
    __printf_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to __printf_flags_ss.o(.constdata) for .constdata
    __printf_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_flags_wp.o(.constdata) for .constdata
    __printf_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_flags_ss_wp.o(.constdata) for .constdata
    _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) refers (Weak) to _printf_char.o(.text) for _printf_char
    _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) refers (Weak) to _printf_char.o(.text) for _printf_string
    _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) refers (Weak) to _printf_hex_int.o(.text) for _printf_int_hex
    _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B) refers (Weak) to _printf_oct_int.o(.text) for _printf_int_oct
    _printf_i.o(.ARM.Collect$$_printf_percent$$00000008) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_e.o(.ARM.Collect$$_printf_percent$$00000004) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) refers (Special) to _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017) for _printf_percent_end
    _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) refers (Weak) to _printf_wchar.o(.text) for _printf_wchar
    rt_memcpy_v6.o(.text) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    daddsub_clz.o(x$fpl$dadd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$dadd) refers to daddsub_clz.o(x$fpl$dsub) for _dsub1
    daddsub_clz.o(x$fpl$dadd) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    daddsub_clz.o(x$fpl$dadd) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    daddsub_clz.o(x$fpl$drsb) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$drsb) refers to daddsub_clz.o(x$fpl$dadd) for _dadd1
    daddsub_clz.o(x$fpl$drsb) refers to daddsub_clz.o(x$fpl$dsub) for _dsub1
    daddsub_clz.o(x$fpl$dsub) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$dsub) refers to daddsub_clz.o(x$fpl$dadd) for _dadd1
    daddsub_clz.o(x$fpl$dsub) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    deqf.o(x$fpl$deqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    deqf.o(x$fpl$deqf) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    deqf.o(x$fpl$deqf) refers to dcmpi.o(x$fpl$dcmpinf) for __fpl_dcmp_Inf
    dfix.o(x$fpl$dfix) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfix.o(x$fpl$dfix) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfix.o(x$fpl$dfixr) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfix.o(x$fpl$dfixr) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dflt_clz.o(x$fpl$dfltu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dflt_clz.o(x$fpl$dflt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dflt_clz.o(x$fpl$dfltn) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dleqf.o(x$fpl$dleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dleqf.o(x$fpl$dleqf) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dleqf.o(x$fpl$dleqf) refers to dcmpi.o(x$fpl$dcmpinf) for __fpl_dcmp_Inf
    dmul.o(x$fpl$dmul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dmul.o(x$fpl$dmul) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    dmul.o(x$fpl$dmul) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    drleqf.o(x$fpl$drleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    drleqf.o(x$fpl$drleqf) refers to dleqf.o(x$fpl$dleqf) for __fpl_dcmple_InfNaN
    f2d.o(x$fpl$f2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    f2d.o(x$fpl$f2d) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    f2d.o(x$fpl$f2d) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    printf1.o(x$fpl$printf1) refers to _printf_fp_dec.o(.text) for _printf_fp_dec_real
    floor.o(i.__softfp_floor) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    floor.o(i.__softfp_floor) refers to floor.o(i.floor) for floor
    floor.o(i.floor) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    floor.o(i.floor) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    floor.o(i.floor) refers to drleqf.o(x$fpl$drleqf) for __aeabi_cdrcmple
    floor.o(i.floor) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    pow.o(i.__softfp_pow) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    pow.o(i.__softfp_pow) refers to pow.o(i.pow) for pow
    pow.o(i.pow) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    pow.o(i.pow) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    pow.o(i.pow) refers to _rserrno.o(.text) for __set_errno
    pow.o(i.pow) refers to dunder.o(i.__mathlib_dbl_divzero) for __mathlib_dbl_divzero
    pow.o(i.pow) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    pow.o(i.pow) refers to sqrt.o(i.sqrt) for sqrt
    pow.o(i.pow) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    pow.o(i.pow) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    pow.o(i.pow) refers to dunder.o(i.__mathlib_dbl_invalid) for __mathlib_dbl_invalid
    pow.o(i.pow) refers to dunder.o(i.__mathlib_dbl_overflow) for __mathlib_dbl_overflow
    pow.o(i.pow) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    pow.o(i.pow) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    pow.o(i.pow) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    pow.o(i.pow) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    pow.o(i.pow) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    pow.o(i.pow) refers to qnan.o(.constdata) for __mathlib_zero
    pow.o(i.pow) refers to poly.o(i.__kernel_poly) for __kernel_poly
    pow.o(i.pow) refers to drleqf.o(x$fpl$drleqf) for __aeabi_cdrcmple
    pow.o(i.pow) refers to pow.o(.constdata) for .constdata
    pow.o(i.pow) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    pow.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    pow_x.o(i.____softfp_pow$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    pow_x.o(i.____softfp_pow$lsc) refers to pow_x.o(i.__pow$lsc) for __pow$lsc
    pow_x.o(i.__pow$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    pow_x.o(i.__pow$lsc) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    pow_x.o(i.__pow$lsc) refers to _rserrno.o(.text) for __set_errno
    pow_x.o(i.__pow$lsc) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    pow_x.o(i.__pow$lsc) refers to sqrt.o(i.sqrt) for sqrt
    pow_x.o(i.__pow$lsc) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    pow_x.o(i.__pow$lsc) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    pow_x.o(i.__pow$lsc) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    pow_x.o(i.__pow$lsc) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    pow_x.o(i.__pow$lsc) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    pow_x.o(i.__pow$lsc) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    pow_x.o(i.__pow$lsc) refers to qnan.o(.constdata) for __mathlib_zero
    pow_x.o(i.__pow$lsc) refers to poly.o(i.__kernel_poly) for __kernel_poly
    pow_x.o(i.__pow$lsc) refers to drleqf.o(x$fpl$drleqf) for __aeabi_cdrcmple
    pow_x.o(i.__pow$lsc) refers to pow_x.o(.constdata) for .constdata
    pow_x.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    rt_ctype_table.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    rt_ctype_table.o(.text) refers to lc_ctype_c.o(locale$$code) for _get_lc_ctype
    rt_heap_descriptor.o(.text) refers to rt_heap_descriptor.o(.bss) for __rt_heap_descriptor_data
    rt_heap_descriptor_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    init_alloc.o(.text) refers (Special) to hguard.o(.text) for __heap$guard
    init_alloc.o(.text) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000005) for __rt_lib_init_heap_2
    init_alloc.o(.text) refers (Special) to maybetermalloc1.o(.emb_text) for _maybe_terminate_alloc
    init_alloc.o(.text) refers to h1_extend.o(.text) for __Heap_ProvideMemory
    init_alloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    init_alloc.o(.text) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    init_alloc.o(.text) refers to h1_init.o(.text) for __Heap_Initialize
    h1_init_mt.o(.text) refers to mutex_dummy.o(.text) for _mutex_initialize
    _rserrno.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_fp_dec.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_dec.o(.text) refers (Special) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    _printf_fp_dec.o(.text) refers to bigflt0.o(.text) for _btod_etento
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_d2e) for _btod_d2e
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    _printf_fp_dec.o(.text) refers to lludiv10.o(.text) for _ll_udiv10
    _printf_fp_dec.o(.text) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    _printf_fp_dec.o(.text) refers to _printf_fp_infnan.o(.text) for _printf_fp_infnan
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_dec.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_char_common.o(.text) refers to __printf_flags_ss_wp.o(.text) for __printf
    _printf_char.o(.text) refers (Weak) to _printf_str.o(.text) for _printf_str
    _printf_char_file.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    _printf_char_file.o(.text) refers to ferror.o(.text) for ferror
    _printf_char_file.o(.text) refers to usart.o(.text) for fputc
    _printf_wchar.o(.text) refers (Weak) to _printf_wctomb.o(.text) for _printf_wctomb
    _wcrtomb.o(.text) refers to rt_ctype_table.o(.text) for __rt_ctype_table
    _get_argv.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv.o(.text) refers to h1_alloc.o(.text) for malloc
    _get_argv.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv.o(.text) refers to sys_command.o(.text) for _sys_command_string
    libinit2.o(.ARM.Collect$$libinit$$00000005) refers (Weak) to init_alloc.o(.text) for _init_alloc
    libinit2.o(.ARM.Collect$$libinit$$0000000F) refers (Weak) to rt_locale_intlibspace.o(.text) for __rt_locale
    libinit2.o(.ARM.Collect$$libinit$$00000010) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers (Weak) to lc_ctype_c.o(locale$$code) for _get_lc_ctype
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers (Weak) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000026) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000027) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    dcmpi.o(x$fpl$dcmpinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$drdiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$drdiv) refers to ddiv.o(x$fpl$ddiv) for ddiv_entry
    ddiv.o(x$fpl$ddiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$ddiv) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    ddiv.o(x$fpl$ddiv) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dnaninf.o(x$fpl$dnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dretinf.o(x$fpl$dretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fnaninf.o(x$fpl$fnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    scalbn.o(x$fpl$scalbn) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    scalbn.o(x$fpl$scalbn) refers to dcheck1.o(x$fpl$dcheck1) for __fpl_dcheck_NaN1
    dunder.o(i.__mathlib_dbl_divzero) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_infnan) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    dunder.o(i.__mathlib_dbl_infnan2) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_invalid) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_overflow) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    dunder.o(i.__mathlib_dbl_posinfnan) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_underflow) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    fpclassify.o(i.__ARM_fpclassify) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    poly.o(i.__kernel_poly) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    poly.o(i.__kernel_poly) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    poly.o(i.__kernel_poly) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    qnan.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt.o(i.__softfp_sqrt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt.o(i.__softfp_sqrt) refers to dsqrt_noumaal.o(x$fpl$dsqrt) for _dsqrt
    sqrt.o(i.__softfp_sqrt) refers to _rserrno.o(.text) for __set_errno
    sqrt.o(i.sqrt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt.o(i.sqrt) refers to dsqrt_noumaal.o(x$fpl$dsqrt) for _dsqrt
    sqrt.o(i.sqrt) refers to _rserrno.o(.text) for __set_errno
    sqrt_x.o(i.____softfp_sqrt$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to _rserrno.o(.text) for __set_errno
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to dsqrt_noumaal.o(x$fpl$dsqrt) for _dsqrt
    sqrt_x.o(i.__sqrt$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt_x.o(i.__sqrt$lsc) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    sqrt_x.o(i.__sqrt$lsc) refers to _rserrno.o(.text) for __set_errno
    sqrt_x.o(i.__sqrt$lsc) refers to dsqrt_noumaal.o(x$fpl$dsqrt) for _dsqrt
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to main.o(.text) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    rt_locale.o(.text) refers to rt_locale.o(.bss) for __rt_locale_data
    rt_locale_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    rt_errno_addr.o(.text) refers to rt_errno_addr.o(.bss) for __aeabi_errno_addr_data
    rt_errno_addr_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    maybetermalloc2.o(.emb_text) refers (Special) to term_alloc.o(.text) for _terminate_alloc
    h1_extend.o(.text) refers to h1_free.o(.text) for free
    h1_extend_mt.o(.text) refers to h1_free_mt.o(.text) for _free_internal
    _printf_fp_infnan.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_infnan.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    bigflt0.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    bigflt0.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    bigflt0.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    bigflt0.o(.text) refers to bigflt0.o(.constdata) for .constdata
    bigflt0.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers to btod.o(CL$$btod_d2e_norm_op1) for _d2e_norm_op1
    btod.o(CL$$btod_d2e_norm_op1) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e_norm_op1) refers to btod.o(CL$$btod_d2e_denorm_low) for _d2e_denorm_low
    btod.o(CL$$btod_d2e_denorm_low) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_ediv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_emuld) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_edivd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_e2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2d) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_mult_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_div_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    lc_ctype_c.o(locale$$data) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000012) for __rt_lib_init_lc_ctype_2
    lc_ctype_c.o(locale$$code) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000012) for __rt_lib_init_lc_ctype_2
    lc_ctype_c.o(locale$$code) refers to strcmpv7m.o(.text) for strcmp
    lc_ctype_c.o(locale$$code) refers to lc_ctype_c.o(locale$$data) for __lcctype_c_name
    lc_numeric_c.o(locale$$data) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000016) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000016) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers to strcmpv7m.o(.text) for strcmp
    lc_numeric_c.o(locale$$code) refers to lc_numeric_c.o(locale$$data) for __lcnum_c_name
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    heapauxa.o(.text) refers to heapauxa.o(.data) for .data
    argv_veneer.o(.emb_text) refers to no_argv.o(.text) for __ARM_get_argv
    dcheck1.o(x$fpl$dcheck1) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dcheck1.o(x$fpl$dcheck1) refers to retnan.o(x$fpl$retnan) for __fpl_return_NaN
    dsqrt_noumaal.o(x$fpl$dsqrt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dsqrt_noumaal.o(x$fpl$dsqrt) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    istatus.o(x$fpl$ieeestatus) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_stm32f10x_hd.o(.text) for __user_initial_stackheap
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to usart.o(.text) for _sys_exit
    term_alloc.o(.text) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000010) for __rt_lib_shutdown_heap_2
    term_alloc.o(.text) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    term_alloc.o(.text) refers to h1_final.o(.text) for __Heap_Finalize
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    defsig_exit.o(.text) refers to usart.o(.text) for _sys_exit
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002C) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000002) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000A) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000011) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000033) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_user_alloc_1
    retnan.o(x$fpl$retnan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    retnan.o(x$fpl$retnan) refers to trapv.o(x$fpl$trapveneer) for __fpl_cmpreturn
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to usart.o(.text) for _ttywrch
    libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F) refers (Weak) to term_alloc.o(.text) for _terminate_alloc
    trapv.o(x$fpl$trapveneer) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to usart.o(.text) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000004) for __rt_lib_shutdown_cpp_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000002) for __rt_lib_shutdown_fini_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000009) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000011) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000012) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000006) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E) for __rt_lib_shutdown_user_alloc_1


==============================================================================

Removing Unused input sections from the image.

    Removing ec800.o(.bss), (100 bytes).
    Removing dht11.o(.text), (400 bytes).
    Removing protocol.o(.text), (484 bytes).
    Removing core_cm3.o(.emb_text), (32 bytes).

4 unused section(s) (total 1016 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit1.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit3.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardshut.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit2.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_copy.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_zi.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry4.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_raise.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_heap_descriptor_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_heap_descriptor.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_ctype_table.o ABSOLUTE
    ../clib/angel/scatter.s                  0x00000000   Number         0  __scatter.o ABSOLUTE
    ../clib/angel/startup.s                  0x00000000   Number         0  __main.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi_2.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  mutex_dummy.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  sys_stackheap_outer.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  indicate_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  libspace.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv_nomalloc.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  no_argv.o ABSOLUTE
    ../clib/bigflt.c                         0x00000000   Number         0  bigflt0.o ABSOLUTE
    ../clib/btod.s                           0x00000000   Number         0  btod.o ABSOLUTE
    ../clib/ctype.c                          0x00000000   Number         0  tolower.o ABSOLUTE
    ../clib/fenv.c                           0x00000000   Number         0  _rserrno.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_init.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_free_mt.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_extend.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_extend_mt.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_alloc.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_final.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_free.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_alloc_mt.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_final_mt.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_init_mt.o ABSOLUTE
    ../clib/heap2.c                          0x00000000   Number         0  fdtree.o ABSOLUTE
    ../clib/heap2.c                          0x00000000   Number         0  heap2mt.o ABSOLUTE
    ../clib/heap2.c                          0x00000000   Number         0  heap2.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  term_alloc.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  maybetermalloc2.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  maybetermalloc1.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  maybetermalloc2.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hrguard.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  heapstubs.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  init_alloc.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  maybetermalloc1.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  malloc.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hguard.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  free.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxa.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxi.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit2.o ABSOLUTE
    ../clib/locale.c                         0x00000000   Number         0  _wcrtomb.o ABSOLUTE
    ../clib/locale.s                         0x00000000   Number         0  lc_ctype_c.o ABSOLUTE
    ../clib/locale.s                         0x00000000   Number         0  lc_numeric_c.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  lludiv10.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memcpy_v6.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memcpy_w.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  strcmpv7m.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memclr_w.o ABSOLUTE
    ../clib/misc.s                           0x00000000   Number         0  printf_stubs.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_pad.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_intcommon.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  noretval__2sprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __2printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_common.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _sputc.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_file.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_wchar.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __2sprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_oct_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_oct_int.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_oct_int_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_wctomb.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ll_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_str.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_infnan.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ll_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_truncate.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_nopercent.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  noretval__2printf.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_f.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_e.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_lc.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent_end.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_c.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_s.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_x.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_o.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_i.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_l.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_d.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_u.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_segv_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_other.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_exit.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_pvfn_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_stak_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  __raise.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_general.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_cppl_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_inner.o ABSOLUTE
    ../clib/signal.s                         0x00000000   Number         0  defsig.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  ferror.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  ferror_locked.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  exit.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  strchr.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  strncmp.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  strlen.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  strcpy.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  memcmp.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  strstr.o ABSOLUTE
    ../fplib/daddsub.s                       0x00000000   Number         0  daddsub_clz.o ABSOLUTE
    ../fplib/dcheck1.s                       0x00000000   Number         0  dcheck1.o ABSOLUTE
    ../fplib/dcmpi.s                         0x00000000   Number         0  dcmpi.o ABSOLUTE
    ../fplib/ddiv.s                          0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/deqf.s                          0x00000000   Number         0  deqf.o ABSOLUTE
    ../fplib/dfix.s                          0x00000000   Number         0  dfix.o ABSOLUTE
    ../fplib/dflt.s                          0x00000000   Number         0  dflt_clz.o ABSOLUTE
    ../fplib/dleqf.s                         0x00000000   Number         0  dleqf.o ABSOLUTE
    ../fplib/dmul.s                          0x00000000   Number         0  dmul.o ABSOLUTE
    ../fplib/dnaninf.s                       0x00000000   Number         0  dnaninf.o ABSOLUTE
    ../fplib/dretinf.s                       0x00000000   Number         0  dretinf.o ABSOLUTE
    ../fplib/drleqf.s                        0x00000000   Number         0  drleqf.o ABSOLUTE
    ../fplib/dsqrt.s                         0x00000000   Number         0  dsqrt_noumaal.o ABSOLUTE
    ../fplib/f2d.s                           0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/fnaninf.s                       0x00000000   Number         0  fnaninf.o ABSOLUTE
    ../fplib/fpinit.s                        0x00000000   Number         0  fpinit.o ABSOLUTE
    ../fplib/istatus.s                       0x00000000   Number         0  istatus.o ABSOLUTE
    ../fplib/printf1.s                       0x00000000   Number         0  printf1.o ABSOLUTE
    ../fplib/retnan.s                        0x00000000   Number         0  retnan.o ABSOLUTE
    ../fplib/scalbn.s                        0x00000000   Number         0  scalbn.o ABSOLUTE
    ../fplib/trapv.s                         0x00000000   Number         0  trapv.o ABSOLUTE
    ../fplib/usenofp.s                       0x00000000   Number         0  usenofp.o ABSOLUTE
    ../mathlib/dunder.c                      0x00000000   Number         0  dunder.o ABSOLUTE
    ../mathlib/floor.c                       0x00000000   Number         0  floor.o ABSOLUTE
    ../mathlib/fpclassify.c                  0x00000000   Number         0  fpclassify.o ABSOLUTE
    ../mathlib/poly.c                        0x00000000   Number         0  poly.o ABSOLUTE
    ../mathlib/pow.c                         0x00000000   Number         0  pow.o ABSOLUTE
    ../mathlib/pow.c                         0x00000000   Number         0  pow_x.o ABSOLUTE
    ../mathlib/qnan.c                        0x00000000   Number         0  qnan.o ABSOLUTE
    ../mathlib/sqrt.c                        0x00000000   Number         0  sqrt_x.o ABSOLUTE
    ../mathlib/sqrt.c                        0x00000000   Number         0  sqrt.o ABSOLUTE
    ..\CORE\core_cm3.c                       0x00000000   Number         0  core_cm3.o ABSOLUTE
    ..\CORE\startup_stm32f10x_hd.s           0x00000000   Number         0  startup_stm32f10x_hd.o ABSOLUTE
    ..\HARDWARE\AHT20\AHT20.c                0x00000000   Number         0  aht20.o ABSOLUTE
    ..\HARDWARE\AHT20\bsp_i2c.c              0x00000000   Number         0  bsp_i2c.o ABSOLUTE
    ..\HARDWARE\DHT11\dht11.c                0x00000000   Number         0  dht11.o ABSOLUTE
    ..\HARDWARE\EC800\EC800.c                0x00000000   Number         0  ec800.o ABSOLUTE
    ..\HARDWARE\LED\led.c                    0x00000000   Number         0  led.o ABSOLUTE
    ..\HARDWARE\WDG\wdg.c                    0x00000000   Number         0  wdg.o ABSOLUTE
    ..\HARDWARE\cJSON\cJSON.c                0x00000000   Number         0  cjson.o ABSOLUTE
    ..\HARDWARE\cJSON\protocol.c             0x00000000   Number         0  protocol.o ABSOLUTE
    ..\STM32F10x_FWLib\src\misc.c            0x00000000   Number         0  misc.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_flash.c 0x00000000   Number         0  stm32f10x_flash.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_gpio.c  0x00000000   Number         0  stm32f10x_gpio.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_iwdg.c  0x00000000   Number         0  stm32f10x_iwdg.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_rcc.c   0x00000000   Number         0  stm32f10x_rcc.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_usart.c 0x00000000   Number         0  stm32f10x_usart.o ABSOLUTE
    ..\SYSTEM\delay\delay.c                  0x00000000   Number         0  delay.o ABSOLUTE
    ..\SYSTEM\sys\sys.c                      0x00000000   Number         0  sys.o ABSOLUTE
    ..\SYSTEM\usart\usart.c                  0x00000000   Number         0  usart.o ABSOLUTE
    ..\\CORE\\core_cm3.c                     0x00000000   Number         0  core_cm3.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    main.c                                   0x00000000   Number         0  main.o ABSOLUTE
    stm32f10x_it.c                           0x00000000   Number         0  stm32f10x_it.o ABSOLUTE
    system_stm32f10x.c                       0x00000000   Number         0  system_stm32f10x.o ABSOLUTE
    RESET                                    0x08000000   Section      304  startup_stm32f10x_hd.o(RESET)
    !!!main                                  0x08000130   Section        8  __main.o(!!!main)
    !!!scatter                               0x08000138   Section       52  __scatter.o(!!!scatter)
    !!handler_copy                           0x0800016c   Section       26  __scatter_copy.o(!!handler_copy)
    !!handler_zi                             0x08000188   Section       28  __scatter_zi.o(!!handler_zi)
    .ARM.Collect$$_printf_percent$$00000000  0x080001a4   Section        0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    .ARM.Collect$$_printf_percent$$00000003  0x080001a4   Section        6  _printf_f.o(.ARM.Collect$$_printf_percent$$00000003)
    .ARM.Collect$$_printf_percent$$00000004  0x080001aa   Section        6  _printf_e.o(.ARM.Collect$$_printf_percent$$00000004)
    .ARM.Collect$$_printf_percent$$00000008  0x080001b0   Section        6  _printf_i.o(.ARM.Collect$$_printf_percent$$00000008)
    .ARM.Collect$$_printf_percent$$00000009  0x080001b6   Section        6  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    .ARM.Collect$$_printf_percent$$0000000A  0x080001bc   Section        6  _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A)
    .ARM.Collect$$_printf_percent$$0000000B  0x080001c2   Section        6  _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B)
    .ARM.Collect$$_printf_percent$$0000000C  0x080001c8   Section        6  _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C)
    .ARM.Collect$$_printf_percent$$00000012  0x080001ce   Section       10  _printf_l.o(.ARM.Collect$$_printf_percent$$00000012)
    .ARM.Collect$$_printf_percent$$00000013  0x080001d8   Section        6  _printf_c.o(.ARM.Collect$$_printf_percent$$00000013)
    .ARM.Collect$$_printf_percent$$00000014  0x080001de   Section        6  _printf_s.o(.ARM.Collect$$_printf_percent$$00000014)
    .ARM.Collect$$_printf_percent$$00000015  0x080001e4   Section        6  _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015)
    .ARM.Collect$$_printf_percent$$00000017  0x080001ea   Section        4  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    .ARM.Collect$$libinit$$00000000          0x080001ee   Section        2  libinit.o(.ARM.Collect$$libinit$$00000000)
    .ARM.Collect$$libinit$$00000002          0x080001f0   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000002)
    .ARM.Collect$$libinit$$00000004          0x080001f0   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    .ARM.Collect$$libinit$$00000005          0x080001f0   Section        8  libinit2.o(.ARM.Collect$$libinit$$00000005)
    .ARM.Collect$$libinit$$0000000A          0x080001f8   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    .ARM.Collect$$libinit$$0000000C          0x080001f8   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    .ARM.Collect$$libinit$$0000000E          0x080001f8   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    .ARM.Collect$$libinit$$0000000F          0x080001f8   Section        6  libinit2.o(.ARM.Collect$$libinit$$0000000F)
    .ARM.Collect$$libinit$$00000011          0x080001fe   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    .ARM.Collect$$libinit$$00000012          0x080001fe   Section       12  libinit2.o(.ARM.Collect$$libinit$$00000012)
    .ARM.Collect$$libinit$$00000013          0x0800020a   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    .ARM.Collect$$libinit$$00000015          0x0800020a   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    .ARM.Collect$$libinit$$00000016          0x0800020a   Section       10  libinit2.o(.ARM.Collect$$libinit$$00000016)
    .ARM.Collect$$libinit$$00000017          0x08000214   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    .ARM.Collect$$libinit$$00000019          0x08000214   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    .ARM.Collect$$libinit$$0000001B          0x08000214   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    .ARM.Collect$$libinit$$0000001D          0x08000214   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    .ARM.Collect$$libinit$$0000001F          0x08000214   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    .ARM.Collect$$libinit$$00000021          0x08000214   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    .ARM.Collect$$libinit$$00000023          0x08000214   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    .ARM.Collect$$libinit$$00000025          0x08000214   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    .ARM.Collect$$libinit$$0000002C          0x08000214   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    .ARM.Collect$$libinit$$0000002E          0x08000214   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    .ARM.Collect$$libinit$$00000030          0x08000214   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    .ARM.Collect$$libinit$$00000032          0x08000214   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    .ARM.Collect$$libinit$$00000033          0x08000214   Section        2  libinit2.o(.ARM.Collect$$libinit$$00000033)
    .ARM.Collect$$libshutdown$$00000000      0x08000216   Section        2  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    .ARM.Collect$$libshutdown$$00000002      0x08000218   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    .ARM.Collect$$libshutdown$$00000004      0x08000218   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    .ARM.Collect$$libshutdown$$00000006      0x08000218   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000006)
    .ARM.Collect$$libshutdown$$00000009      0x08000218   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000009)
    .ARM.Collect$$libshutdown$$0000000C      0x08000218   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    .ARM.Collect$$libshutdown$$0000000E      0x08000218   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E)
    .ARM.Collect$$libshutdown$$00000011      0x08000218   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000011)
    .ARM.Collect$$libshutdown$$00000012      0x08000218   Section        2  libshutdown2.o(.ARM.Collect$$libshutdown$$00000012)
    .ARM.Collect$$rtentry$$00000000          0x0800021a   Section        0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    .ARM.Collect$$rtentry$$00000002          0x0800021a   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    .ARM.Collect$$rtentry$$00000004          0x0800021a   Section        6  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    .ARM.Collect$$rtentry$$00000009          0x08000220   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    .ARM.Collect$$rtentry$$0000000A          0x08000220   Section        4  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    .ARM.Collect$$rtentry$$0000000C          0x08000224   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    .ARM.Collect$$rtentry$$0000000D          0x08000224   Section        8  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    .ARM.Collect$$rtexit$$00000000           0x0800022c   Section        2  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    .ARM.Collect$$rtexit$$00000002           0x0800022e   Section        0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    .ARM.Collect$$rtexit$$00000003           0x0800022e   Section        4  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    .ARM.Collect$$rtexit$$00000004           0x08000232   Section        6  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    .emb_text                                0x08000238   Section        0  maybetermalloc1.o(.emb_text)
    .text                                    0x08000238   Section        0  main.o(.text)
    .text                                    0x08000354   Section        0  stm32f10x_it.o(.text)
    .text                                    0x08000370   Section        0  system_stm32f10x.o(.text)
    SetSysClockTo72                          0x080003df   Thumb Code   214  system_stm32f10x.o(.text)
    SetSysClock                              0x080004b5   Thumb Code     8  system_stm32f10x.o(.text)
    .text                                    0x080005bc   Section        0  led.o(.text)
    .text                                    0x08000624   Section        0  wdg.o(.text)
    .text                                    0x08000650   Section        0  ec800.o(.text)
    .text                                    0x08000ca8   Section        0  cjson.o(.text)
    cJSON_strcasecmp                         0x08000caf   Thumb Code    76  cjson.o(.text)
    cJSON_strdup                             0x08000cfb   Thumb Code    40  cjson.o(.text)
    cJSON_New_Item                           0x08000d53   Thumb Code    26  cjson.o(.text)
    parse_number                             0x08000dbf   Thumb Code   432  cjson.o(.text)
    pow2gt                                   0x08000f6f   Thumb Code    28  cjson.o(.text)
    ensure                                   0x08000f8b   Thumb Code   104  cjson.o(.text)
    update                                   0x08000ff3   Thumb Code    32  cjson.o(.text)
    print_number                             0x08001013   Thumb Code   396  cjson.o(.text)
    parse_hex4                               0x0800119f   Thumb Code   276  cjson.o(.text)
    parse_string                             0x080012b3   Thumb Code   448  cjson.o(.text)
    print_string_ptr                         0x08001473   Thumb Code   498  cjson.o(.text)
    print_string                             0x08001665   Thumb Code    16  cjson.o(.text)
    skip                                     0x08001675   Thumb Code    18  cjson.o(.text)
    parse_object                             0x08001687   Thumb Code   270  cjson.o(.text)
    parse_array                              0x08001795   Thumb Code   166  cjson.o(.text)
    parse_value                              0x0800183b   Thumb Code   158  cjson.o(.text)
    print_object                             0x08001981   Thumb Code  1052  cjson.o(.text)
    print_array                              0x08001d9d   Thumb Code   578  cjson.o(.text)
    print_value                              0x08001fdf   Thumb Code   290  cjson.o(.text)
    suffix_object                            0x0800219b   Thumb Code     6  cjson.o(.text)
    create_reference                         0x080021a1   Thumb Code    48  cjson.o(.text)
    .text                                    0x08002728   Section        0  aht20.o(.text)
    .text                                    0x08002868   Section        0  bsp_i2c.o(.text)
    Soft_I2C_Delay                           0x08002869   Thumb Code    10  bsp_i2c.o(.text)
    Soft_I2C_Configuration                   0x0800287f   Thumb Code    60  bsp_i2c.o(.text)
    Soft_I2C_START                           0x080028c9   Thumb Code    90  bsp_i2c.o(.text)
    Soft_I2C_STOP                            0x08002923   Thumb Code    46  bsp_i2c.o(.text)
    Soft_I2C_SendACK                         0x08002951   Thumb Code    46  bsp_i2c.o(.text)
    Soft_I2C_SendNACK                        0x0800297f   Thumb Code    46  bsp_i2c.o(.text)
    Soft_I2C_SendByte                        0x080029f7   Thumb Code    92  bsp_i2c.o(.text)
    Soft_I2C_ReceiveByte                     0x08002a53   Thumb Code    86  bsp_i2c.o(.text)
    Soft_I2C_ReceiveByte_WithACK             0x08002aa9   Thumb Code    86  bsp_i2c.o(.text)
    Soft_DMP_I2C_Write                       0x08002aff   Thumb Code    90  bsp_i2c.o(.text)
    Soft_DMP_I2C_Read                        0x08002b59   Thumb Code   114  bsp_i2c.o(.text)
    .text                                    0x08002c70   Section        0  stm32f10x_gpio.o(.text)
    .text                                    0x08002fcc   Section        0  stm32f10x_iwdg.o(.text)
    .text                                    0x0800300c   Section        0  stm32f10x_usart.o(.text)
    .text                                    0x08003414   Section        0  stm32f10x_rcc.o(.text)
    .text                                    0x080037b8   Section        0  stm32f10x_flash.o(.text)
    .text                                    0x08003d74   Section        0  misc.o(.text)
    .text                                    0x08003e50   Section       64  startup_stm32f10x_hd.o(.text)
    .text                                    0x08003e90   Section        0  usart.o(.text)
    .text                                    0x08004044   Section        0  delay.o(.text)
    .text                                    0x08004118   Section        0  sys.o(.text)
    .text                                    0x08004124   Section        2  use_no_semi_2.o(.text)
    .text                                    0x08004126   Section        0  h1_alloc.o(.text)
    .text                                    0x08004184   Section        0  h1_free.o(.text)
    .text                                    0x080041d2   Section        0  tolower.o(.text)
    .text                                    0x080041ec   Section        0  noretval__2printf.o(.text)
    .text                                    0x08004204   Section        0  noretval__2sprintf.o(.text)
    .text                                    0x0800422c   Section        0  _printf_pad.o(.text)
    .text                                    0x0800427a   Section        0  _printf_truncate.o(.text)
    .text                                    0x0800429e   Section        0  _printf_str.o(.text)
    .text                                    0x080042f0   Section        0  _printf_dec.o(.text)
    .text                                    0x08004368   Section        0  _printf_wctomb.o(.text)
    .text                                    0x08004424   Section        0  _printf_oct_int.o(.text)
    .text                                    0x08004474   Section        0  _printf_hex_int.o(.text)
    .text                                    0x080044cc   Section        0  __printf_flags_ss_wp.o(.text)
    .text                                    0x08004654   Section        0  strchr.o(.text)
    .text                                    0x08004668   Section        0  strstr.o(.text)
    .text                                    0x0800468c   Section        0  strcpy.o(.text)
    .text                                    0x080046d4   Section        0  strlen.o(.text)
    .text                                    0x08004712   Section        0  strncmp.o(.text)
    .text                                    0x080047a8   Section      138  rt_memcpy_v6.o(.text)
    .text                                    0x08004832   Section      100  rt_memcpy_w.o(.text)
    .text                                    0x08004896   Section       78  rt_memclr_w.o(.text)
    .text                                    0x080048e4   Section        0  heapauxi.o(.text)
    .text                                    0x080048ea   Section        2  use_no_semi.o(.text)
    .text                                    0x080048ec   Section       16  rt_ctype_table.o(.text)
    .text                                    0x080048fc   Section        8  rt_heap_descriptor_intlibspace.o(.text)
    .text                                    0x08004904   Section        0  hguard.o(.text)
    .text                                    0x08004908   Section        0  init_alloc.o(.text)
    .text                                    0x08004992   Section        0  h1_init.o(.text)
    .text                                    0x080049a0   Section        0  _rserrno.o(.text)
    .text                                    0x080049b6   Section        0  _printf_intcommon.o(.text)
    .text                                    0x08004a68   Section        0  _printf_fp_dec.o(.text)
    _fp_digits                               0x08004a6b   Thumb Code   432  _printf_fp_dec.o(.text)
    .text                                    0x08004e88   Section        0  _printf_char_common.o(.text)
    _printf_input_char                       0x08004e89   Thumb Code    10  _printf_char_common.o(.text)
    .text                                    0x08004eb8   Section        0  _sputc.o(.text)
    .text                                    0x08004ec2   Section        0  _printf_char.o(.text)
    .text                                    0x08004ef0   Section        0  _printf_char_file.o(.text)
    .text                                    0x08004f14   Section        0  _printf_wchar.o(.text)
    .text                                    0x08004f40   Section        0  _wcrtomb.o(.text)
    .text                                    0x08004f80   Section        8  libspace.o(.text)
    .text                                    0x08004f88   Section        8  rt_locale_intlibspace.o(.text)
    .text                                    0x08004f90   Section        8  rt_errno_addr_intlibspace.o(.text)
    .text                                    0x08004f98   Section        0  h1_extend.o(.text)
    .text                                    0x08004fcc   Section      138  lludiv10.o(.text)
    .text                                    0x08005058   Section        0  _printf_fp_infnan.o(.text)
    .text                                    0x080050d8   Section        0  bigflt0.o(.text)
    .text                                    0x080051bc   Section        0  ferror.o(.text)
    .text                                    0x080051c4   Section        0  defsig_rtmem_outer.o(.text)
    .text                                    0x080051d2   Section       74  sys_stackheap_outer.o(.text)
    .text                                    0x0800521c   Section        0  exit.o(.text)
    .text                                    0x0800522e   Section        0  defsig_exit.o(.text)
    .text                                    0x08005238   Section        0  defsig_rtmem_inner.o(.text)
    .text                                    0x08005288   Section      128  strcmpv7m.o(.text)
    .text                                    0x08005308   Section        0  defsig_general.o(.text)
    CL$$btod_d2e                             0x0800533a   Section       62  btod.o(CL$$btod_d2e)
    CL$$btod_d2e_denorm_low                  0x08005378   Section       70  btod.o(CL$$btod_d2e_denorm_low)
    CL$$btod_d2e_norm_op1                    0x080053be   Section       96  btod.o(CL$$btod_d2e_norm_op1)
    CL$$btod_div_common                      0x0800541e   Section      824  btod.o(CL$$btod_div_common)
    CL$$btod_e2e                             0x08005756   Section      220  btod.o(CL$$btod_e2e)
    CL$$btod_ediv                            0x08005832   Section       42  btod.o(CL$$btod_ediv)
    CL$$btod_emul                            0x0800585c   Section       42  btod.o(CL$$btod_emul)
    CL$$btod_mult_common                     0x08005886   Section      580  btod.o(CL$$btod_mult_common)
    i.__ARM_fpclassify                       0x08005aca   Section        0  fpclassify.o(i.__ARM_fpclassify)
    i.__kernel_poly                          0x08005af2   Section        0  poly.o(i.__kernel_poly)
    i.__mathlib_dbl_divzero                  0x08005b9c   Section        0  dunder.o(i.__mathlib_dbl_divzero)
    i.__mathlib_dbl_infnan2                  0x08005bac   Section        0  dunder.o(i.__mathlib_dbl_infnan2)
    i.__mathlib_dbl_invalid                  0x08005bb0   Section        0  dunder.o(i.__mathlib_dbl_invalid)
    i.__mathlib_dbl_overflow                 0x08005bbc   Section        0  dunder.o(i.__mathlib_dbl_overflow)
    i.__mathlib_dbl_underflow                0x08005bcc   Section        0  dunder.o(i.__mathlib_dbl_underflow)
    i._is_digit                              0x08005bdc   Section        0  __printf_wp.o(i._is_digit)
    i.floor                                  0x08005bec   Section        0  floor.o(i.floor)
    i.pow                                    0x08005cc8   Section        0  pow.o(i.pow)
    i.sqrt                                   0x080066a0   Section        0  sqrt.o(i.sqrt)
    locale$$code                             0x080066ec   Section       44  lc_ctype_c.o(locale$$code)
    locale$$code                             0x08006718   Section       44  lc_numeric_c.o(locale$$code)
    x$fpl$dadd                               0x08006744   Section      336  daddsub_clz.o(x$fpl$dadd)
    _dadd1                                   0x08006755   Thumb Code     0  daddsub_clz.o(x$fpl$dadd)
    x$fpl$dcheck1                            0x08006894   Section       16  dcheck1.o(x$fpl$dcheck1)
    x$fpl$dcmpinf                            0x080068a4   Section       24  dcmpi.o(x$fpl$dcmpinf)
    x$fpl$ddiv                               0x080068bc   Section      688  ddiv.o(x$fpl$ddiv)
    ddiv_entry                               0x080068c3   Thumb Code     0  ddiv.o(x$fpl$ddiv)
    x$fpl$deqf                               0x08006b6c   Section      120  deqf.o(x$fpl$deqf)
    x$fpl$dfix                               0x08006be4   Section       94  dfix.o(x$fpl$dfix)
    x$fpl$dflt                               0x08006c42   Section       46  dflt_clz.o(x$fpl$dflt)
    x$fpl$dleqf                              0x08006c70   Section      120  dleqf.o(x$fpl$dleqf)
    x$fpl$dmul                               0x08006ce8   Section      340  dmul.o(x$fpl$dmul)
    x$fpl$dnaninf                            0x08006e3c   Section      156  dnaninf.o(x$fpl$dnaninf)
    x$fpl$dretinf                            0x08006ed8   Section       12  dretinf.o(x$fpl$dretinf)
    x$fpl$drleqf                             0x08006ee4   Section      108  drleqf.o(x$fpl$drleqf)
    x$fpl$drsb                               0x08006f50   Section       22  daddsub_clz.o(x$fpl$drsb)
    x$fpl$dsqrt                              0x08006f68   Section      460  dsqrt_noumaal.o(x$fpl$dsqrt)
    x$fpl$dsub                               0x08007134   Section      468  daddsub_clz.o(x$fpl$dsub)
    _dsub1                                   0x08007145   Thumb Code     0  daddsub_clz.o(x$fpl$dsub)
    x$fpl$f2d                                0x08007308   Section       86  f2d.o(x$fpl$f2d)
    x$fpl$fnaninf                            0x0800735e   Section      140  fnaninf.o(x$fpl$fnaninf)
    x$fpl$printf1                            0x080073ea   Section        4  printf1.o(x$fpl$printf1)
    x$fpl$retnan                             0x080073ee   Section      100  retnan.o(x$fpl$retnan)
    x$fpl$scalbn                             0x08007452   Section       92  scalbn.o(x$fpl$scalbn)
    x$fpl$trapveneer                         0x080074ae   Section       48  trapv.o(x$fpl$trapveneer)
    .constdata                               0x080074de   Section        7  cjson.o(.constdata)
    x$fpl$usenofp                            0x080074de   Section        0  usenofp.o(x$fpl$usenofp)
    firstByteMark                            0x080074de   Data           7  cjson.o(.constdata)
    .constdata                               0x080074e8   Section        8  _printf_wctomb.o(.constdata)
    initial_mbstate                          0x080074e8   Data           8  _printf_wctomb.o(.constdata)
    .constdata                               0x080074f0   Section       40  _printf_hex_int.o(.constdata)
    uc_hextab                                0x080074f0   Data          20  _printf_hex_int.o(.constdata)
    lc_hextab                                0x08007504   Data          20  _printf_hex_int.o(.constdata)
    .constdata                               0x08007518   Section       17  __printf_flags_ss_wp.o(.constdata)
    maptable                                 0x08007518   Data          17  __printf_flags_ss_wp.o(.constdata)
    .constdata                               0x08007530   Section      136  pow.o(.constdata)
    bp                                       0x08007530   Data          16  pow.o(.constdata)
    dp_h                                     0x08007540   Data          16  pow.o(.constdata)
    dp_l                                     0x08007550   Data          16  pow.o(.constdata)
    L                                        0x08007560   Data          48  pow.o(.constdata)
    P                                        0x08007590   Data          40  pow.o(.constdata)
    .constdata                               0x080075b8   Section        8  qnan.o(.constdata)
    .constdata                               0x080075c0   Section      148  bigflt0.o(.constdata)
    tenpwrs_x                                0x080075c0   Data          60  bigflt0.o(.constdata)
    tenpwrs_i                                0x080075fc   Data          64  bigflt0.o(.constdata)
    .conststring                             0x08007654   Section      157  ec800.o(.conststring)
    locale$$data                             0x08007714   Section      272  lc_ctype_c.o(locale$$data)
    __lcctype_c_name                         0x08007718   Data           2  lc_ctype_c.o(locale$$data)
    __lcctype_c_start                        0x08007720   Data           0  lc_ctype_c.o(locale$$data)
    locale$$data                             0x08007824   Section       28  lc_numeric_c.o(locale$$data)
    __lcctype_c_end                          0x08007824   Data           0  lc_ctype_c.o(locale$$data)
    __lcnum_c_name                           0x08007828   Data           2  lc_numeric_c.o(locale$$data)
    __lcnum_c_start                          0x08007830   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_point                          0x0800783c   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_thousands                      0x0800783e   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_grouping                       0x0800783f   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_end                            0x08007840   Data           0  lc_numeric_c.o(locale$$data)
    .data                                    0x20000000   Section       20  system_stm32f10x.o(.data)
    .data                                    0x20000014   Section       16  ec800.o(.data)
    .data                                    0x20000024   Section       12  cjson.o(.data)
    ep                                       0x20000024   Data           4  cjson.o(.data)
    cJSON_malloc                             0x20000028   Data           4  cjson.o(.data)
    cJSON_free                               0x2000002c   Data           4  cjson.o(.data)
    .data                                    0x20000030   Section        1  aht20.o(.data)
    .data                                    0x20000032   Section        2  bsp_i2c.o(.data)
    RETRY_IN_MLSEC                           0x20000032   Data           2  bsp_i2c.o(.data)
    .data                                    0x20000034   Section       20  stm32f10x_rcc.o(.data)
    APBAHBPrescTable                         0x20000034   Data          16  stm32f10x_rcc.o(.data)
    ADCPrescTable                            0x20000044   Data           4  stm32f10x_rcc.o(.data)
    .data                                    0x20000048   Section        8  usart.o(.data)
    .data                                    0x20000050   Section        4  delay.o(.data)
    fac_us                                   0x20000050   Data           1  delay.o(.data)
    fac_ms                                   0x20000052   Data           2  delay.o(.data)
    .bss                                     0x20000054   Section      250  usart.o(.bss)
    .bss                                     0x20000150   Section       96  libspace.o(.bss)
    HEAP                                     0x200001b0   Section      512  startup_stm32f10x_hd.o(HEAP)
    Heap_Mem                                 0x200001b0   Data         512  startup_stm32f10x_hd.o(HEAP)
    STACK                                    0x200003b0   Section     1024  startup_stm32f10x_hd.o(STACK)
    Stack_Mem                                0x200003b0   Data        1024  startup_stm32f10x_hd.o(STACK)
    __initial_sp                             0x200007b0   Data           0  startup_stm32f10x_hd.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$P$D$K$B$S$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEX$EBA8$UX$STANDARDLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    _printf_flags                            0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  printf_stubs.o ABSOLUTE
    __ARM_exceptions_init                     - Undefined Weak Reference
    __alloca_initialize                       - Undefined Weak Reference
    __arm_fini_                               - Undefined Weak Reference
    __arm_preinit_                            - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __sigvec_lookup                           - Undefined Weak Reference
    __user_heap_extent                        - Undefined Weak Reference
    _atexit_init                              - Undefined Weak Reference
    _call_atexit_fns                          - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _fp_trap_init                             - Undefined Weak Reference
    _fp_trap_shutdown                         - Undefined Weak Reference
    _get_lc_collate                           - Undefined Weak Reference
    _get_lc_monetary                          - Undefined Weak Reference
    _get_lc_time                              - Undefined Weak Reference
    _getenv_init                              - Undefined Weak Reference
    _handle_redirection                       - Undefined Weak Reference
    _initio                                   - Undefined Weak Reference
    _mutex_acquire                            - Undefined Weak Reference
    _mutex_free                               - Undefined Weak Reference
    _mutex_release                            - Undefined Weak Reference
    _printf_mbtowc                            - Undefined Weak Reference
    _printf_wc                                - Undefined Weak Reference
    _rand_init                                - Undefined Weak Reference
    _signal_finish                            - Undefined Weak Reference
    _signal_init                              - Undefined Weak Reference
    _terminateio                              - Undefined Weak Reference
    __Vectors_Size                           0x00000130   Number         0  startup_stm32f10x_hd.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f10x_hd.o(RESET)
    __Vectors_End                            0x08000130   Data           0  startup_stm32f10x_hd.o(RESET)
    __main                                   0x08000131   Thumb Code     8  __main.o(!!!main)
    __scatterload                            0x08000139   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_rt2                        0x08000139   Thumb Code    44  __scatter.o(!!!scatter)
    __scatterload_rt2_thumb_only             0x08000139   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_null                       0x08000147   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_copy                       0x0800016d   Thumb Code    26  __scatter_copy.o(!!handler_copy)
    __scatterload_zeroinit                   0x08000189   Thumb Code    28  __scatter_zi.o(!!handler_zi)
    _printf_f                                0x080001a5   Thumb Code     0  _printf_f.o(.ARM.Collect$$_printf_percent$$00000003)
    _printf_percent                          0x080001a5   Thumb Code     0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    _printf_e                                0x080001ab   Thumb Code     0  _printf_e.o(.ARM.Collect$$_printf_percent$$00000004)
    _printf_i                                0x080001b1   Thumb Code     0  _printf_i.o(.ARM.Collect$$_printf_percent$$00000008)
    _printf_d                                0x080001b7   Thumb Code     0  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    _printf_u                                0x080001bd   Thumb Code     0  _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A)
    _printf_o                                0x080001c3   Thumb Code     0  _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B)
    _printf_x                                0x080001c9   Thumb Code     0  _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C)
    _printf_l                                0x080001cf   Thumb Code     0  _printf_l.o(.ARM.Collect$$_printf_percent$$00000012)
    _printf_c                                0x080001d9   Thumb Code     0  _printf_c.o(.ARM.Collect$$_printf_percent$$00000013)
    _printf_s                                0x080001df   Thumb Code     0  _printf_s.o(.ARM.Collect$$_printf_percent$$00000014)
    _printf_lc                               0x080001e5   Thumb Code     0  _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015)
    _printf_percent_end                      0x080001eb   Thumb Code     0  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    __rt_lib_init                            0x080001ef   Thumb Code     0  libinit.o(.ARM.Collect$$libinit$$00000000)
    __rt_lib_init_fp_1                       0x080001f1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000002)
    __rt_lib_init_heap_2                     0x080001f1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000005)
    __rt_lib_init_preinit_1                  0x080001f1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    __rt_lib_init_heap_1                     0x080001f9   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    __rt_lib_init_lc_common                  0x080001f9   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000F)
    __rt_lib_init_rand_1                     0x080001f9   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    __rt_lib_init_user_alloc_1               0x080001f9   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    __rt_lib_init_lc_collate_1               0x080001ff   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    __rt_lib_init_lc_ctype_2                 0x080001ff   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000012)
    __rt_lib_init_lc_ctype_1                 0x0800020b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    __rt_lib_init_lc_monetary_1              0x0800020b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    __rt_lib_init_lc_numeric_2               0x0800020b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000016)
    __rt_lib_init_alloca_1                   0x08000215   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    __rt_lib_init_argv_1                     0x08000215   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    __rt_lib_init_atexit_1                   0x08000215   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    __rt_lib_init_clock_1                    0x08000215   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    __rt_lib_init_cpp_1                      0x08000215   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    __rt_lib_init_exceptions_1               0x08000215   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    __rt_lib_init_fp_trap_1                  0x08000215   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    __rt_lib_init_getenv_1                   0x08000215   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    __rt_lib_init_lc_numeric_1               0x08000215   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    __rt_lib_init_lc_time_1                  0x08000215   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    __rt_lib_init_return                     0x08000215   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000033)
    __rt_lib_init_signal_1                   0x08000215   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    __rt_lib_init_stdio_1                    0x08000215   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    __rt_lib_shutdown                        0x08000217   Thumb Code     0  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    __rt_lib_shutdown_cpp_1                  0x08000219   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    __rt_lib_shutdown_fini_1                 0x08000219   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    __rt_lib_shutdown_fp_trap_1              0x08000219   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000009)
    __rt_lib_shutdown_heap_1                 0x08000219   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000011)
    __rt_lib_shutdown_return                 0x08000219   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000012)
    __rt_lib_shutdown_signal_1               0x08000219   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    __rt_lib_shutdown_stdio_1                0x08000219   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000006)
    __rt_lib_shutdown_user_alloc_1           0x08000219   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E)
    __rt_entry                               0x0800021b   Thumb Code     0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    __rt_entry_presh_1                       0x0800021b   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    __rt_entry_sh                            0x0800021b   Thumb Code     0  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    __rt_entry_li                            0x08000221   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    __rt_entry_postsh_1                      0x08000221   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    __rt_entry_main                          0x08000225   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    __rt_entry_postli_1                      0x08000225   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    __rt_exit                                0x0800022d   Thumb Code     0  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    __rt_exit_ls                             0x0800022f   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    __rt_exit_prels_1                        0x0800022f   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    __rt_exit_exit                           0x08000233   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    _maybe_terminate_alloc                   0x08000239   Thumb Code     0  maybetermalloc1.o(.emb_text)
    main                                     0x08000239   Thumb Code   240  main.o(.text)
    NMI_Handler                              0x08000355   Thumb Code     2  stm32f10x_it.o(.text)
    HardFault_Handler                        0x08000357   Thumb Code     4  stm32f10x_it.o(.text)
    MemManage_Handler                        0x0800035b   Thumb Code     4  stm32f10x_it.o(.text)
    BusFault_Handler                         0x0800035f   Thumb Code     4  stm32f10x_it.o(.text)
    UsageFault_Handler                       0x08000363   Thumb Code     4  stm32f10x_it.o(.text)
    SVC_Handler                              0x08000367   Thumb Code     2  stm32f10x_it.o(.text)
    DebugMon_Handler                         0x08000369   Thumb Code     2  stm32f10x_it.o(.text)
    PendSV_Handler                           0x0800036b   Thumb Code     2  stm32f10x_it.o(.text)
    SysTick_Handler                          0x0800036d   Thumb Code     2  stm32f10x_it.o(.text)
    RCC_Configuration                        0x08000371   Thumb Code   110  system_stm32f10x.o(.text)
    SystemInit                               0x080004bd   Thumb Code    78  system_stm32f10x.o(.text)
    SystemCoreClockUpdate                    0x0800050b   Thumb Code   142  system_stm32f10x.o(.text)
    LED_Init                                 0x080005bd   Thumb Code    90  led.o(.text)
    IWDG_Init                                0x08000625   Thumb Code    36  wdg.o(.text)
    IWDG_Feed                                0x08000649   Thumb Code     8  wdg.o(.text)
    Uart1_SendStr                            0x08000651   Thumb Code    34  ec800.o(.text)
    Clear_Buffer                             0x08000673   Thumb Code    42  ec800.o(.text)
    EC800_Init                               0x0800069d   Thumb Code   442  ec800.o(.text)
    MQTT_Init                                0x08000857   Thumb Code   128  ec800.o(.text)
    Mqttaliyun_Savedata                      0x080008d7   Thumb Code    56  ec800.o(.text)
    aliyunMQTT_PUBdata                       0x0800090f   Thumb Code    98  ec800.o(.text)
    CParsejson                               0x08000971   Thumb Code   152  ec800.o(.text)
    EC800_RECData                            0x08000a09   Thumb Code   668  ec800.o(.text)
    cJSON_GetErrorPtr                        0x08000ca9   Thumb Code     6  cjson.o(.text)
    cJSON_InitHooks                          0x08000d23   Thumb Code    48  cjson.o(.text)
    cJSON_Delete                             0x08000d6d   Thumb Code    82  cjson.o(.text)
    cJSON_ParseWithOpts                      0x080018d9   Thumb Code   152  cjson.o(.text)
    cJSON_Parse                              0x08001971   Thumb Code    16  cjson.o(.text)
    cJSON_Print                              0x08002101   Thumb Code    18  cjson.o(.text)
    cJSON_PrintUnformatted                   0x08002113   Thumb Code    18  cjson.o(.text)
    cJSON_PrintBuffered                      0x08002125   Thumb Code    52  cjson.o(.text)
    cJSON_GetArraySize                       0x08002159   Thumb Code    18  cjson.o(.text)
    cJSON_GetArrayItem                       0x0800216b   Thumb Code    18  cjson.o(.text)
    cJSON_GetObjectItem                      0x0800217d   Thumb Code    30  cjson.o(.text)
    cJSON_AddItemToArray                     0x080021d1   Thumb Code    42  cjson.o(.text)
    cJSON_AddItemToObject                    0x080021fb   Thumb Code    44  cjson.o(.text)
    cJSON_AddItemToObjectCS                  0x08002227   Thumb Code    54  cjson.o(.text)
    cJSON_AddItemReferenceToArray            0x0800225d   Thumb Code    24  cjson.o(.text)
    cJSON_AddItemReferenceToObject           0x08002275   Thumb Code    32  cjson.o(.text)
    cJSON_DetachItemFromArray                0x08002295   Thumb Code    66  cjson.o(.text)
    cJSON_DeleteItemFromArray                0x080022d7   Thumb Code    22  cjson.o(.text)
    cJSON_DetachItemFromObject               0x080022ed   Thumb Code    50  cjson.o(.text)
    cJSON_DeleteItemFromObject               0x0800231f   Thumb Code    22  cjson.o(.text)
    cJSON_InsertItemInArray                  0x08002335   Thumb Code    64  cjson.o(.text)
    cJSON_ReplaceItemInArray                 0x08002375   Thumb Code    76  cjson.o(.text)
    cJSON_ReplaceItemInObject                0x080023c1   Thumb Code    62  cjson.o(.text)
    cJSON_CreateNull                         0x080023ff   Thumb Code    18  cjson.o(.text)
    cJSON_CreateTrue                         0x08002411   Thumb Code    18  cjson.o(.text)
    cJSON_CreateFalse                        0x08002423   Thumb Code    18  cjson.o(.text)
    cJSON_CreateBool                         0x08002435   Thumb Code    26  cjson.o(.text)
    cJSON_CreateNumber                       0x0800244f   Thumb Code    36  cjson.o(.text)
    cJSON_CreateString                       0x08002473   Thumb Code    28  cjson.o(.text)
    cJSON_CreateArray                        0x0800248f   Thumb Code    18  cjson.o(.text)
    cJSON_CreateObject                       0x080024a1   Thumb Code    18  cjson.o(.text)
    cJSON_CreateIntArray                     0x080024b3   Thumb Code    68  cjson.o(.text)
    cJSON_CreateFloatArray                   0x080024f7   Thumb Code    68  cjson.o(.text)
    cJSON_CreateDoubleArray                  0x0800253b   Thumb Code    68  cjson.o(.text)
    cJSON_CreateStringArray                  0x0800257f   Thumb Code    62  cjson.o(.text)
    cJSON_Duplicate                          0x080025bd   Thumb Code   178  cjson.o(.text)
    cJSON_Minify                             0x0800266f   Thumb Code   186  cjson.o(.text)
    AHT20_Read_Status                        0x08002729   Thumb Code    20  aht20.o(.text)
    AHT20_Read_Cal_Enable                    0x0800273d   Thumb Code    26  aht20.o(.text)
    AHT20_Read_CTdata                        0x08002757   Thumb Code   150  aht20.o(.text)
    AHT20_Init                               0x080027ed   Thumb Code   120  aht20.o(.text)
    Set_I2C_Retry                            0x08002873   Thumb Code     6  bsp_i2c.o(.text)
    Get_I2C_Retry                            0x08002879   Thumb Code     6  bsp_i2c.o(.text)
    I2C_Bus_Init                             0x080028bb   Thumb Code    14  bsp_i2c.o(.text)
    Soft_I2C_Wait_Ack                        0x080029ad   Thumb Code    74  bsp_i2c.o(.text)
    Sensors_I2C_WriteRegister                0x08002bcb   Thumb Code    76  bsp_i2c.o(.text)
    Sensors_I2C_ReadRegister                 0x08002c17   Thumb Code    90  bsp_i2c.o(.text)
    GPIO_DeInit                              0x08002c71   Thumb Code   172  stm32f10x_gpio.o(.text)
    GPIO_AFIODeInit                          0x08002d1d   Thumb Code    20  stm32f10x_gpio.o(.text)
    GPIO_Init                                0x08002d31   Thumb Code   278  stm32f10x_gpio.o(.text)
    GPIO_StructInit                          0x08002e47   Thumb Code    16  stm32f10x_gpio.o(.text)
    GPIO_ReadInputDataBit                    0x08002e57   Thumb Code    18  stm32f10x_gpio.o(.text)
    GPIO_ReadInputData                       0x08002e69   Thumb Code     8  stm32f10x_gpio.o(.text)
    GPIO_ReadOutputDataBit                   0x08002e71   Thumb Code    18  stm32f10x_gpio.o(.text)
    GPIO_ReadOutputData                      0x08002e83   Thumb Code     8  stm32f10x_gpio.o(.text)
    GPIO_SetBits                             0x08002e8b   Thumb Code     4  stm32f10x_gpio.o(.text)
    GPIO_ResetBits                           0x08002e8f   Thumb Code     4  stm32f10x_gpio.o(.text)
    GPIO_WriteBit                            0x08002e93   Thumb Code    10  stm32f10x_gpio.o(.text)
    GPIO_Write                               0x08002e9d   Thumb Code     4  stm32f10x_gpio.o(.text)
    GPIO_PinLockConfig                       0x08002ea1   Thumb Code    18  stm32f10x_gpio.o(.text)
    GPIO_EventOutputConfig                   0x08002eb3   Thumb Code    26  stm32f10x_gpio.o(.text)
    GPIO_EventOutputCmd                      0x08002ecd   Thumb Code     6  stm32f10x_gpio.o(.text)
    GPIO_PinRemapConfig                      0x08002ed3   Thumb Code   138  stm32f10x_gpio.o(.text)
    GPIO_EXTILineConfig                      0x08002f5d   Thumb Code    66  stm32f10x_gpio.o(.text)
    GPIO_ETH_MediaInterfaceConfig            0x08002f9f   Thumb Code     8  stm32f10x_gpio.o(.text)
    IWDG_WriteAccessCmd                      0x08002fcd   Thumb Code     6  stm32f10x_iwdg.o(.text)
    IWDG_SetPrescaler                        0x08002fd3   Thumb Code     6  stm32f10x_iwdg.o(.text)
    IWDG_SetReload                           0x08002fd9   Thumb Code     6  stm32f10x_iwdg.o(.text)
    IWDG_ReloadCounter                       0x08002fdf   Thumb Code    10  stm32f10x_iwdg.o(.text)
    IWDG_Enable                              0x08002fe9   Thumb Code    10  stm32f10x_iwdg.o(.text)
    IWDG_GetFlagStatus                       0x08002ff3   Thumb Code    20  stm32f10x_iwdg.o(.text)
    USART_DeInit                             0x0800300d   Thumb Code   134  stm32f10x_usart.o(.text)
    USART_Init                               0x08003093   Thumb Code   210  stm32f10x_usart.o(.text)
    USART_StructInit                         0x08003165   Thumb Code    24  stm32f10x_usart.o(.text)
    USART_ClockInit                          0x0800317d   Thumb Code    34  stm32f10x_usart.o(.text)
    USART_ClockStructInit                    0x0800319f   Thumb Code    12  stm32f10x_usart.o(.text)
    USART_Cmd                                0x080031ab   Thumb Code    24  stm32f10x_usart.o(.text)
    USART_ITConfig                           0x080031c3   Thumb Code    74  stm32f10x_usart.o(.text)
    USART_DMACmd                             0x0800320d   Thumb Code    18  stm32f10x_usart.o(.text)
    USART_SetAddress                         0x0800321f   Thumb Code    18  stm32f10x_usart.o(.text)
    USART_WakeUpConfig                       0x08003231   Thumb Code    18  stm32f10x_usart.o(.text)
    USART_ReceiverWakeUpCmd                  0x08003243   Thumb Code    24  stm32f10x_usart.o(.text)
    USART_LINBreakDetectLengthConfig         0x0800325b   Thumb Code    18  stm32f10x_usart.o(.text)
    USART_LINCmd                             0x0800326d   Thumb Code    24  stm32f10x_usart.o(.text)
    USART_SendData                           0x08003285   Thumb Code     8  stm32f10x_usart.o(.text)
    USART_ReceiveData                        0x0800328d   Thumb Code    10  stm32f10x_usart.o(.text)
    USART_SendBreak                          0x08003297   Thumb Code    10  stm32f10x_usart.o(.text)
    USART_SetGuardTime                       0x080032a1   Thumb Code    16  stm32f10x_usart.o(.text)
    USART_SetPrescaler                       0x080032b1   Thumb Code    16  stm32f10x_usart.o(.text)
    USART_SmartCardCmd                       0x080032c1   Thumb Code    24  stm32f10x_usart.o(.text)
    USART_SmartCardNACKCmd                   0x080032d9   Thumb Code    24  stm32f10x_usart.o(.text)
    USART_HalfDuplexCmd                      0x080032f1   Thumb Code    24  stm32f10x_usart.o(.text)
    USART_OverSampling8Cmd                   0x08003309   Thumb Code    22  stm32f10x_usart.o(.text)
    USART_OneBitMethodCmd                    0x0800331f   Thumb Code    24  stm32f10x_usart.o(.text)
    USART_IrDAConfig                         0x08003337   Thumb Code    18  stm32f10x_usart.o(.text)
    USART_IrDACmd                            0x08003349   Thumb Code    24  stm32f10x_usart.o(.text)
    USART_GetFlagStatus                      0x08003361   Thumb Code    26  stm32f10x_usart.o(.text)
    USART_ClearFlag                          0x0800337b   Thumb Code    18  stm32f10x_usart.o(.text)
    USART_GetITStatus                        0x0800338d   Thumb Code    84  stm32f10x_usart.o(.text)
    USART_ClearITPendingBit                  0x080033e1   Thumb Code    52  stm32f10x_usart.o(.text)
    RCC_DeInit                               0x08003415   Thumb Code    64  stm32f10x_rcc.o(.text)
    RCC_HSEConfig                            0x08003455   Thumb Code    70  stm32f10x_rcc.o(.text)
    RCC_GetFlagStatus                        0x0800349b   Thumb Code    56  stm32f10x_rcc.o(.text)
    RCC_WaitForHSEStartUp                    0x080034d3   Thumb Code    56  stm32f10x_rcc.o(.text)
    RCC_AdjustHSICalibrationValue            0x0800350b   Thumb Code    20  stm32f10x_rcc.o(.text)
    RCC_HSICmd                               0x0800351f   Thumb Code     6  stm32f10x_rcc.o(.text)
    RCC_PLLConfig                            0x08003525   Thumb Code    24  stm32f10x_rcc.o(.text)
    RCC_PLLCmd                               0x0800353d   Thumb Code     6  stm32f10x_rcc.o(.text)
    RCC_SYSCLKConfig                         0x08003543   Thumb Code    18  stm32f10x_rcc.o(.text)
    RCC_GetSYSCLKSource                      0x08003555   Thumb Code    10  stm32f10x_rcc.o(.text)
    RCC_HCLKConfig                           0x0800355f   Thumb Code    18  stm32f10x_rcc.o(.text)
    RCC_PCLK1Config                          0x08003571   Thumb Code    18  stm32f10x_rcc.o(.text)
    RCC_PCLK2Config                          0x08003583   Thumb Code    20  stm32f10x_rcc.o(.text)
    RCC_ITConfig                             0x08003597   Thumb Code    26  stm32f10x_rcc.o(.text)
    RCC_USBCLKConfig                         0x080035b1   Thumb Code     8  stm32f10x_rcc.o(.text)
    RCC_ADCCLKConfig                         0x080035b9   Thumb Code    18  stm32f10x_rcc.o(.text)
    RCC_LSEConfig                            0x080035cb   Thumb Code    50  stm32f10x_rcc.o(.text)
    RCC_LSICmd                               0x080035fd   Thumb Code     6  stm32f10x_rcc.o(.text)
    RCC_RTCCLKConfig                         0x08003603   Thumb Code    12  stm32f10x_rcc.o(.text)
    RCC_RTCCLKCmd                            0x0800360f   Thumb Code     8  stm32f10x_rcc.o(.text)
    RCC_GetClocksFreq                        0x08003617   Thumb Code   192  stm32f10x_rcc.o(.text)
    RCC_AHBPeriphClockCmd                    0x080036d7   Thumb Code    26  stm32f10x_rcc.o(.text)
    RCC_APB2PeriphClockCmd                   0x080036f1   Thumb Code    26  stm32f10x_rcc.o(.text)
    RCC_APB1PeriphClockCmd                   0x0800370b   Thumb Code    26  stm32f10x_rcc.o(.text)
    RCC_APB2PeriphResetCmd                   0x08003725   Thumb Code    26  stm32f10x_rcc.o(.text)
    RCC_APB1PeriphResetCmd                   0x0800373f   Thumb Code    26  stm32f10x_rcc.o(.text)
    RCC_BackupResetCmd                       0x08003759   Thumb Code     8  stm32f10x_rcc.o(.text)
    RCC_ClockSecuritySystemCmd               0x08003761   Thumb Code     6  stm32f10x_rcc.o(.text)
    RCC_MCOConfig                            0x08003767   Thumb Code     6  stm32f10x_rcc.o(.text)
    RCC_ClearFlag                            0x0800376d   Thumb Code    14  stm32f10x_rcc.o(.text)
    RCC_GetITStatus                          0x0800377b   Thumb Code    20  stm32f10x_rcc.o(.text)
    RCC_ClearITPendingBit                    0x0800378f   Thumb Code     6  stm32f10x_rcc.o(.text)
    FLASH_SetLatency                         0x080037b9   Thumb Code    18  stm32f10x_flash.o(.text)
    FLASH_HalfCycleAccessCmd                 0x080037cb   Thumb Code    22  stm32f10x_flash.o(.text)
    FLASH_PrefetchBufferCmd                  0x080037e1   Thumb Code    22  stm32f10x_flash.o(.text)
    FLASH_Unlock                             0x080037f7   Thumb Code    12  stm32f10x_flash.o(.text)
    FLASH_UnlockBank1                        0x08003803   Thumb Code    12  stm32f10x_flash.o(.text)
    FLASH_Lock                               0x0800380f   Thumb Code    14  stm32f10x_flash.o(.text)
    FLASH_LockBank1                          0x0800381d   Thumb Code    14  stm32f10x_flash.o(.text)
    FLASH_GetBank1Status                     0x0800382b   Thumb Code    48  stm32f10x_flash.o(.text)
    FLASH_WaitForLastOperation               0x0800385b   Thumb Code    38  stm32f10x_flash.o(.text)
    FLASH_ErasePage                          0x08003881   Thumb Code    72  stm32f10x_flash.o(.text)
    FLASH_EraseAllPages                      0x080038c9   Thumb Code    68  stm32f10x_flash.o(.text)
    FLASH_WaitForLastBank1Operation          0x0800390d   Thumb Code    38  stm32f10x_flash.o(.text)
    FLASH_EraseAllBank1Pages                 0x08003933   Thumb Code    68  stm32f10x_flash.o(.text)
    FLASH_GetReadOutProtectionStatus         0x08003977   Thumb Code    20  stm32f10x_flash.o(.text)
    FLASH_EraseOptionBytes                   0x0800398b   Thumb Code   150  stm32f10x_flash.o(.text)
    FLASH_ProgramWord                        0x08003a21   Thumb Code   102  stm32f10x_flash.o(.text)
    FLASH_ProgramHalfWord                    0x08003a87   Thumb Code    60  stm32f10x_flash.o(.text)
    FLASH_ProgramOptionByteData              0x08003ac3   Thumb Code    72  stm32f10x_flash.o(.text)
    FLASH_EnableWriteProtection              0x08003b0b   Thumb Code   200  stm32f10x_flash.o(.text)
    FLASH_ReadOutProtection                  0x08003bd3   Thumb Code   156  stm32f10x_flash.o(.text)
    FLASH_UserOptionByteConfig               0x08003c6f   Thumb Code    88  stm32f10x_flash.o(.text)
    FLASH_GetUserOptionByte                  0x08003cc7   Thumb Code     8  stm32f10x_flash.o(.text)
    FLASH_GetWriteProtectionOptionByte       0x08003ccf   Thumb Code     6  stm32f10x_flash.o(.text)
    FLASH_GetPrefetchBufferStatus            0x08003cd5   Thumb Code    20  stm32f10x_flash.o(.text)
    FLASH_ITConfig                           0x08003ce9   Thumb Code    26  stm32f10x_flash.o(.text)
    FLASH_GetFlagStatus                      0x08003d03   Thumb Code    42  stm32f10x_flash.o(.text)
    FLASH_ClearFlag                          0x08003d2d   Thumb Code     6  stm32f10x_flash.o(.text)
    FLASH_GetStatus                          0x08003d33   Thumb Code    48  stm32f10x_flash.o(.text)
    NVIC_PriorityGroupConfig                 0x08003d75   Thumb Code    10  misc.o(.text)
    NVIC_Init                                0x08003d7f   Thumb Code   100  misc.o(.text)
    NVIC_SetVectorTable                      0x08003de3   Thumb Code    14  misc.o(.text)
    NVIC_SystemLPConfig                      0x08003df1   Thumb Code    34  misc.o(.text)
    SysTick_CLKSourceConfig                  0x08003e13   Thumb Code    40  misc.o(.text)
    Reset_Handler                            0x08003e51   Thumb Code     8  startup_stm32f10x_hd.o(.text)
    ADC1_2_IRQHandler                        0x08003e6b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    ADC3_IRQHandler                          0x08003e6b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    CAN1_RX1_IRQHandler                      0x08003e6b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    CAN1_SCE_IRQHandler                      0x08003e6b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel1_IRQHandler                 0x08003e6b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel2_IRQHandler                 0x08003e6b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel3_IRQHandler                 0x08003e6b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel4_IRQHandler                 0x08003e6b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel5_IRQHandler                 0x08003e6b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel6_IRQHandler                 0x08003e6b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel7_IRQHandler                 0x08003e6b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel1_IRQHandler                 0x08003e6b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel2_IRQHandler                 0x08003e6b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel3_IRQHandler                 0x08003e6b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel4_5_IRQHandler               0x08003e6b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI0_IRQHandler                         0x08003e6b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI15_10_IRQHandler                     0x08003e6b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI1_IRQHandler                         0x08003e6b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI2_IRQHandler                         0x08003e6b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI3_IRQHandler                         0x08003e6b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI4_IRQHandler                         0x08003e6b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI9_5_IRQHandler                       0x08003e6b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    FLASH_IRQHandler                         0x08003e6b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    FSMC_IRQHandler                          0x08003e6b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C1_ER_IRQHandler                       0x08003e6b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C1_EV_IRQHandler                       0x08003e6b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C2_ER_IRQHandler                       0x08003e6b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C2_EV_IRQHandler                       0x08003e6b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    PVD_IRQHandler                           0x08003e6b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    RCC_IRQHandler                           0x08003e6b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    RTCAlarm_IRQHandler                      0x08003e6b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    RTC_IRQHandler                           0x08003e6b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SDIO_IRQHandler                          0x08003e6b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SPI1_IRQHandler                          0x08003e6b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SPI2_IRQHandler                          0x08003e6b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SPI3_IRQHandler                          0x08003e6b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TAMPER_IRQHandler                        0x08003e6b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_BRK_IRQHandler                      0x08003e6b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_CC_IRQHandler                       0x08003e6b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_TRG_COM_IRQHandler                  0x08003e6b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_UP_IRQHandler                       0x08003e6b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM2_IRQHandler                          0x08003e6b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM3_IRQHandler                          0x08003e6b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM4_IRQHandler                          0x08003e6b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM5_IRQHandler                          0x08003e6b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM6_IRQHandler                          0x08003e6b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM7_IRQHandler                          0x08003e6b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_BRK_IRQHandler                      0x08003e6b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_CC_IRQHandler                       0x08003e6b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_TRG_COM_IRQHandler                  0x08003e6b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_UP_IRQHandler                       0x08003e6b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    UART4_IRQHandler                         0x08003e6b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    UART5_IRQHandler                         0x08003e6b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USART3_IRQHandler                        0x08003e6b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USBWakeUp_IRQHandler                     0x08003e6b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USB_HP_CAN1_TX_IRQHandler                0x08003e6b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USB_LP_CAN1_RX0_IRQHandler               0x08003e6b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    WWDG_IRQHandler                          0x08003e6b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    __user_initial_stackheap                 0x08003e6d   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    _sys_exit                                0x08003e91   Thumb Code     4  usart.o(.text)
    _ttywrch                                 0x08003e95   Thumb Code     4  usart.o(.text)
    fputc                                    0x08003e99   Thumb Code    24  usart.o(.text)
    uart_init                                0x08003eb1   Thumb Code   158  usart.o(.text)
    uart2_init                               0x08003f4f   Thumb Code   160  usart.o(.text)
    USART1_IRQHandler                        0x08003fef   Thumb Code    24  usart.o(.text)
    USART2_IRQHandler                        0x08004007   Thumb Code    40  usart.o(.text)
    delay_init                               0x08004045   Thumb Code    50  delay.o(.text)
    delay_us                                 0x08004077   Thumb Code    72  delay.o(.text)
    delay_ms                                 0x080040bf   Thumb Code    72  delay.o(.text)
    NVIC_Configuration                       0x08004119   Thumb Code    12  sys.o(.text)
    __use_no_semihosting                     0x08004125   Thumb Code     2  use_no_semi_2.o(.text)
    malloc                                   0x08004127   Thumb Code    94  h1_alloc.o(.text)
    free                                     0x08004185   Thumb Code    78  h1_free.o(.text)
    tolower                                  0x080041d3   Thumb Code    26  tolower.o(.text)
    __2printf                                0x080041ed   Thumb Code    20  noretval__2printf.o(.text)
    __2sprintf                               0x08004205   Thumb Code    34  noretval__2sprintf.o(.text)
    _printf_pre_padding                      0x0800422d   Thumb Code    44  _printf_pad.o(.text)
    _printf_post_padding                     0x08004259   Thumb Code    34  _printf_pad.o(.text)
    _printf_truncate_signed                  0x0800427b   Thumb Code    18  _printf_truncate.o(.text)
    _printf_truncate_unsigned                0x0800428d   Thumb Code    18  _printf_truncate.o(.text)
    _printf_str                              0x0800429f   Thumb Code    82  _printf_str.o(.text)
    _printf_int_dec                          0x080042f1   Thumb Code   104  _printf_dec.o(.text)
    _printf_wctomb                           0x08004369   Thumb Code   182  _printf_wctomb.o(.text)
    _printf_int_oct                          0x08004425   Thumb Code    72  _printf_oct_int.o(.text)
    _printf_longlong_oct                     0x08004425   Thumb Code     0  _printf_oct_int.o(.text)
    _printf_int_hex                          0x08004475   Thumb Code    84  _printf_hex_int.o(.text)
    _printf_longlong_hex                     0x08004475   Thumb Code     0  _printf_hex_int.o(.text)
    __printf                                 0x080044cd   Thumb Code   388  __printf_flags_ss_wp.o(.text)
    strchr                                   0x08004655   Thumb Code    20  strchr.o(.text)
    strstr                                   0x08004669   Thumb Code    36  strstr.o(.text)
    strcpy                                   0x0800468d   Thumb Code    72  strcpy.o(.text)
    strlen                                   0x080046d5   Thumb Code    62  strlen.o(.text)
    strncmp                                  0x08004713   Thumb Code   150  strncmp.o(.text)
    __aeabi_memcpy                           0x080047a9   Thumb Code     0  rt_memcpy_v6.o(.text)
    __rt_memcpy                              0x080047a9   Thumb Code   138  rt_memcpy_v6.o(.text)
    _memcpy_lastbytes                        0x0800480f   Thumb Code     0  rt_memcpy_v6.o(.text)
    __aeabi_memcpy4                          0x08004833   Thumb Code     0  rt_memcpy_w.o(.text)
    __aeabi_memcpy8                          0x08004833   Thumb Code     0  rt_memcpy_w.o(.text)
    __rt_memcpy_w                            0x08004833   Thumb Code   100  rt_memcpy_w.o(.text)
    _memcpy_lastbytes_aligned                0x0800487b   Thumb Code     0  rt_memcpy_w.o(.text)
    __aeabi_memclr4                          0x08004897   Thumb Code     0  rt_memclr_w.o(.text)
    __aeabi_memclr8                          0x08004897   Thumb Code     0  rt_memclr_w.o(.text)
    __rt_memclr_w                            0x08004897   Thumb Code    78  rt_memclr_w.o(.text)
    _memset_w                                0x0800489b   Thumb Code     0  rt_memclr_w.o(.text)
    __use_two_region_memory                  0x080048e5   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_escrow                         0x080048e7   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_expand                         0x080048e9   Thumb Code     2  heapauxi.o(.text)
    __I$use$semihosting                      0x080048eb   Thumb Code     0  use_no_semi.o(.text)
    __use_no_semihosting_swi                 0x080048eb   Thumb Code     2  use_no_semi.o(.text)
    __rt_ctype_table                         0x080048ed   Thumb Code    16  rt_ctype_table.o(.text)
    __rt_heap_descriptor                     0x080048fd   Thumb Code     8  rt_heap_descriptor_intlibspace.o(.text)
    __use_no_heap                            0x08004905   Thumb Code     2  hguard.o(.text)
    __heap$guard                             0x08004907   Thumb Code     2  hguard.o(.text)
    _terminate_user_alloc                    0x08004909   Thumb Code     2  init_alloc.o(.text)
    _init_user_alloc                         0x0800490b   Thumb Code     2  init_alloc.o(.text)
    __Heap_Full                              0x0800490d   Thumb Code    34  init_alloc.o(.text)
    __Heap_Broken                            0x0800492f   Thumb Code     6  init_alloc.o(.text)
    _init_alloc                              0x08004935   Thumb Code    94  init_alloc.o(.text)
    __Heap_Initialize                        0x08004993   Thumb Code    10  h1_init.o(.text)
    __Heap_DescSize                          0x0800499d   Thumb Code     4  h1_init.o(.text)
    __read_errno                             0x080049a1   Thumb Code    10  _rserrno.o(.text)
    __set_errno                              0x080049ab   Thumb Code    12  _rserrno.o(.text)
    _printf_int_common                       0x080049b7   Thumb Code   178  _printf_intcommon.o(.text)
    __lib_sel_fp_printf                      0x08004a69   Thumb Code     2  _printf_fp_dec.o(.text)
    _printf_fp_dec_real                      0x08004c1b   Thumb Code   620  _printf_fp_dec.o(.text)
    _printf_char_common                      0x08004e93   Thumb Code    32  _printf_char_common.o(.text)
    _sputc                                   0x08004eb9   Thumb Code    10  _sputc.o(.text)
    _printf_cs_common                        0x08004ec3   Thumb Code    20  _printf_char.o(.text)
    _printf_char                             0x08004ed7   Thumb Code    16  _printf_char.o(.text)
    _printf_string                           0x08004ee7   Thumb Code     8  _printf_char.o(.text)
    _printf_char_file                        0x08004ef1   Thumb Code    32  _printf_char_file.o(.text)
    _printf_lcs_common                       0x08004f15   Thumb Code    20  _printf_wchar.o(.text)
    _printf_wchar                            0x08004f29   Thumb Code    16  _printf_wchar.o(.text)
    _printf_wstring                          0x08004f39   Thumb Code     8  _printf_wchar.o(.text)
    _wcrtomb                                 0x08004f41   Thumb Code    64  _wcrtomb.o(.text)
    __user_libspace                          0x08004f81   Thumb Code     8  libspace.o(.text)
    __user_perproc_libspace                  0x08004f81   Thumb Code     0  libspace.o(.text)
    __user_perthread_libspace                0x08004f81   Thumb Code     0  libspace.o(.text)
    __rt_locale                              0x08004f89   Thumb Code     8  rt_locale_intlibspace.o(.text)
    __aeabi_errno_addr                       0x08004f91   Thumb Code     8  rt_errno_addr_intlibspace.o(.text)
    __errno$intlibspace                      0x08004f91   Thumb Code     0  rt_errno_addr_intlibspace.o(.text)
    __rt_errno_addr$intlibspace              0x08004f91   Thumb Code     0  rt_errno_addr_intlibspace.o(.text)
    __Heap_ProvideMemory                     0x08004f99   Thumb Code    52  h1_extend.o(.text)
    _ll_udiv10                               0x08004fcd   Thumb Code   138  lludiv10.o(.text)
    _printf_fp_infnan                        0x08005059   Thumb Code   112  _printf_fp_infnan.o(.text)
    _btod_etento                             0x080050d9   Thumb Code   224  bigflt0.o(.text)
    ferror                                   0x080051bd   Thumb Code     8  ferror.o(.text)
    __rt_SIGRTMEM                            0x080051c5   Thumb Code    14  defsig_rtmem_outer.o(.text)
    __user_setup_stackheap                   0x080051d3   Thumb Code    74  sys_stackheap_outer.o(.text)
    exit                                     0x0800521d   Thumb Code    18  exit.o(.text)
    __sig_exit                               0x0800522f   Thumb Code    10  defsig_exit.o(.text)
    __rt_SIGRTMEM_inner                      0x08005239   Thumb Code    22  defsig_rtmem_inner.o(.text)
    strcmp                                   0x08005289   Thumb Code   128  strcmpv7m.o(.text)
    __default_signal_display                 0x08005309   Thumb Code    50  defsig_general.o(.text)
    _btod_d2e                                0x0800533b   Thumb Code    62  btod.o(CL$$btod_d2e)
    _d2e_denorm_low                          0x08005379   Thumb Code    70  btod.o(CL$$btod_d2e_denorm_low)
    _d2e_norm_op1                            0x080053bf   Thumb Code    96  btod.o(CL$$btod_d2e_norm_op1)
    __btod_div_common                        0x0800541f   Thumb Code   696  btod.o(CL$$btod_div_common)
    _e2e                                     0x08005757   Thumb Code   220  btod.o(CL$$btod_e2e)
    _btod_ediv                               0x08005833   Thumb Code    42  btod.o(CL$$btod_ediv)
    _btod_emul                               0x0800585d   Thumb Code    42  btod.o(CL$$btod_emul)
    __btod_mult_common                       0x08005887   Thumb Code   580  btod.o(CL$$btod_mult_common)
    __ARM_fpclassify                         0x08005acb   Thumb Code    40  fpclassify.o(i.__ARM_fpclassify)
    __kernel_poly                            0x08005af3   Thumb Code   170  poly.o(i.__kernel_poly)
    __mathlib_dbl_divzero                    0x08005b9d   Thumb Code    12  dunder.o(i.__mathlib_dbl_divzero)
    __mathlib_dbl_infnan2                    0x08005bad   Thumb Code     4  dunder.o(i.__mathlib_dbl_infnan2)
    __mathlib_dbl_invalid                    0x08005bb1   Thumb Code    12  dunder.o(i.__mathlib_dbl_invalid)
    __mathlib_dbl_overflow                   0x08005bbd   Thumb Code    14  dunder.o(i.__mathlib_dbl_overflow)
    __mathlib_dbl_underflow                  0x08005bcd   Thumb Code    10  dunder.o(i.__mathlib_dbl_underflow)
    _is_digit                                0x08005bdd   Thumb Code    14  __printf_wp.o(i._is_digit)
    floor                                    0x08005bed   Thumb Code   204  floor.o(i.floor)
    pow                                      0x08005cc9   Thumb Code  2512  pow.o(i.pow)
    sqrt                                     0x080066a1   Thumb Code    76  sqrt.o(i.sqrt)
    _get_lc_ctype                            0x080066ed   Thumb Code    44  lc_ctype_c.o(locale$$code)
    _get_lc_numeric                          0x08006719   Thumb Code    44  lc_numeric_c.o(locale$$code)
    __aeabi_dadd                             0x08006745   Thumb Code     0  daddsub_clz.o(x$fpl$dadd)
    _dadd                                    0x08006745   Thumb Code   332  daddsub_clz.o(x$fpl$dadd)
    __fpl_dcheck_NaN1                        0x08006895   Thumb Code    10  dcheck1.o(x$fpl$dcheck1)
    __fpl_dcmp_Inf                           0x080068a5   Thumb Code    24  dcmpi.o(x$fpl$dcmpinf)
    __aeabi_ddiv                             0x080068bd   Thumb Code     0  ddiv.o(x$fpl$ddiv)
    _ddiv                                    0x080068bd   Thumb Code   552  ddiv.o(x$fpl$ddiv)
    __aeabi_cdcmpeq                          0x08006b6d   Thumb Code     0  deqf.o(x$fpl$deqf)
    _dcmpeq                                  0x08006b6d   Thumb Code   120  deqf.o(x$fpl$deqf)
    __aeabi_d2iz                             0x08006be5   Thumb Code     0  dfix.o(x$fpl$dfix)
    _dfix                                    0x08006be5   Thumb Code    94  dfix.o(x$fpl$dfix)
    __aeabi_i2d                              0x08006c43   Thumb Code     0  dflt_clz.o(x$fpl$dflt)
    _dflt                                    0x08006c43   Thumb Code    46  dflt_clz.o(x$fpl$dflt)
    __aeabi_cdcmple                          0x08006c71   Thumb Code     0  dleqf.o(x$fpl$dleqf)
    _dcmple                                  0x08006c71   Thumb Code   120  dleqf.o(x$fpl$dleqf)
    __fpl_dcmple_InfNaN                      0x08006cd3   Thumb Code     0  dleqf.o(x$fpl$dleqf)
    __aeabi_dmul                             0x08006ce9   Thumb Code     0  dmul.o(x$fpl$dmul)
    _dmul                                    0x08006ce9   Thumb Code   332  dmul.o(x$fpl$dmul)
    __fpl_dnaninf                            0x08006e3d   Thumb Code   156  dnaninf.o(x$fpl$dnaninf)
    __fpl_dretinf                            0x08006ed9   Thumb Code    12  dretinf.o(x$fpl$dretinf)
    __aeabi_cdrcmple                         0x08006ee5   Thumb Code     0  drleqf.o(x$fpl$drleqf)
    _drcmple                                 0x08006ee5   Thumb Code   108  drleqf.o(x$fpl$drleqf)
    __aeabi_drsub                            0x08006f51   Thumb Code     0  daddsub_clz.o(x$fpl$drsb)
    _drsb                                    0x08006f51   Thumb Code    22  daddsub_clz.o(x$fpl$drsb)
    _dsqrt                                   0x08006f69   Thumb Code   456  dsqrt_noumaal.o(x$fpl$dsqrt)
    __aeabi_dsub                             0x08007135   Thumb Code     0  daddsub_clz.o(x$fpl$dsub)
    _dsub                                    0x08007135   Thumb Code   464  daddsub_clz.o(x$fpl$dsub)
    __aeabi_f2d                              0x08007309   Thumb Code     0  f2d.o(x$fpl$f2d)
    _f2d                                     0x08007309   Thumb Code    86  f2d.o(x$fpl$f2d)
    __fpl_fnaninf                            0x0800735f   Thumb Code   140  fnaninf.o(x$fpl$fnaninf)
    _printf_fp_dec                           0x080073eb   Thumb Code     4  printf1.o(x$fpl$printf1)
    __fpl_return_NaN                         0x080073ef   Thumb Code   100  retnan.o(x$fpl$retnan)
    __ARM_scalbn                             0x08007453   Thumb Code    92  scalbn.o(x$fpl$scalbn)
    __fpl_cmpreturn                          0x080074af   Thumb Code    48  trapv.o(x$fpl$trapveneer)
    __I$use$fp                               0x080074de   Number         0  usenofp.o(x$fpl$usenofp)
    __mathlib_zero                           0x080075b8   Data           8  qnan.o(.constdata)
    Region$$Table$$Base                      0x080076f4   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x08007714   Number         0  anon$$obj.o(Region$$Table)
    __ctype                                  0x08007721   Data           0  lc_ctype_c.o(locale$$data)
    SystemCoreClock                          0x20000000   Data           4  system_stm32f10x.o(.data)
    AHBPrescTable                            0x20000004   Data          16  system_stm32f10x.o(.data)
    strx                                     0x20000014   Data           4  ec800.o(.data)
    extstrx                                  0x20000018   Data           4  ec800.o(.data)
    Readystrx                                0x2000001c   Data           4  ec800.o(.data)
    Errstrx                                  0x20000020   Data           4  ec800.o(.data)
    count                                    0x20000030   Data           1  aht20.o(.data)
    __stdout                                 0x20000048   Data           4  usart.o(.data)
    RxCounter                                0x2000004c   Data           1  usart.o(.data)
    USART_RX_STA                             0x2000004e   Data           2  usart.o(.data)
    RxBuffer                                 0x20000054   Data         250  usart.o(.bss)
    __libspace_start                         0x20000150   Data          96  libspace.o(.bss)
    __temporary_stack_top$libspace           0x200001b0   Data           0  libspace.o(.bss)



==============================================================================

Memory Map of the image

  Image Entry point : 0x08000131

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x00007894, Max: 0x00010000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x00007840, Max: 0x00010000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x00000130   Data   RO          394    RESET               startup_stm32f10x_hd.o
    0x08000130   0x08000130   0x00000008   Code   RO          612  * !!!main             c_w.l(__main.o)
    0x08000138   0x08000138   0x00000034   Code   RO          980    !!!scatter          c_w.l(__scatter.o)
    0x0800016c   0x0800016c   0x0000001a   Code   RO          982    !!handler_copy      c_w.l(__scatter_copy.o)
    0x08000186   0x08000186   0x00000002   PAD
    0x08000188   0x08000188   0x0000001c   Code   RO          984    !!handler_zi        c_w.l(__scatter_zi.o)
    0x080001a4   0x080001a4   0x00000000   Code   RO          590    .ARM.Collect$$_printf_percent$$00000000  c_w.l(_printf_percent.o)
    0x080001a4   0x080001a4   0x00000006   Code   RO          588    .ARM.Collect$$_printf_percent$$00000003  c_w.l(_printf_f.o)
    0x080001aa   0x080001aa   0x00000006   Code   RO          589    .ARM.Collect$$_printf_percent$$00000004  c_w.l(_printf_e.o)
    0x080001b0   0x080001b0   0x00000006   Code   RO          585    .ARM.Collect$$_printf_percent$$00000008  c_w.l(_printf_i.o)
    0x080001b6   0x080001b6   0x00000006   Code   RO          586    .ARM.Collect$$_printf_percent$$00000009  c_w.l(_printf_d.o)
    0x080001bc   0x080001bc   0x00000006   Code   RO          587    .ARM.Collect$$_printf_percent$$0000000A  c_w.l(_printf_u.o)
    0x080001c2   0x080001c2   0x00000006   Code   RO          584    .ARM.Collect$$_printf_percent$$0000000B  c_w.l(_printf_o.o)
    0x080001c8   0x080001c8   0x00000006   Code   RO          583    .ARM.Collect$$_printf_percent$$0000000C  c_w.l(_printf_x.o)
    0x080001ce   0x080001ce   0x0000000a   Code   RO          705    .ARM.Collect$$_printf_percent$$00000012  c_w.l(_printf_l.o)
    0x080001d8   0x080001d8   0x00000006   Code   RO          581    .ARM.Collect$$_printf_percent$$00000013  c_w.l(_printf_c.o)
    0x080001de   0x080001de   0x00000006   Code   RO          582    .ARM.Collect$$_printf_percent$$00000014  c_w.l(_printf_s.o)
    0x080001e4   0x080001e4   0x00000006   Code   RO          591    .ARM.Collect$$_printf_percent$$00000015  c_w.l(_printf_lc.o)
    0x080001ea   0x080001ea   0x00000004   Code   RO          704    .ARM.Collect$$_printf_percent$$00000017  c_w.l(_printf_percent_end.o)
    0x080001ee   0x080001ee   0x00000002   Code   RO          917    .ARM.Collect$$libinit$$00000000  c_w.l(libinit.o)
    0x080001f0   0x080001f0   0x00000000   Code   RO          711    .ARM.Collect$$libinit$$00000002  c_w.l(libinit2.o)
    0x080001f0   0x080001f0   0x00000000   Code   RO          713    .ARM.Collect$$libinit$$00000004  c_w.l(libinit2.o)
    0x080001f0   0x080001f0   0x00000008   Code   RO          714    .ARM.Collect$$libinit$$00000005  c_w.l(libinit2.o)
    0x080001f8   0x080001f8   0x00000000   Code   RO          716    .ARM.Collect$$libinit$$0000000A  c_w.l(libinit2.o)
    0x080001f8   0x080001f8   0x00000000   Code   RO          718    .ARM.Collect$$libinit$$0000000C  c_w.l(libinit2.o)
    0x080001f8   0x080001f8   0x00000000   Code   RO          720    .ARM.Collect$$libinit$$0000000E  c_w.l(libinit2.o)
    0x080001f8   0x080001f8   0x00000006   Code   RO          721    .ARM.Collect$$libinit$$0000000F  c_w.l(libinit2.o)
    0x080001fe   0x080001fe   0x00000000   Code   RO          723    .ARM.Collect$$libinit$$00000011  c_w.l(libinit2.o)
    0x080001fe   0x080001fe   0x0000000c   Code   RO          724    .ARM.Collect$$libinit$$00000012  c_w.l(libinit2.o)
    0x0800020a   0x0800020a   0x00000000   Code   RO          725    .ARM.Collect$$libinit$$00000013  c_w.l(libinit2.o)
    0x0800020a   0x0800020a   0x00000000   Code   RO          727    .ARM.Collect$$libinit$$00000015  c_w.l(libinit2.o)
    0x0800020a   0x0800020a   0x0000000a   Code   RO          728    .ARM.Collect$$libinit$$00000016  c_w.l(libinit2.o)
    0x08000214   0x08000214   0x00000000   Code   RO          729    .ARM.Collect$$libinit$$00000017  c_w.l(libinit2.o)
    0x08000214   0x08000214   0x00000000   Code   RO          731    .ARM.Collect$$libinit$$00000019  c_w.l(libinit2.o)
    0x08000214   0x08000214   0x00000000   Code   RO          733    .ARM.Collect$$libinit$$0000001B  c_w.l(libinit2.o)
    0x08000214   0x08000214   0x00000000   Code   RO          735    .ARM.Collect$$libinit$$0000001D  c_w.l(libinit2.o)
    0x08000214   0x08000214   0x00000000   Code   RO          737    .ARM.Collect$$libinit$$0000001F  c_w.l(libinit2.o)
    0x08000214   0x08000214   0x00000000   Code   RO          739    .ARM.Collect$$libinit$$00000021  c_w.l(libinit2.o)
    0x08000214   0x08000214   0x00000000   Code   RO          741    .ARM.Collect$$libinit$$00000023  c_w.l(libinit2.o)
    0x08000214   0x08000214   0x00000000   Code   RO          743    .ARM.Collect$$libinit$$00000025  c_w.l(libinit2.o)
    0x08000214   0x08000214   0x00000000   Code   RO          747    .ARM.Collect$$libinit$$0000002C  c_w.l(libinit2.o)
    0x08000214   0x08000214   0x00000000   Code   RO          749    .ARM.Collect$$libinit$$0000002E  c_w.l(libinit2.o)
    0x08000214   0x08000214   0x00000000   Code   RO          751    .ARM.Collect$$libinit$$00000030  c_w.l(libinit2.o)
    0x08000214   0x08000214   0x00000000   Code   RO          753    .ARM.Collect$$libinit$$00000032  c_w.l(libinit2.o)
    0x08000214   0x08000214   0x00000002   Code   RO          754    .ARM.Collect$$libinit$$00000033  c_w.l(libinit2.o)
    0x08000216   0x08000216   0x00000002   Code   RO          977    .ARM.Collect$$libshutdown$$00000000  c_w.l(libshutdown.o)
    0x08000218   0x08000218   0x00000000   Code   RO          931    .ARM.Collect$$libshutdown$$00000002  c_w.l(libshutdown2.o)
    0x08000218   0x08000218   0x00000000   Code   RO          933    .ARM.Collect$$libshutdown$$00000004  c_w.l(libshutdown2.o)
    0x08000218   0x08000218   0x00000000   Code   RO          935    .ARM.Collect$$libshutdown$$00000006  c_w.l(libshutdown2.o)
    0x08000218   0x08000218   0x00000000   Code   RO          938    .ARM.Collect$$libshutdown$$00000009  c_w.l(libshutdown2.o)
    0x08000218   0x08000218   0x00000000   Code   RO          941    .ARM.Collect$$libshutdown$$0000000C  c_w.l(libshutdown2.o)
    0x08000218   0x08000218   0x00000000   Code   RO          943    .ARM.Collect$$libshutdown$$0000000E  c_w.l(libshutdown2.o)
    0x08000218   0x08000218   0x00000000   Code   RO          946    .ARM.Collect$$libshutdown$$00000011  c_w.l(libshutdown2.o)
    0x08000218   0x08000218   0x00000002   Code   RO          947    .ARM.Collect$$libshutdown$$00000012  c_w.l(libshutdown2.o)
    0x0800021a   0x0800021a   0x00000000   Code   RO          660    .ARM.Collect$$rtentry$$00000000  c_w.l(__rtentry.o)
    0x0800021a   0x0800021a   0x00000000   Code   RO          803    .ARM.Collect$$rtentry$$00000002  c_w.l(__rtentry2.o)
    0x0800021a   0x0800021a   0x00000006   Code   RO          815    .ARM.Collect$$rtentry$$00000004  c_w.l(__rtentry4.o)
    0x08000220   0x08000220   0x00000000   Code   RO          805    .ARM.Collect$$rtentry$$00000009  c_w.l(__rtentry2.o)
    0x08000220   0x08000220   0x00000004   Code   RO          806    .ARM.Collect$$rtentry$$0000000A  c_w.l(__rtentry2.o)
    0x08000224   0x08000224   0x00000000   Code   RO          808    .ARM.Collect$$rtentry$$0000000C  c_w.l(__rtentry2.o)
    0x08000224   0x08000224   0x00000008   Code   RO          809    .ARM.Collect$$rtentry$$0000000D  c_w.l(__rtentry2.o)
    0x0800022c   0x0800022c   0x00000002   Code   RO          920    .ARM.Collect$$rtexit$$00000000  c_w.l(rtexit.o)
    0x0800022e   0x0800022e   0x00000000   Code   RO          951    .ARM.Collect$$rtexit$$00000002  c_w.l(rtexit2.o)
    0x0800022e   0x0800022e   0x00000004   Code   RO          952    .ARM.Collect$$rtexit$$00000003  c_w.l(rtexit2.o)
    0x08000232   0x08000232   0x00000006   Code   RO          953    .ARM.Collect$$rtexit$$00000004  c_w.l(rtexit2.o)
    0x08000238   0x08000238   0x00000000   Code   RO          829    .emb_text           c_w.l(maybetermalloc1.o)
    0x08000238   0x08000238   0x0000011c   Code   RO            1    .text               main.o
    0x08000354   0x08000354   0x0000001a   Code   RO           94    .text               stm32f10x_it.o
    0x0800036e   0x0800036e   0x00000002   PAD
    0x08000370   0x08000370   0x0000024c   Code   RO          129    .text               system_stm32f10x.o
    0x080005bc   0x080005bc   0x00000068   Code   RO          147    .text               led.o
    0x08000624   0x08000624   0x0000002c   Code   RO          159    .text               wdg.o
    0x08000650   0x08000650   0x00000658   Code   RO          174    .text               ec800.o
    0x08000ca8   0x08000ca8   0x00001a80   Code   RO          213    .text               cjson.o
    0x08002728   0x08002728   0x00000140   Code   RO          272    .text               aht20.o
    0x08002868   0x08002868   0x00000408   Code   RO          290    .text               bsp_i2c.o
    0x08002c70   0x08002c70   0x0000035c   Code   RO          307    .text               stm32f10x_gpio.o
    0x08002fcc   0x08002fcc   0x00000040   Code   RO          319    .text               stm32f10x_iwdg.o
    0x0800300c   0x0800300c   0x00000408   Code   RO          331    .text               stm32f10x_usart.o
    0x08003414   0x08003414   0x000003a4   Code   RO          343    .text               stm32f10x_rcc.o
    0x080037b8   0x080037b8   0x000005bc   Code   RO          357    .text               stm32f10x_flash.o
    0x08003d74   0x08003d74   0x000000dc   Code   RO          369    .text               misc.o
    0x08003e50   0x08003e50   0x00000040   Code   RO          395    .text               startup_stm32f10x_hd.o
    0x08003e90   0x08003e90   0x000001b4   Code   RO          399    .text               usart.o
    0x08004044   0x08004044   0x000000d4   Code   RO          419    .text               delay.o
    0x08004118   0x08004118   0x0000000c   Code   RO          433    .text               sys.o
    0x08004124   0x08004124   0x00000002   Code   RO          447    .text               c_w.l(use_no_semi_2.o)
    0x08004126   0x08004126   0x0000005e   Code   RO          453    .text               c_w.l(h1_alloc.o)
    0x08004184   0x08004184   0x0000004e   Code   RO          455    .text               c_w.l(h1_free.o)
    0x080041d2   0x080041d2   0x0000001a   Code   RO          511    .text               c_w.l(tolower.o)
    0x080041ec   0x080041ec   0x00000018   Code   RO          517    .text               c_w.l(noretval__2printf.o)
    0x08004204   0x08004204   0x00000028   Code   RO          519    .text               c_w.l(noretval__2sprintf.o)
    0x0800422c   0x0800422c   0x0000004e   Code   RO          523    .text               c_w.l(_printf_pad.o)
    0x0800427a   0x0800427a   0x00000024   Code   RO          525    .text               c_w.l(_printf_truncate.o)
    0x0800429e   0x0800429e   0x00000052   Code   RO          527    .text               c_w.l(_printf_str.o)
    0x080042f0   0x080042f0   0x00000078   Code   RO          529    .text               c_w.l(_printf_dec.o)
    0x08004368   0x08004368   0x000000bc   Code   RO          531    .text               c_w.l(_printf_wctomb.o)
    0x08004424   0x08004424   0x00000050   Code   RO          536    .text               c_w.l(_printf_oct_int.o)
    0x08004474   0x08004474   0x00000058   Code   RO          543    .text               c_w.l(_printf_hex_int.o)
    0x080044cc   0x080044cc   0x00000188   Code   RO          578    .text               c_w.l(__printf_flags_ss_wp.o)
    0x08004654   0x08004654   0x00000014   Code   RO          592    .text               c_w.l(strchr.o)
    0x08004668   0x08004668   0x00000024   Code   RO          594    .text               c_w.l(strstr.o)
    0x0800468c   0x0800468c   0x00000048   Code   RO          598    .text               c_w.l(strcpy.o)
    0x080046d4   0x080046d4   0x0000003e   Code   RO          600    .text               c_w.l(strlen.o)
    0x08004712   0x08004712   0x00000096   Code   RO          602    .text               c_w.l(strncmp.o)
    0x080047a8   0x080047a8   0x0000008a   Code   RO          604    .text               c_w.l(rt_memcpy_v6.o)
    0x08004832   0x08004832   0x00000064   Code   RO          606    .text               c_w.l(rt_memcpy_w.o)
    0x08004896   0x08004896   0x0000004e   Code   RO          608    .text               c_w.l(rt_memclr_w.o)
    0x080048e4   0x080048e4   0x00000006   Code   RO          610    .text               c_w.l(heapauxi.o)
    0x080048ea   0x080048ea   0x00000002   Code   RO          656    .text               c_w.l(use_no_semi.o)
    0x080048ec   0x080048ec   0x00000010   Code   RO          661    .text               c_w.l(rt_ctype_table.o)
    0x080048fc   0x080048fc   0x00000008   Code   RO          666    .text               c_w.l(rt_heap_descriptor_intlibspace.o)
    0x08004904   0x08004904   0x00000004   Code   RO          668    .text               c_w.l(hguard.o)
    0x08004908   0x08004908   0x0000008a   Code   RO          670    .text               c_w.l(init_alloc.o)
    0x08004992   0x08004992   0x0000000e   Code   RO          674    .text               c_w.l(h1_init.o)
    0x080049a0   0x080049a0   0x00000016   Code   RO          688    .text               c_w.l(_rserrno.o)
    0x080049b6   0x080049b6   0x000000b2   Code   RO          690    .text               c_w.l(_printf_intcommon.o)
    0x08004a68   0x08004a68   0x0000041e   Code   RO          692    .text               c_w.l(_printf_fp_dec.o)
    0x08004e86   0x08004e86   0x00000002   PAD
    0x08004e88   0x08004e88   0x00000030   Code   RO          694    .text               c_w.l(_printf_char_common.o)
    0x08004eb8   0x08004eb8   0x0000000a   Code   RO          696    .text               c_w.l(_sputc.o)
    0x08004ec2   0x08004ec2   0x0000002c   Code   RO          698    .text               c_w.l(_printf_char.o)
    0x08004eee   0x08004eee   0x00000002   PAD
    0x08004ef0   0x08004ef0   0x00000024   Code   RO          700    .text               c_w.l(_printf_char_file.o)
    0x08004f14   0x08004f14   0x0000002c   Code   RO          702    .text               c_w.l(_printf_wchar.o)
    0x08004f40   0x08004f40   0x00000040   Code   RO          706    .text               c_w.l(_wcrtomb.o)
    0x08004f80   0x08004f80   0x00000008   Code   RO          799    .text               c_w.l(libspace.o)
    0x08004f88   0x08004f88   0x00000008   Code   RO          820    .text               c_w.l(rt_locale_intlibspace.o)
    0x08004f90   0x08004f90   0x00000008   Code   RO          825    .text               c_w.l(rt_errno_addr_intlibspace.o)
    0x08004f98   0x08004f98   0x00000034   Code   RO          831    .text               c_w.l(h1_extend.o)
    0x08004fcc   0x08004fcc   0x0000008a   Code   RO          835    .text               c_w.l(lludiv10.o)
    0x08005056   0x08005056   0x00000002   PAD
    0x08005058   0x08005058   0x00000080   Code   RO          837    .text               c_w.l(_printf_fp_infnan.o)
    0x080050d8   0x080050d8   0x000000e4   Code   RO          841    .text               c_w.l(bigflt0.o)
    0x080051bc   0x080051bc   0x00000008   Code   RO          866    .text               c_w.l(ferror.o)
    0x080051c4   0x080051c4   0x0000000e   Code   RO          876    .text               c_w.l(defsig_rtmem_outer.o)
    0x080051d2   0x080051d2   0x0000004a   Code   RO          894    .text               c_w.l(sys_stackheap_outer.o)
    0x0800521c   0x0800521c   0x00000012   Code   RO          900    .text               c_w.l(exit.o)
    0x0800522e   0x0800522e   0x0000000a   Code   RO          902    .text               c_w.l(defsig_exit.o)
    0x08005238   0x08005238   0x00000050   Code   RO          904    .text               c_w.l(defsig_rtmem_inner.o)
    0x08005288   0x08005288   0x00000080   Code   RO          906    .text               c_w.l(strcmpv7m.o)
    0x08005308   0x08005308   0x00000032   Code   RO          928    .text               c_w.l(defsig_general.o)
    0x0800533a   0x0800533a   0x0000003e   Code   RO          844    CL$$btod_d2e        c_w.l(btod.o)
    0x08005378   0x08005378   0x00000046   Code   RO          846    CL$$btod_d2e_denorm_low  c_w.l(btod.o)
    0x080053be   0x080053be   0x00000060   Code   RO          845    CL$$btod_d2e_norm_op1  c_w.l(btod.o)
    0x0800541e   0x0800541e   0x00000338   Code   RO          854    CL$$btod_div_common  c_w.l(btod.o)
    0x08005756   0x08005756   0x000000dc   Code   RO          851    CL$$btod_e2e        c_w.l(btod.o)
    0x08005832   0x08005832   0x0000002a   Code   RO          848    CL$$btod_ediv       c_w.l(btod.o)
    0x0800585c   0x0800585c   0x0000002a   Code   RO          847    CL$$btod_emul       c_w.l(btod.o)
    0x08005886   0x08005886   0x00000244   Code   RO          853    CL$$btod_mult_common  c_w.l(btod.o)
    0x08005aca   0x08005aca   0x00000028   Code   RO          784    i.__ARM_fpclassify  m_ws.l(fpclassify.o)
    0x08005af2   0x08005af2   0x000000aa   Code   RO          786    i.__kernel_poly     m_ws.l(poly.o)
    0x08005b9c   0x08005b9c   0x00000010   Code   RO          770    i.__mathlib_dbl_divzero  m_ws.l(dunder.o)
    0x08005bac   0x08005bac   0x00000004   Code   RO          772    i.__mathlib_dbl_infnan2  m_ws.l(dunder.o)
    0x08005bb0   0x08005bb0   0x0000000c   Code   RO          773    i.__mathlib_dbl_invalid  m_ws.l(dunder.o)
    0x08005bbc   0x08005bbc   0x0000000e   Code   RO          774    i.__mathlib_dbl_overflow  m_ws.l(dunder.o)
    0x08005bca   0x08005bca   0x00000002   PAD
    0x08005bcc   0x08005bcc   0x00000010   Code   RO          776    i.__mathlib_dbl_underflow  m_ws.l(dunder.o)
    0x08005bdc   0x08005bdc   0x0000000e   Code   RO          571    i._is_digit         c_w.l(__printf_wp.o)
    0x08005bea   0x08005bea   0x00000002   PAD
    0x08005bec   0x08005bec   0x000000dc   Code   RO          643    i.floor             m_ws.l(floor.o)
    0x08005cc8   0x08005cc8   0x000009d8   Code   RO          647    i.pow               m_ws.l(pow.o)
    0x080066a0   0x080066a0   0x0000004c   Code   RO          790    i.sqrt              m_ws.l(sqrt.o)
    0x080066ec   0x080066ec   0x0000002c   Code   RO          871    locale$$code        c_w.l(lc_ctype_c.o)
    0x08006718   0x08006718   0x0000002c   Code   RO          874    locale$$code        c_w.l(lc_numeric_c.o)
    0x08006744   0x08006744   0x00000150   Code   RO          614    x$fpl$dadd          fz_ws.l(daddsub_clz.o)
    0x08006894   0x08006894   0x00000010   Code   RO          885    x$fpl$dcheck1       fz_ws.l(dcheck1.o)
    0x080068a4   0x080068a4   0x00000018   Code   RO          755    x$fpl$dcmpinf       fz_ws.l(dcmpi.o)
    0x080068bc   0x080068bc   0x000002b0   Code   RO          758    x$fpl$ddiv          fz_ws.l(ddiv.o)
    0x08006b6c   0x08006b6c   0x00000078   Code   RO          620    x$fpl$deqf          fz_ws.l(deqf.o)
    0x08006be4   0x08006be4   0x0000005e   Code   RO          622    x$fpl$dfix          fz_ws.l(dfix.o)
    0x08006c42   0x08006c42   0x0000002e   Code   RO          627    x$fpl$dflt          fz_ws.l(dflt_clz.o)
    0x08006c70   0x08006c70   0x00000078   Code   RO          632    x$fpl$dleqf         fz_ws.l(dleqf.o)
    0x08006ce8   0x08006ce8   0x00000154   Code   RO          634    x$fpl$dmul          fz_ws.l(dmul.o)
    0x08006e3c   0x08006e3c   0x0000009c   Code   RO          761    x$fpl$dnaninf       fz_ws.l(dnaninf.o)
    0x08006ed8   0x08006ed8   0x0000000c   Code   RO          763    x$fpl$dretinf       fz_ws.l(dretinf.o)
    0x08006ee4   0x08006ee4   0x0000006c   Code   RO          636    x$fpl$drleqf        fz_ws.l(drleqf.o)
    0x08006f50   0x08006f50   0x00000016   Code   RO          615    x$fpl$drsb          fz_ws.l(daddsub_clz.o)
    0x08006f66   0x08006f66   0x00000002   PAD
    0x08006f68   0x08006f68   0x000001cc   Code   RO          887    x$fpl$dsqrt         fz_ws.l(dsqrt_noumaal.o)
    0x08007134   0x08007134   0x000001d4   Code   RO          616    x$fpl$dsub          fz_ws.l(daddsub_clz.o)
    0x08007308   0x08007308   0x00000056   Code   RO          638    x$fpl$f2d           fz_ws.l(f2d.o)
    0x0800735e   0x0800735e   0x0000008c   Code   RO          765    x$fpl$fnaninf       fz_ws.l(fnaninf.o)
    0x080073ea   0x080073ea   0x00000004   Code   RO          640    x$fpl$printf1       fz_ws.l(printf1.o)
    0x080073ee   0x080073ee   0x00000064   Code   RO          918    x$fpl$retnan        fz_ws.l(retnan.o)
    0x08007452   0x08007452   0x0000005c   Code   RO          767    x$fpl$scalbn        fz_ws.l(scalbn.o)
    0x080074ae   0x080074ae   0x00000030   Code   RO          948    x$fpl$trapveneer    fz_ws.l(trapv.o)
    0x080074de   0x080074de   0x00000000   Code   RO          769    x$fpl$usenofp       fz_ws.l(usenofp.o)
    0x080074de   0x080074de   0x00000007   Data   RO          214    .constdata          cjson.o
    0x080074e5   0x080074e5   0x00000003   PAD
    0x080074e8   0x080074e8   0x00000008   Data   RO          532    .constdata          c_w.l(_printf_wctomb.o)
    0x080074f0   0x080074f0   0x00000028   Data   RO          544    .constdata          c_w.l(_printf_hex_int.o)
    0x08007518   0x08007518   0x00000011   Data   RO          579    .constdata          c_w.l(__printf_flags_ss_wp.o)
    0x08007529   0x08007529   0x00000007   PAD
    0x08007530   0x08007530   0x00000088   Data   RO          648    .constdata          m_ws.l(pow.o)
    0x080075b8   0x080075b8   0x00000008   Data   RO          788    .constdata          m_ws.l(qnan.o)
    0x080075c0   0x080075c0   0x00000094   Data   RO          842    .constdata          c_w.l(bigflt0.o)
    0x08007654   0x08007654   0x0000009d   Data   RO          176    .conststring        ec800.o
    0x080076f1   0x080076f1   0x00000003   PAD
    0x080076f4   0x080076f4   0x00000020   Data   RO          978    Region$$Table       anon$$obj.o
    0x08007714   0x08007714   0x00000110   Data   RO          870    locale$$data        c_w.l(lc_ctype_c.o)
    0x08007824   0x08007824   0x0000001c   Data   RO          873    locale$$data        c_w.l(lc_numeric_c.o)


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x08007840, Size: 0x000007b0, Max: 0x00005000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x08007840   0x00000014   Data   RW          130    .data               system_stm32f10x.o
    0x20000014   0x08007854   0x00000010   Data   RW          177    .data               ec800.o
    0x20000024   0x08007864   0x0000000c   Data   RW          215    .data               cjson.o
    0x20000030   0x08007870   0x00000001   Data   RW          273    .data               aht20.o
    0x20000031   0x08007871   0x00000001   PAD
    0x20000032   0x08007872   0x00000002   Data   RW          291    .data               bsp_i2c.o
    0x20000034   0x08007874   0x00000014   Data   RW          344    .data               stm32f10x_rcc.o
    0x20000048   0x08007888   0x00000008   Data   RW          401    .data               usart.o
    0x20000050   0x08007890   0x00000004   Data   RW          420    .data               delay.o
    0x20000054        -       0x000000fa   Zero   RW          400    .bss                usart.o
    0x2000014e   0x08007894   0x00000002   PAD
    0x20000150        -       0x00000060   Zero   RW          800    .bss                c_w.l(libspace.o)
    0x200001b0        -       0x00000200   Zero   RW          393    HEAP                startup_stm32f10x_hd.o
    0x200003b0        -       0x00000400   Zero   RW          392    STACK               startup_stm32f10x_hd.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       320          4          0          1          0       2239   aht20.o
      1032         14          0          2          0       5272   bsp_i2c.o
      6784        208          7         12          0      24948   cjson.o
         0          0          0          0          0         32   core_cm3.o
       212         18          0          4          0       1171   delay.o
      1624        586        157         16          0       5968   ec800.o
       104         14          0          0          0        575   led.o
       284         44          0          0          0     239803   main.o
       220         22          0          0          0       1933   misc.o
        64         26        304          0       1536        784   startup_stm32f10x_hd.o
      1468         34          0          0          0       8342   stm32f10x_flash.o
       860         38          0          0          0       5865   stm32f10x_gpio.o
        26          0          0          0          0       1246   stm32f10x_it.o
        64          6          0          0          0       1345   stm32f10x_iwdg.o
       932         36          0         20          0       9112   stm32f10x_rcc.o
      1032         22          0          0          0       8592   stm32f10x_usart.o
        12          0          0          0          0        517   sys.o
       588         36          0         20          0       2189   system_stm32f10x.o
       436         22          0          8        250       4034   usart.o
        44          0          0          0          0        726   wdg.o

    ----------------------------------------------------------------------
     16108       <USER>        <GROUP>         84       1788     324693   Object Totals
         0          0         32          0          0          0   (incl. Generated)
         2          0          6          1          2          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

         8          0          0          0          0         68   __main.o
       392          4         17          0          0         92   __printf_flags_ss_wp.o
        14          0          0          0          0         68   __printf_wp.o
         0          0          0          0          0          0   __rtentry.o
        12          0          0          0          0          0   __rtentry2.o
         6          0          0          0          0          0   __rtentry4.o
        52          8          0          0          0          0   __scatter.o
        26          0          0          0          0          0   __scatter_copy.o
        28          0          0          0          0          0   __scatter_zi.o
         6          0          0          0          0          0   _printf_c.o
        44          0          0          0          0        108   _printf_char.o
        48          6          0          0          0         96   _printf_char_common.o
        36          4          0          0          0         80   _printf_char_file.o
         6          0          0          0          0          0   _printf_d.o
       120         16          0          0          0         92   _printf_dec.o
         6          0          0          0          0          0   _printf_e.o
         6          0          0          0          0          0   _printf_f.o
      1054          0          0          0          0        216   _printf_fp_dec.o
       128         16          0          0          0         84   _printf_fp_infnan.o
        88          4         40          0          0         88   _printf_hex_int.o
         6          0          0          0          0          0   _printf_i.o
       178          0          0          0          0         88   _printf_intcommon.o
        10          0          0          0          0          0   _printf_l.o
         6          0          0          0          0          0   _printf_lc.o
         6          0          0          0          0          0   _printf_o.o
        80          8          0          0          0         88   _printf_oct_int.o
        78          0          0          0          0        108   _printf_pad.o
         0          0          0          0          0          0   _printf_percent.o
         4          0          0          0          0          0   _printf_percent_end.o
         6          0          0          0          0          0   _printf_s.o
        82          0          0          0          0         80   _printf_str.o
        36          0          0          0          0         84   _printf_truncate.o
         6          0          0          0          0          0   _printf_u.o
        44          0          0          0          0        108   _printf_wchar.o
       188          6          8          0          0         92   _printf_wctomb.o
         6          0          0          0          0          0   _printf_x.o
        22          0          0          0          0        100   _rserrno.o
        10          0          0          0          0         68   _sputc.o
        64          0          0          0          0         92   _wcrtomb.o
       228          4        148          0          0         96   bigflt0.o
      1936        128          0          0          0        672   btod.o
        10          0          0          0          0         68   defsig_exit.o
        50          0          0          0          0         88   defsig_general.o
        80         58          0          0          0         76   defsig_rtmem_inner.o
        14          0          0          0          0         80   defsig_rtmem_outer.o
        18          0          0          0          0         80   exit.o
         8          0          0          0          0         68   ferror.o
        94          0          0          0          0         80   h1_alloc.o
        52          0          0          0          0         68   h1_extend.o
        78          0          0          0          0         80   h1_free.o
        14          0          0          0          0         84   h1_init.o
         6          0          0          0          0        152   heapauxi.o
         4          0          0          0          0        136   hguard.o
       138          0          0          0          0        168   init_alloc.o
        44         10        272          0          0         76   lc_ctype_c.o
        44         10         28          0          0         76   lc_numeric_c.o
         2          0          0          0          0          0   libinit.o
        38          0          0          0          0          0   libinit2.o
         2          0          0          0          0          0   libshutdown.o
         2          0          0          0          0          0   libshutdown2.o
         8          4          0          0         96         68   libspace.o
       138          0          0          0          0         80   lludiv10.o
         0          0          0          0          0          0   maybetermalloc1.o
        24          4          0          0          0         84   noretval__2printf.o
        40          6          0          0          0         84   noretval__2sprintf.o
        16          4          0          0          0         76   rt_ctype_table.o
         8          4          0          0          0         68   rt_errno_addr_intlibspace.o
         8          4          0          0          0         68   rt_heap_descriptor_intlibspace.o
         8          4          0          0          0         68   rt_locale_intlibspace.o
        78          0          0          0          0         80   rt_memclr_w.o
       138          0          0          0          0         68   rt_memcpy_v6.o
       100          0          0          0          0         80   rt_memcpy_w.o
         2          0          0          0          0          0   rtexit.o
        10          0          0          0          0          0   rtexit2.o
        20          0          0          0          0         68   strchr.o
       128          0          0          0          0         68   strcmpv7m.o
        72          0          0          0          0         80   strcpy.o
        62          0          0          0          0         76   strlen.o
       150          0          0          0          0         80   strncmp.o
        36          0          0          0          0         80   strstr.o
        74          0          0          0          0         80   sys_stackheap_outer.o
        26          0          0          0          0         76   tolower.o
         2          0          0          0          0         68   use_no_semi.o
         2          0          0          0          0         68   use_no_semi_2.o
       826         16          0          0          0        348   daddsub_clz.o
        16          4          0          0          0         68   dcheck1.o
        24          0          0          0          0         68   dcmpi.o
       688        140          0          0          0        208   ddiv.o
       120          4          0          0          0         92   deqf.o
        94          4          0          0          0         92   dfix.o
        46          0          0          0          0         68   dflt_clz.o
       120          4          0          0          0         92   dleqf.o
       340         12          0          0          0        104   dmul.o
       156          4          0          0          0         92   dnaninf.o
        12          0          0          0          0         68   dretinf.o
       108          0          0          0          0         80   drleqf.o
       460         56          0          0          0        120   dsqrt_noumaal.o
        86          4          0          0          0         84   f2d.o
       140          4          0          0          0         84   fnaninf.o
         4          0          0          0          0         68   printf1.o
       100          0          0          0          0         68   retnan.o
        92          0          0          0          0         68   scalbn.o
        48          0          0          0          0         68   trapv.o
         0          0          0          0          0          0   usenofp.o
        62         10          0          0          0        340   dunder.o
       220         16          0          0          0         84   floor.o
        40          0          0          0          0         68   fpclassify.o
       170          0          0          0          0         96   poly.o
      2520        190        136          0          0        324   pow.o
         0          0          8          0          0          0   qnan.o
        76          0          0          0          0         84   sqrt.o

    ----------------------------------------------------------------------
     13506        <USER>        <GROUP>          0         96       8552   Library Totals
        14          0          7          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      6924        312        513          0         96       5616   c_w.l
      3480        252          0          0          0       1940   fz_ws.l
      3088        216        144          0          0        996   m_ws.l

    ----------------------------------------------------------------------
     13506        <USER>        <GROUP>          0         96       8552   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     29614       1910       1170         84       1884     326741   Grand Totals
     29614       1910       1170         84       1884     326741   ELF Image Totals
     29614       1910       1170         84          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                30784 (  30.06kB)
    Total RW  Size (RW Data + ZI Data)              1968 (   1.92kB)
    Total ROM Size (Code + RO Data + RW Data)      30868 (  30.14kB)

==============================================================================

