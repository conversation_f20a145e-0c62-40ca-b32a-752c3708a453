/**
 * 数据相关接口
 */
import request from '../utils/request';

/**
 * 获取设备实时数据
 * @param {String} deviceId 设备ID
 * @returns {Promise} Promise对象
 */
export function getRealtimeData(deviceId) {
  return request.get('/api/data/realtime', { deviceId });
}

/**
 * 获取设备历史数据
 * @param {Object} params 查询参数
 * @returns {Promise} Promise对象
 */
export function getHistoryData(params) {
  return request.get('/api/data/history', params);
}

/**
 * 获取能耗统计
 * @param {String} period 统计周期
 * @returns {Promise} Promise对象
 */
export function getEnergyStats(period) {
  return request.get('/api/data/energyStats', { period });
} 

/**
 * 获取设备历史数据点
 * @param {Object} params 包含product_id, device_name等参数
 * @param {String} token 授权token
 * @returns {Promise} Promise对象
 */
export function getHistoryDatapoints(params, token) {
  return new Promise((resolve, reject) => {
    uni.request({
      url: 'https://iot-api.heclouds.com/datapoint/history-datapoints',
      method: 'GET',
      data: params,
      header: {
        'authorization': token
      },
      success: (res) => {
        if (res.data && res.data.code === 0) {
          resolve(res.data);
        } else {
          reject(res.data || { msg: '获取历史数据失败' });
        }
      },
      fail: (err) => {
        reject(err);
      }
    });
  });
} 